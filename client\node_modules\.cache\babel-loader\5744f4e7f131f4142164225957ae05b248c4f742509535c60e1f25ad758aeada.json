{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Ranking\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { getAllReportsForRanking } from \"../../../apicalls/reports\";\nimport { getUserInfo } from \"../../../apicalls/users\";\nimport { message } from \"antd\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport UserRankingList from \"../../../components/modern/UserRankingList\";\nimport { TbTrophy, TbUsers, TbSchool } from \"react-icons/tb\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Ranking = () => {\n  _s();\n  const [rankingData, setRankingData] = useState([]);\n  const [userData, setUserData] = useState(null);\n  const [isAdmin, setIsAdmin] = useState(false);\n  const [activeTab, setActiveTab] = useState(\"overall\"); // \"overall\" or \"class\"\n  const [loading, setLoading] = useState(true);\n  const dispatch = useDispatch();\n\n  // Helper function to determine subscription status\n  const getSubscriptionStatus = user => {\n    if (!user.paymentRequired) {\n      return 'free';\n    }\n    if (user.subscription && user.subscription.status === 'active') {\n      if (user.subscription.paymentStatus === 'paid') {\n        const endDate = new Date(user.subscription.endDate);\n        const now = new Date();\n        if (endDate > now) {\n          return 'active';\n        }\n      }\n    }\n    return 'expired';\n  };\n  const fetchReports = async () => {\n    try {\n      const response = await getAllReportsForRanking();\n      if (response.success) {\n        // Transform data for our new components\n        const transformedData = response.data.map((item, index) => ({\n          userId: item.user._id,\n          _id: item.user._id,\n          name: item.user.name,\n          profilePicture: item.user.profilePicture || null,\n          subscriptionStatus: getSubscriptionStatus(item.user),\n          totalPoints: item.totalPoints || 0,\n          passedExamsCount: item.passedExamsCount || 0,\n          quizzesTaken: item.quizzesTaken || 0,\n          score: item.totalPoints || 0,\n          rank: index + 1,\n          userSchool: item.user.userSchool || 'Not Enrolled',\n          userClass: item.user.userClass || 'Not Enrolled'\n        }));\n        setRankingData(transformedData);\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getUserData = async () => {\n    try {\n      const response = await getUserInfo();\n      if (response.success) {\n        if (response.data.isAdmin) {\n          setIsAdmin(true);\n        } else {\n          setIsAdmin(false);\n          setUserData(response.data);\n          await fetchReports();\n          dispatch(HideLoading());\n        }\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  useEffect(() => {\n    if (localStorage.getItem(\"token\")) {\n      dispatch(ShowLoading());\n      getUserData();\n    }\n  }, []);\n\n  // Helper function to format user ID for mobile devices\n  const formatMobileUserId = userId => {\n    const prefix = userId.slice(0, 4);\n    const suffix = userId.slice(-4);\n    return `${prefix}.....${suffix}`;\n  };\n  if (isAdmin) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(TbUsers, {\n          className: \"w-16 h-16 text-gray-400 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900 mb-2\",\n          children: \"Admin Access\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Ranking is only available for students\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 13\n    }, this);\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Loading ranking data...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full mb-4\",\n          children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n            className: \"w-8 h-8 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-bold text-gray-900 mb-4\",\n          children: \"Student Leaderboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-gray-600\",\n          children: \"See how you rank against other students\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.2\n        },\n        className: \"mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl p-2 shadow-sm border border-gray-200 max-w-md mx-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(motion.button, {\n              whileHover: {\n                scale: 1.02\n              },\n              whileTap: {\n                scale: 0.98\n              },\n              className: `flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 flex-1 justify-center ${activeTab === \"overall\" ? 'bg-blue-600 text-white shadow-md' : 'text-gray-600 hover:bg-gray-100'}`,\n              onClick: () => setActiveTab(\"overall\"),\n              children: [/*#__PURE__*/_jsxDEV(TbUsers, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Overall\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n              whileHover: {\n                scale: 1.02\n              },\n              whileTap: {\n                scale: 0.98\n              },\n              className: `flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 flex-1 justify-center ${activeTab === \"class\" ? 'bg-blue-600 text-white shadow-md' : 'text-gray-600 hover:bg-gray-100'}`,\n              onClick: () => setActiveTab(\"class\"),\n              children: [/*#__PURE__*/_jsxDEV(TbSchool, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Class\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.3\n        },\n        children: /*#__PURE__*/_jsxDEV(UserRankingList, {\n          users: rankingData,\n          currentUserId: userData === null || userData === void 0 ? void 0 : userData._id,\n          layout: \"horizontal\",\n          size: \"medium\",\n          showSearch: true,\n          showFilters: true,\n          showStats: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 128,\n    columnNumber: 9\n  }, this);\n};\n_s(Ranking, \"CQ5NosNnAJcL4CGyxydCkouMRgE=\", false, function () {\n  return [useDispatch];\n});\n_c = Ranking;\nexport default Ranking;\nvar _c;\n$RefreshReg$(_c, \"Ranking\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "motion", "getAllReportsForRanking", "getUserInfo", "message", "useDispatch", "useSelector", "HideLoading", "ShowLoading", "UserRankingList", "TbTrophy", "TbUsers", "TbSchool", "jsxDEV", "_jsxDEV", "Ranking", "_s", "rankingData", "setRankingData", "userData", "setUserData", "isAdmin", "setIsAdmin", "activeTab", "setActiveTab", "loading", "setLoading", "dispatch", "getSubscriptionStatus", "user", "paymentRequired", "subscription", "status", "paymentStatus", "endDate", "Date", "now", "fetchReports", "response", "success", "transformedData", "data", "map", "item", "index", "userId", "_id", "name", "profilePicture", "subscriptionStatus", "totalPoints", "passedExamsCount", "quizzesTaken", "score", "rank", "userSchool", "userClass", "error", "getUserData", "localStorage", "getItem", "formatMobileUserId", "prefix", "slice", "suffix", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "y", "animate", "transition", "delay", "button", "whileHover", "scale", "whileTap", "onClick", "users", "currentUserId", "layout", "size", "showSearch", "showFilters", "showStats", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Ranking/index.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { getAllReportsForRanking } from \"../../../apicalls/reports\";\r\nimport { getUserInfo } from \"../../../apicalls/users\";\r\nimport { message } from \"antd\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport UserRankingList from \"../../../components/modern/UserRankingList\";\r\nimport { TbTrophy, TbUsers, TbSchool } from \"react-icons/tb\";\r\n\r\nconst Ranking = () => {\r\n    const [rankingData, setRankingData] = useState([]);\r\n    const [userData, setUserData] = useState(null);\r\n    const [isAdmin, setIsAdmin] = useState(false);\r\n    const [activeTab, setActiveTab] = useState(\"overall\"); // \"overall\" or \"class\"\r\n    const [loading, setLoading] = useState(true);\r\n\r\n    const dispatch = useDispatch();\r\n\r\n    // Helper function to determine subscription status\r\n    const getSubscriptionStatus = (user) => {\r\n        if (!user.paymentRequired) {\r\n            return 'free';\r\n        }\r\n\r\n        if (user.subscription && user.subscription.status === 'active') {\r\n            if (user.subscription.paymentStatus === 'paid') {\r\n                const endDate = new Date(user.subscription.endDate);\r\n                const now = new Date();\r\n                if (endDate > now) {\r\n                    return 'active';\r\n                }\r\n            }\r\n        }\r\n\r\n        return 'expired';\r\n    };\r\n\r\n    const fetchReports = async () => {\r\n        try {\r\n            const response = await getAllReportsForRanking();\r\n            if (response.success) {\r\n                // Transform data for our new components\r\n                const transformedData = response.data.map((item, index) => ({\r\n                    userId: item.user._id,\r\n                    _id: item.user._id,\r\n                    name: item.user.name,\r\n                    profilePicture: item.user.profilePicture || null,\r\n                    subscriptionStatus: getSubscriptionStatus(item.user),\r\n                    totalPoints: item.totalPoints || 0,\r\n                    passedExamsCount: item.passedExamsCount || 0,\r\n                    quizzesTaken: item.quizzesTaken || 0,\r\n                    score: item.totalPoints || 0,\r\n                    rank: index + 1,\r\n                    userSchool: item.user.userSchool || 'Not Enrolled',\r\n                    userClass: item.user.userClass || 'Not Enrolled'\r\n                }));\r\n                setRankingData(transformedData);\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    }\r\n\r\n\r\n    const getUserData = async () => {\r\n        try {\r\n            const response = await getUserInfo();\r\n            if (response.success) {\r\n                if (response.data.isAdmin) {\r\n                    setIsAdmin(true);\r\n                } else {\r\n                    setIsAdmin(false);\r\n                    setUserData(response.data);\r\n                    await fetchReports();\r\n                    dispatch(HideLoading());\r\n                }\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (localStorage.getItem(\"token\")) {\r\n            dispatch(ShowLoading());\r\n            getUserData();\r\n        }\r\n    }, []);\r\n\r\n    // Helper function to format user ID for mobile devices\r\n    const formatMobileUserId = (userId) => {\r\n        const prefix = userId.slice(0, 4);\r\n        const suffix = userId.slice(-4);\r\n        return `${prefix}.....${suffix}`;\r\n    };\r\n\r\n    if (isAdmin) {\r\n        return (\r\n            <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\r\n                <div className=\"text-center\">\r\n                    <TbUsers className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\r\n                    <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">Admin Access</h2>\r\n                    <p className=\"text-gray-600\">Ranking is only available for students</p>\r\n                </div>\r\n            </div>\r\n        );\r\n    }\r\n\r\n    if (loading) {\r\n        return (\r\n            <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\r\n                <div className=\"text-center\">\r\n                    <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\r\n                    <p className=\"text-gray-600\">Loading ranking data...</p>\r\n                </div>\r\n            </div>\r\n        );\r\n    }\r\n\r\n    return (\r\n        <div className=\"min-h-screen bg-gray-50\">\r\n            <div className=\"container mx-auto px-4 py-8\">\r\n                {/* Header */}\r\n                <motion.div\r\n                    initial={{ opacity: 0, y: -20 }}\r\n                    animate={{ opacity: 1, y: 0 }}\r\n                    className=\"text-center mb-8\"\r\n                >\r\n                    <div className=\"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full mb-4\">\r\n                        <TbTrophy className=\"w-8 h-8 text-white\" />\r\n                    </div>\r\n                    <h1 className=\"text-4xl font-bold text-gray-900 mb-4\">Student Leaderboard</h1>\r\n                    <p className=\"text-xl text-gray-600\">\r\n                        See how you rank against other students\r\n                    </p>\r\n                </motion.div>\r\n\r\n                {/* Tabs for Overall vs Class Ranking */}\r\n                <motion.div\r\n                    initial={{ opacity: 0, y: 20 }}\r\n                    animate={{ opacity: 1, y: 0 }}\r\n                    transition={{ delay: 0.2 }}\r\n                    className=\"mb-8\"\r\n                >\r\n                    <div className=\"bg-white rounded-xl p-2 shadow-sm border border-gray-200 max-w-md mx-auto\">\r\n                        <div className=\"flex gap-2\">\r\n                            <motion.button\r\n                                whileHover={{ scale: 1.02 }}\r\n                                whileTap={{ scale: 0.98 }}\r\n                                className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 flex-1 justify-center ${\r\n                                    activeTab === \"overall\"\r\n                                        ? 'bg-blue-600 text-white shadow-md'\r\n                                        : 'text-gray-600 hover:bg-gray-100'\r\n                                }`}\r\n                                onClick={() => setActiveTab(\"overall\")}\r\n                            >\r\n                                <TbUsers className=\"w-5 h-5\" />\r\n                                <span>Overall</span>\r\n                            </motion.button>\r\n                            <motion.button\r\n                                whileHover={{ scale: 1.02 }}\r\n                                whileTap={{ scale: 0.98 }}\r\n                                className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 flex-1 justify-center ${\r\n                                    activeTab === \"class\"\r\n                                        ? 'bg-blue-600 text-white shadow-md'\r\n                                        : 'text-gray-600 hover:bg-gray-100'\r\n                                }`}\r\n                                onClick={() => setActiveTab(\"class\")}\r\n                            >\r\n                                <TbSchool className=\"w-5 h-5\" />\r\n                                <span>Class</span>\r\n                            </motion.button>\r\n                        </div>\r\n                    </div>\r\n                </motion.div>\r\n\r\n                {/* Modern Ranking List */}\r\n                <motion.div\r\n                    initial={{ opacity: 0, y: 20 }}\r\n                    animate={{ opacity: 1, y: 0 }}\r\n                    transition={{ delay: 0.3 }}\r\n                >\r\n                    <UserRankingList\r\n                        users={rankingData}\r\n                        currentUserId={userData?._id}\r\n                        layout=\"horizontal\"\r\n                        size=\"medium\"\r\n                        showSearch={true}\r\n                        showFilters={true}\r\n                        showStats={true}\r\n                    />\r\n                </motion.div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Ranking;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,uBAAuB,QAAQ,2BAA2B;AACnE,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,OAAOC,eAAe,MAAM,4CAA4C;AACxE,SAASC,QAAQ,EAAEC,OAAO,EAAEC,QAAQ,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7D,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuB,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;EACvD,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAE5C,MAAM2B,QAAQ,GAAGtB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMuB,qBAAqB,GAAIC,IAAI,IAAK;IACpC,IAAI,CAACA,IAAI,CAACC,eAAe,EAAE;MACvB,OAAO,MAAM;IACjB;IAEA,IAAID,IAAI,CAACE,YAAY,IAAIF,IAAI,CAACE,YAAY,CAACC,MAAM,KAAK,QAAQ,EAAE;MAC5D,IAAIH,IAAI,CAACE,YAAY,CAACE,aAAa,KAAK,MAAM,EAAE;QAC5C,MAAMC,OAAO,GAAG,IAAIC,IAAI,CAACN,IAAI,CAACE,YAAY,CAACG,OAAO,CAAC;QACnD,MAAME,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;QACtB,IAAID,OAAO,GAAGE,GAAG,EAAE;UACf,OAAO,QAAQ;QACnB;MACJ;IACJ;IAEA,OAAO,SAAS;EACpB,CAAC;EAED,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMpC,uBAAuB,CAAC,CAAC;MAChD,IAAIoC,QAAQ,CAACC,OAAO,EAAE;QAClB;QACA,MAAMC,eAAe,GAAGF,QAAQ,CAACG,IAAI,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,MAAM;UACxDC,MAAM,EAAEF,IAAI,CAACd,IAAI,CAACiB,GAAG;UACrBA,GAAG,EAAEH,IAAI,CAACd,IAAI,CAACiB,GAAG;UAClBC,IAAI,EAAEJ,IAAI,CAACd,IAAI,CAACkB,IAAI;UACpBC,cAAc,EAAEL,IAAI,CAACd,IAAI,CAACmB,cAAc,IAAI,IAAI;UAChDC,kBAAkB,EAAErB,qBAAqB,CAACe,IAAI,CAACd,IAAI,CAAC;UACpDqB,WAAW,EAAEP,IAAI,CAACO,WAAW,IAAI,CAAC;UAClCC,gBAAgB,EAAER,IAAI,CAACQ,gBAAgB,IAAI,CAAC;UAC5CC,YAAY,EAAET,IAAI,CAACS,YAAY,IAAI,CAAC;UACpCC,KAAK,EAAEV,IAAI,CAACO,WAAW,IAAI,CAAC;UAC5BI,IAAI,EAAEV,KAAK,GAAG,CAAC;UACfW,UAAU,EAAEZ,IAAI,CAACd,IAAI,CAAC0B,UAAU,IAAI,cAAc;UAClDC,SAAS,EAAEb,IAAI,CAACd,IAAI,CAAC2B,SAAS,IAAI;QACtC,CAAC,CAAC,CAAC;QACHtC,cAAc,CAACsB,eAAe,CAAC;MACnC,CAAC,MAAM;QACHpC,OAAO,CAACqD,KAAK,CAACnB,QAAQ,CAAClC,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAOqD,KAAK,EAAE;MACZrD,OAAO,CAACqD,KAAK,CAACA,KAAK,CAACrD,OAAO,CAAC;IAChC,CAAC,SAAS;MACNsB,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAGD,MAAMgC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACA,MAAMpB,QAAQ,GAAG,MAAMnC,WAAW,CAAC,CAAC;MACpC,IAAImC,QAAQ,CAACC,OAAO,EAAE;QAClB,IAAID,QAAQ,CAACG,IAAI,CAACpB,OAAO,EAAE;UACvBC,UAAU,CAAC,IAAI,CAAC;QACpB,CAAC,MAAM;UACHA,UAAU,CAAC,KAAK,CAAC;UACjBF,WAAW,CAACkB,QAAQ,CAACG,IAAI,CAAC;UAC1B,MAAMJ,YAAY,CAAC,CAAC;UACpBV,QAAQ,CAACpB,WAAW,CAAC,CAAC,CAAC;QAC3B;MACJ,CAAC,MAAM;QACHH,OAAO,CAACqD,KAAK,CAACnB,QAAQ,CAAClC,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAOqD,KAAK,EAAE;MACZrD,OAAO,CAACqD,KAAK,CAACA,KAAK,CAACrD,OAAO,CAAC;IAChC;EACJ,CAAC;EAEDL,SAAS,CAAC,MAAM;IACZ,IAAI4D,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE;MAC/BjC,QAAQ,CAACnB,WAAW,CAAC,CAAC,CAAC;MACvBkD,WAAW,CAAC,CAAC;IACjB;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,kBAAkB,GAAIhB,MAAM,IAAK;IACnC,MAAMiB,MAAM,GAAGjB,MAAM,CAACkB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IACjC,MAAMC,MAAM,GAAGnB,MAAM,CAACkB,KAAK,CAAC,CAAC,CAAC,CAAC;IAC/B,OAAQ,GAAED,MAAO,QAAOE,MAAO,EAAC;EACpC,CAAC;EAED,IAAI3C,OAAO,EAAE;IACT,oBACIP,OAAA;MAAKmD,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACrEpD,OAAA;QAAKmD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxBpD,OAAA,CAACH,OAAO;UAACsD,SAAS,EAAC;QAAsC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5DxD,OAAA;UAAImD,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvExD,OAAA;UAAGmD,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAsC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,IAAI7C,OAAO,EAAE;IACT,oBACIX,OAAA;MAAKmD,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACrEpD,OAAA;QAAKmD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxBpD,OAAA;UAAKmD,SAAS,EAAC;QAA6E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnGxD,OAAA;UAAGmD,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,oBACIxD,OAAA;IAAKmD,SAAS,EAAC,yBAAyB;IAAAC,QAAA,eACpCpD,OAAA;MAAKmD,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAExCpD,OAAA,CAACb,MAAM,CAACsE,GAAG;QACPC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BT,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAE5BpD,OAAA;UAAKmD,SAAS,EAAC,oHAAoH;UAAAC,QAAA,eAC/HpD,OAAA,CAACJ,QAAQ;YAACuD,SAAS,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACNxD,OAAA;UAAImD,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9ExD,OAAA;UAAGmD,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAErC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGbxD,OAAA,CAACb,MAAM,CAACsE,GAAG;QACPC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3BZ,SAAS,EAAC,MAAM;QAAAC,QAAA,eAEhBpD,OAAA;UAAKmD,SAAS,EAAC,2EAA2E;UAAAC,QAAA,eACtFpD,OAAA;YAAKmD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACvBpD,OAAA,CAACb,MAAM,CAAC6E,MAAM;cACVC,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAC1Bf,SAAS,EAAG,kHACR1C,SAAS,KAAK,SAAS,GACjB,kCAAkC,GAClC,iCACT,EAAE;cACH2D,OAAO,EAAEA,CAAA,KAAM1D,YAAY,CAAC,SAAS,CAAE;cAAA0C,QAAA,gBAEvCpD,OAAA,CAACH,OAAO;gBAACsD,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/BxD,OAAA;gBAAAoD,QAAA,EAAM;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAChBxD,OAAA,CAACb,MAAM,CAAC6E,MAAM;cACVC,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAC1Bf,SAAS,EAAG,kHACR1C,SAAS,KAAK,OAAO,GACf,kCAAkC,GAClC,iCACT,EAAE;cACH2D,OAAO,EAAEA,CAAA,KAAM1D,YAAY,CAAC,OAAO,CAAE;cAAA0C,QAAA,gBAErCpD,OAAA,CAACF,QAAQ;gBAACqD,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChCxD,OAAA;gBAAAoD,QAAA,EAAM;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGbxD,OAAA,CAACb,MAAM,CAACsE,GAAG;QACPC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAAX,QAAA,eAE3BpD,OAAA,CAACL,eAAe;UACZ0E,KAAK,EAAElE,WAAY;UACnBmE,aAAa,EAAEjE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE2B,GAAI;UAC7BuC,MAAM,EAAC,YAAY;UACnBC,IAAI,EAAC,QAAQ;UACbC,UAAU,EAAE,IAAK;UACjBC,WAAW,EAAE,IAAK;UAClBC,SAAS,EAAE;QAAK;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACtD,EAAA,CAhMID,OAAO;EAAA,QAOQV,WAAW;AAAA;AAAAqF,EAAA,GAP1B3E,OAAO;AAkMb,eAAeA,OAAO;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}