{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Ranking\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport './index.css';\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { getAllReportsForRanking } from \"../../../apicalls/reports\";\nimport { getUserInfo } from \"../../../apicalls/users\";\nimport { message } from \"antd\";\nimport PageTitle from \"../../../components/PageTitle\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { Card, Button, Loading } from \"../../../components/modern\";\nimport image from '../../../assets/person.png';\nimport { IoPersonCircleOutline } from \"react-icons/io5\";\nimport { TbTrophy, TbMedal, TbCrown, TbUsers, TbSchool, TbStar, TbChartBar, TbUser, TbAward } from \"react-icons/tb\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Ranking = () => {\n  _s();\n  const [rankingData, setRankingData] = useState('');\n  const [userRanking, setUserRanking] = useState('');\n  const [userData, setUserData] = useState('');\n  const [isAdmin, setIsAdmin] = useState(false);\n  const [isMobile, setIsMobile] = useState(false);\n  const [activeTab, setActiveTab] = useState(\"overall\"); // \"overall\" or \"class\"\n\n  const dispatch = useDispatch();\n  const fetchReports = async () => {\n    try {\n      const response = await getAllReportsForRanking();\n      if (response.success) {\n        setRankingData(response.data);\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  const getUserData = async () => {\n    try {\n      const response = await getUserInfo();\n      if (response.success) {\n        if (response.data.isAdmin) {\n          setIsAdmin(true);\n        } else {\n          setIsAdmin(false);\n          setUserData(response.data);\n          await fetchReports();\n          dispatch(HideLoading());\n        }\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  useEffect(() => {\n    if (window.innerWidth < 700) {\n      setIsMobile(true);\n    } else {\n      setIsMobile(false);\n    }\n    if (localStorage.getItem(\"token\")) {\n      dispatch(ShowLoading());\n      getUserData();\n    }\n  }, []);\n  const getUserStats = () => {\n    const Ranking = rankingData.map((user, index) => ({\n      user,\n      ranking: index + 1\n    })).filter(item => item.user.userId.includes(userData._id));\n    setUserRanking(Ranking);\n  };\n  useEffect(() => {\n    if (rankingData) {\n      getUserStats();\n    }\n  }, [rankingData]);\n\n  // Helper function to format user ID for mobile devices\n  const formatMobileUserId = userId => {\n    const prefix = userId.slice(0, 4);\n    const suffix = userId.slice(-4);\n    return `${prefix}.....${suffix}`;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50\",\n    children: !isAdmin && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-modern py-8\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        className: \"text-center mb-6 sm:mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"inline-flex items-center justify-center w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full mb-3 sm:mb-4\",\n          children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n            className: \"w-6 h-6 sm:w-7 sm:h-7 md:w-8 md:h-8 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"heading-2 text-gradient mb-3 sm:mb-4 text-2xl sm:text-3xl md:text-4xl\",\n          children: \"Leaderboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-base sm:text-lg md:text-xl text-gray-600\",\n          children: \"See how you rank against other students\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.2\n        },\n        className: \"mb-6 sm:mb-8\",\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"p-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(motion.button, {\n              whileHover: {\n                scale: 1.02\n              },\n              whileTap: {\n                scale: 0.98\n              },\n              className: `flex items-center space-x-1 sm:space-x-2 px-3 sm:px-6 py-2 sm:py-3 rounded-lg font-medium transition-all duration-200 flex-1 justify-center text-sm sm:text-base ${activeTab === \"overall\" ? 'bg-primary-600 text-white shadow-md' : 'text-gray-600 hover:bg-gray-100'}`,\n              onClick: () => setActiveTab(\"overall\"),\n              children: [/*#__PURE__*/_jsxDEV(TbUsers, {\n                className: \"w-4 h-4 sm:w-5 sm:h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"hidden sm:inline\",\n                children: \"Overall Ranking\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"sm:hidden\",\n                children: \"Overall\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 37\n              }, this), activeTab === \"overall\" && /*#__PURE__*/_jsxDEV(motion.div, {\n                layoutId: \"activeRankingTab\",\n                className: \"w-2 h-2 bg-white rounded-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n              whileHover: {\n                scale: 1.02\n              },\n              whileTap: {\n                scale: 0.98\n              },\n              className: `flex items-center space-x-1 sm:space-x-2 px-3 sm:px-6 py-2 sm:py-3 rounded-lg font-medium transition-all duration-200 flex-1 justify-center text-sm sm:text-base ${activeTab === \"class\" ? 'bg-primary-600 text-white shadow-md' : 'text-gray-600 hover:bg-gray-100'}`,\n              onClick: () => setActiveTab(\"class\"),\n              children: [/*#__PURE__*/_jsxDEV(TbSchool, {\n                className: \"w-4 h-4 sm:w-5 sm:h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"hidden sm:inline\",\n                children: \"Class Ranking\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"sm:hidden\",\n                children: \"Class\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 37\n              }, this), activeTab === \"class\" && /*#__PURE__*/_jsxDEV(motion.div, {\n                layoutId: \"activeRankingTab\",\n                className: \"w-2 h-2 bg-white rounded-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.4\n        },\n        children: rankingData.length > 0 ? /*#__PURE__*/_jsxDEV(Card, {\n          className: \"overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 text-white p-4 sm:p-6 relative overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 opacity-10\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute top-2 left-2 sm:top-4 sm:left-4 text-3xl sm:text-4xl md:text-6xl\",\n                children: \"\\uD83C\\uDFC6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute top-4 right-4 sm:top-8 sm:right-8 text-2xl sm:text-3xl md:text-4xl\",\n                children: \"\\u2B50\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute bottom-2 left-1/4 sm:bottom-4 text-xl sm:text-2xl md:text-3xl\",\n                children: \"\\uD83C\\uDFAF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute bottom-3 right-1/3 sm:bottom-6 text-3xl sm:text-4xl md:text-5xl\",\n                children: \"\\uD83D\\uDC8E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative z-10\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col sm:flex-row items-center justify-center space-y-2 sm:space-y-0 sm:space-x-3 mb-3 sm:mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n                  className: \"w-8 h-8 sm:w-10 sm:h-10 text-yellow-300 animate-bounce\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-xl sm:text-2xl md:text-3xl font-black bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent text-center\",\n                  children: activeTab === \"overall\" ? \"Global Leaderboard\" : \"Class Champions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(TbTrophy, {\n                  className: \"w-8 h-8 sm:w-10 sm:h-10 text-yellow-300 animate-bounce hidden sm:block\",\n                  style: {\n                    animationDelay: '0.5s'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-center text-blue-100 text-sm sm:text-base md:text-lg font-semibold\",\n                children: activeTab === \"overall\" ? \"🌟 Elite performers from all classes competing for glory! 🌟\" : `🎓 Class ${(userData === null || userData === void 0 ? void 0 : userData.class) || 'your class'} top achievers! 🎓`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center mt-3\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"bg-white/20 px-3 py-2 sm:px-4 rounded-full text-xs sm:text-sm font-bold block sm:inline\",\n                  children: \"\\uD83D\\uDCAA Earn points by acing quizzes \\u2022 \\uD83C\\uDFC5 Climb the ranks \\u2022 \\uD83D\\uDC51 Become a champion!\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 45\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-br from-blue-50 to-purple-50 p-3 sm:p-6 border-b\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg sm:text-xl font-bold text-center mb-4 sm:mb-6 text-gray-800\",\n              children: \"\\uD83C\\uDFC6 Champions Podium \\uD83C\\uDFC6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-center items-end space-x-2 sm:space-x-4 overflow-x-auto pb-2\",\n              children: (activeTab === \"overall\" ? rankingData.slice(0, 3) : rankingData.filter(user => user.userClass === (userData === null || userData === void 0 ? void 0 : userData.class)).slice(0, 3)).map((user, index) => {\n                const positions = [1, 0, 2]; // Silver, Diamond, Bronze order for visual appeal\n                const actualPosition = positions.indexOf(index);\n                const heights = ['h-16 sm:h-20 md:h-24', 'h-20 sm:h-24 md:h-32', 'h-12 sm:h-16 md:h-20']; // Responsive heights for podium effect\n                const badges = [{\n                  icon: \"🥈\",\n                  color: \"from-gray-400 to-gray-600\",\n                  title: \"Silver\"\n                }, {\n                  icon: \"💎\",\n                  color: \"from-blue-400 to-cyan-600\",\n                  title: \"Diamond\"\n                }, {\n                  icon: \"🥉\",\n                  color: \"from-amber-400 to-orange-600\",\n                  title: \"Bronze\"\n                }];\n                return /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 50\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: index * 0.2\n                  },\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-2 sm:mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-10 h-10 sm:w-12 sm:h-12 md:w-14 md:h-14 mx-auto rounded-full overflow-hidden border-2 sm:border-3 border-white shadow-lg\",\n                      children: user.userPhoto ? /*#__PURE__*/_jsxDEV(\"img\", {\n                        src: user.userPhoto,\n                        alt: \"profile\",\n                        className: \"w-full h-full object-cover\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 247,\n                        columnNumber: 65\n                      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-full h-full bg-gray-200 flex items-center justify-center\",\n                        children: /*#__PURE__*/_jsxDEV(TbUser, {\n                          className: \"w-3 h-3 sm:w-4 sm:h-4 md:w-5 md:h-5 text-gray-400\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 250,\n                          columnNumber: 69\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 249,\n                        columnNumber: 65\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 245,\n                      columnNumber: 57\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-lg sm:text-xl md:text-2xl mt-1 sm:mt-2\",\n                      children: badges[index].icon\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 254,\n                      columnNumber: 57\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 244,\n                    columnNumber: 53\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `${heights[index]} w-16 sm:w-18 md:w-20 bg-gradient-to-t ${badges[index].color} rounded-t-lg flex flex-col justify-end p-1 sm:p-2 shadow-lg`,\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white text-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"font-bold text-sm sm:text-base md:text-lg\",\n                        children: actualPosition + 1\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 260,\n                        columnNumber: 61\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-xs hidden sm:block\",\n                        children: badges[index].title\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 261,\n                        columnNumber: 61\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 259,\n                      columnNumber: 57\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 258,\n                    columnNumber: 53\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-1 sm:mt-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-bold text-xs sm:text-sm truncate w-16 sm:w-18 md:w-20\",\n                      children: user.userName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 267,\n                      columnNumber: 57\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-yellow-600 font-semibold\",\n                      children: [user.totalPoints || 0, \" pts\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 268,\n                      columnNumber: 57\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `text-xs font-bold mt-1 ${user.subscriptionStatus === \"active\" ? 'text-green-600' : 'text-red-600'}`,\n                      children: user.subscriptionStatus === \"active\" ? \"Premium\" : \"Expired\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 269,\n                      columnNumber: 57\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 266,\n                    columnNumber: 53\n                  }, this)]\n                }, user.userId, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 49\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 sm:p-4 md:p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3 sm:space-y-4\",\n              children: (activeTab === \"overall\" ? rankingData : rankingData.filter(user => user.userClass === (userData === null || userData === void 0 ? void 0 : userData.class))).map((user, index) => {\n                const isCurrentUser = user.userId.includes(userData === null || userData === void 0 ? void 0 : userData._id);\n                const getRankBadge = position => {\n                  if (position === 0) return {\n                    icon: \"💎\",\n                    color: \"text-blue-600\",\n                    bg: \"bg-gradient-to-br from-blue-100 to-cyan-100\",\n                    border: \"border-blue-300\",\n                    title: \"Diamond\",\n                    glow: \"shadow-blue-200\"\n                  };\n                  if (position === 1) return {\n                    icon: \"🥈\",\n                    color: \"text-gray-600\",\n                    bg: \"bg-gradient-to-br from-gray-100 to-slate-100\",\n                    border: \"border-gray-300\",\n                    title: \"Silver\",\n                    glow: \"shadow-gray-200\"\n                  };\n                  if (position === 2) return {\n                    icon: \"🥉\",\n                    color: \"text-amber-600\",\n                    bg: \"bg-gradient-to-br from-amber-100 to-orange-100\",\n                    border: \"border-amber-300\",\n                    title: \"Bronze\",\n                    glow: \"shadow-amber-200\"\n                  };\n                  return {\n                    icon: position + 1,\n                    color: \"text-gray-600\",\n                    bg: \"bg-gray-50\",\n                    border: \"border-gray-200\",\n                    title: `Rank ${position + 1}`,\n                    glow: \"shadow-gray-100\"\n                  };\n                };\n                const rankInfo = getRankBadge(index);\n                return /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    x: -20\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0\n                  },\n                  transition: {\n                    delay: index * 0.1\n                  },\n                  className: `flex items-center space-x-2 sm:space-x-3 md:space-x-4 p-3 sm:p-4 rounded-xl transition-all duration-200 ${isCurrentUser ? 'bg-primary-50 border-2 border-primary-200 shadow-md' : user.subscriptionStatus === \"expired\" ? 'bg-gray-100 border border-gray-300 opacity-75' : 'bg-gray-50 hover:bg-gray-100'}`,\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `relative w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 rounded-full flex items-center justify-center ${rankInfo.bg} ${rankInfo.border} border-2 ${rankInfo.glow} shadow-lg transition-all duration-300 hover:scale-110 flex-shrink-0`,\n                    children: [index < 3 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-lg sm:text-xl md:text-2xl mb-0.5 sm:mb-1\",\n                        children: rankInfo.icon\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 344,\n                        columnNumber: 65\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `text-xs font-bold ${rankInfo.color} hidden sm:block`,\n                        children: rankInfo.title\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 345,\n                        columnNumber: 65\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 343,\n                      columnNumber: 61\n                    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-black text-sm sm:text-base md:text-lg text-gray-700\",\n                        children: rankInfo.icon\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 349,\n                        columnNumber: 65\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-xs font-semibold text-gray-500 hidden sm:block\",\n                        children: \"Rank\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 350,\n                        columnNumber: 65\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 348,\n                      columnNumber: 61\n                    }, this), index < 3 && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `absolute inset-0 rounded-full ${rankInfo.bg} opacity-50 blur-md -z-10 animate-pulse`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 356,\n                      columnNumber: 61\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 341,\n                    columnNumber: 53\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 h-8 sm:w-9 sm:h-9 md:w-10 md:h-10 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center flex-shrink-0\",\n                    children: user.userPhoto ? /*#__PURE__*/_jsxDEV(\"img\", {\n                      src: user.userPhoto,\n                      alt: \"profile\",\n                      className: \"w-full h-full object-cover\",\n                      onError: e => {\n                        e.target.src = image;\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 363,\n                      columnNumber: 61\n                    }, this) : /*#__PURE__*/_jsxDEV(TbUser, {\n                      className: \"w-3 h-3 sm:w-4 sm:h-4 md:w-5 md:h-5 text-gray-400\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 370,\n                      columnNumber: 61\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 361,\n                    columnNumber: 53\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex flex-col sm:flex-row sm:items-center space-y-1 sm:space-y-0 sm:space-x-2 mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: `font-bold text-sm sm:text-base md:text-lg truncate ${isCurrentUser ? 'text-primary-900' : 'text-gray-900'}`,\n                        children: user.userName\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 377,\n                        columnNumber: 61\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-1 sm:space-x-2\",\n                        children: [isCurrentUser && /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"bg-gradient-to-r from-blue-500 to-purple-500 text-white text-xs px-2 py-1 rounded-full font-bold\",\n                          children: \"You\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 384,\n                          columnNumber: 69\n                        }, this), index < 3 && /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: `text-xs px-2 py-1 rounded-full font-bold hidden sm:inline ${index === 0 ? 'bg-blue-100 text-blue-800' : index === 1 ? 'bg-gray-100 text-gray-800' : 'bg-amber-100 text-amber-800'}`,\n                          children: [rankInfo.title, \" Champion\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 387,\n                          columnNumber: 69\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: `text-xs px-2 py-1 rounded-full font-bold ${user.subscriptionStatus === \"active\" ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                          children: user.subscriptionStatus === \"active\" ? \"Premium\" : \"Expired\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 396,\n                          columnNumber: 65\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 382,\n                        columnNumber: 61\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 376,\n                      columnNumber: 57\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex flex-wrap items-center gap-2 sm:gap-3 md:gap-4 mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-2 h-2 bg-yellow-400 rounded-full\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 409,\n                          columnNumber: 65\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"font-bold text-yellow-600 text-xs sm:text-sm\",\n                          children: [user.totalPoints || 0, \" pts\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 410,\n                          columnNumber: 65\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 408,\n                        columnNumber: 61\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-2 h-2 bg-green-400 rounded-full\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 413,\n                          columnNumber: 65\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"font-semibold text-green-600 text-xs sm:text-sm\",\n                          children: [user.passedExamsCount || 0, \" passed\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 414,\n                          columnNumber: 65\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 412,\n                        columnNumber: 61\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 417,\n                          columnNumber: 65\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"font-semibold text-blue-600 text-xs sm:text-sm\",\n                          children: [user.quizzesTaken || 0, \" quizzes\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 418,\n                          columnNumber: 65\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 416,\n                        columnNumber: 61\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 407,\n                      columnNumber: 57\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex flex-col sm:flex-row sm:items-center space-y-1 sm:space-y-0 sm:space-x-4 text-xs sm:text-sm text-gray-600\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-1\",\n                        children: [/*#__PURE__*/_jsxDEV(TbSchool, {\n                          className: \"w-3 h-3 sm:w-4 sm:h-4\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 424,\n                          columnNumber: 65\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"truncate\",\n                          children: user.userSchool || 'Not Enrolled'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 425,\n                          columnNumber: 65\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 423,\n                        columnNumber: 61\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-1\",\n                        children: [/*#__PURE__*/_jsxDEV(TbUsers, {\n                          className: \"w-4 h-4\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 428,\n                          columnNumber: 65\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: user.userClass || 'Not Enrolled'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 429,\n                          columnNumber: 65\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 427,\n                        columnNumber: 61\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 422,\n                      columnNumber: 57\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 375,\n                    columnNumber: 53\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-right\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `text-2xl font-bold ${isCurrentUser ? 'text-primary-600' : 'text-gray-900'}`,\n                      children: user.score\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 436,\n                      columnNumber: 57\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-500\",\n                      children: \"points\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 441,\n                      columnNumber: 57\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 435,\n                    columnNumber: 53\n                  }, this)]\n                }, user.userId, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 49\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 29\n        }, this) : /*#__PURE__*/_jsxDEV(Card, {\n          className: \"p-12 text-center\",\n          children: [/*#__PURE__*/_jsxDEV(TbChartBar, {\n            className: \"w-16 h-16 text-gray-400 mx-auto mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-semibold text-gray-900 mb-2\",\n            children: \"No Rankings Yet\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Complete some quizzes to see your ranking on the leaderboard!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 453,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 450,\n          columnNumber: 29\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 17\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 107,\n    columnNumber: 9\n  }, this);\n};\n_s(Ranking, \"go+TI1mL+JKEHb4M2IP7GD/Gias=\", false, function () {\n  return [useDispatch];\n});\n_c = Ranking;\nexport default Ranking;\nvar _c;\n$RefreshReg$(_c, \"Ranking\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "motion", "AnimatePresence", "getAllReportsForRanking", "getUserInfo", "message", "Page<PERSON><PERSON>le", "useDispatch", "HideLoading", "ShowLoading", "Card", "<PERSON><PERSON>", "Loading", "image", "IoPersonCircleOutline", "TbTrophy", "TbMedal", "TbCrown", "TbUsers", "TbSchool", "TbStar", "TbChartBar", "TbUser", "TbAward", "jsxDEV", "_jsxDEV", "Ranking", "_s", "rankingData", "setRankingData", "userRanking", "setUserRanking", "userData", "setUserData", "isAdmin", "setIsAdmin", "isMobile", "setIsMobile", "activeTab", "setActiveTab", "dispatch", "fetchReports", "response", "success", "data", "error", "getUserData", "window", "innerWidth", "localStorage", "getItem", "getUserStats", "map", "user", "index", "ranking", "filter", "item", "userId", "includes", "_id", "formatMobileUserId", "prefix", "slice", "suffix", "className", "children", "div", "initial", "opacity", "y", "animate", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "transition", "delay", "button", "whileHover", "scale", "whileTap", "onClick", "layoutId", "length", "style", "animationDelay", "class", "userClass", "positions", "actualPosition", "indexOf", "heights", "badges", "icon", "color", "title", "userPhoto", "src", "alt", "userName", "totalPoints", "subscriptionStatus", "isCurrentUser", "getRankBadge", "position", "bg", "border", "glow", "rankInfo", "x", "onError", "e", "target", "passedExamsCount", "quizzesTaken", "userSchool", "score", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Ranking/index.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport './index.css'\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { getAllReportsForRanking } from \"../../../apicalls/reports\";\r\nimport { getUserInfo } from \"../../../apicalls/users\";\r\nimport { message } from \"antd\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { Card, Button, Loading } from \"../../../components/modern\";\r\nimport image from '../../../assets/person.png';\r\nimport { IoPersonCircleOutline } from \"react-icons/io5\";\r\nimport {\r\n  TbTrophy,\r\n  TbMedal,\r\n  TbCrown,\r\n  TbUsers,\r\n  TbSchool,\r\n  TbStar,\r\n  TbChartBar,\r\n  TbUser,\r\n  TbAward\r\n} from \"react-icons/tb\";\r\n\r\nconst Ranking = () => {\r\n    const [rankingData, setRankingData] = useState('');\r\n    const [userRanking, setUserRanking] = useState('');\r\n    const [userData, setUserData] = useState('');\r\n    const [isAdmin, setIsAdmin] = useState(false);\r\n    const [isMobile, setIsMobile] = useState(false);\r\n    const [activeTab, setActiveTab] = useState(\"overall\"); // \"overall\" or \"class\"\r\n\r\n\r\n    const dispatch = useDispatch();\r\n\r\n    const fetchReports = async () => {\r\n        try {\r\n            const response = await getAllReportsForRanking();\r\n            if (response.success) {\r\n                setRankingData(response.data);\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n    }\r\n\r\n\r\n    const getUserData = async () => {\r\n        try {\r\n            const response = await getUserInfo();\r\n            if (response.success) {\r\n                if (response.data.isAdmin) {\r\n                    setIsAdmin(true);\r\n                } else {\r\n                    setIsAdmin(false);\r\n                    setUserData(response.data);\r\n                    await fetchReports();\r\n                    dispatch(HideLoading());\r\n                }\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (window.innerWidth < 700) {\r\n            setIsMobile(true);\r\n        }\r\n        else {\r\n            setIsMobile(false);\r\n        }\r\n        if (localStorage.getItem(\"token\")) {\r\n            dispatch(ShowLoading());\r\n            getUserData();\r\n        }\r\n    }, []);\r\n\r\n    const getUserStats = () => {\r\n        const Ranking = rankingData\r\n            .map((user, index) => ({\r\n                user,\r\n                ranking: index + 1,\r\n            }))\r\n            .filter((item) => item.user.userId.includes(userData._id));\r\n        setUserRanking(Ranking);\r\n    }\r\n\r\n    useEffect(() => {\r\n        if (rankingData) {\r\n            getUserStats();\r\n        }\r\n    }, [rankingData]);\r\n\r\n    // Helper function to format user ID for mobile devices\r\n    const formatMobileUserId = (userId) => {\r\n        const prefix = userId.slice(0, 4);\r\n        const suffix = userId.slice(-4);\r\n        return `${prefix}.....${suffix}`;\r\n    };\r\n\r\n    return (\r\n        <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50\">\r\n            {!isAdmin && (\r\n                <div className=\"container-modern py-8\">\r\n                    {/* Modern Header - Responsive */}\r\n                    <motion.div\r\n                        initial={{ opacity: 0, y: -20 }}\r\n                        animate={{ opacity: 1, y: 0 }}\r\n                        className=\"text-center mb-6 sm:mb-8\"\r\n                    >\r\n                        <div className=\"inline-flex items-center justify-center w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full mb-3 sm:mb-4\">\r\n                            <TbTrophy className=\"w-6 h-6 sm:w-7 sm:h-7 md:w-8 md:h-8 text-white\" />\r\n                        </div>\r\n                        <h1 className=\"heading-2 text-gradient mb-3 sm:mb-4 text-2xl sm:text-3xl md:text-4xl\">Leaderboard</h1>\r\n                        <p className=\"text-base sm:text-lg md:text-xl text-gray-600\">\r\n                            See how you rank against other students\r\n                        </p>\r\n                    </motion.div>\r\n\r\n                    {/* Modern Tabs - Responsive */}\r\n                    <motion.div\r\n                        initial={{ opacity: 0, y: 20 }}\r\n                        animate={{ opacity: 1, y: 0 }}\r\n                        transition={{ delay: 0.2 }}\r\n                        className=\"mb-6 sm:mb-8\"\r\n                    >\r\n                        <Card className=\"p-2\">\r\n                            <div className=\"flex gap-2\">\r\n                                <motion.button\r\n                                    whileHover={{ scale: 1.02 }}\r\n                                    whileTap={{ scale: 0.98 }}\r\n                                    className={`flex items-center space-x-1 sm:space-x-2 px-3 sm:px-6 py-2 sm:py-3 rounded-lg font-medium transition-all duration-200 flex-1 justify-center text-sm sm:text-base ${\r\n                                        activeTab === \"overall\"\r\n                                            ? 'bg-primary-600 text-white shadow-md'\r\n                                            : 'text-gray-600 hover:bg-gray-100'\r\n                                    }`}\r\n                                    onClick={() => setActiveTab(\"overall\")}\r\n                                >\r\n                                    <TbUsers className=\"w-4 h-4 sm:w-5 sm:h-5\" />\r\n                                    <span className=\"hidden sm:inline\">Overall Ranking</span>\r\n                                    <span className=\"sm:hidden\">Overall</span>\r\n                                    {activeTab === \"overall\" && (\r\n                                        <motion.div\r\n                                            layoutId=\"activeRankingTab\"\r\n                                            className=\"w-2 h-2 bg-white rounded-full\"\r\n                                        />\r\n                                    )}\r\n                                </motion.button>\r\n                                <motion.button\r\n                                    whileHover={{ scale: 1.02 }}\r\n                                    whileTap={{ scale: 0.98 }}\r\n                                    className={`flex items-center space-x-1 sm:space-x-2 px-3 sm:px-6 py-2 sm:py-3 rounded-lg font-medium transition-all duration-200 flex-1 justify-center text-sm sm:text-base ${\r\n                                        activeTab === \"class\"\r\n                                            ? 'bg-primary-600 text-white shadow-md'\r\n                                            : 'text-gray-600 hover:bg-gray-100'\r\n                                    }`}\r\n                                    onClick={() => setActiveTab(\"class\")}\r\n                                >\r\n                                    <TbSchool className=\"w-4 h-4 sm:w-5 sm:h-5\" />\r\n                                    <span className=\"hidden sm:inline\">Class Ranking</span>\r\n                                    <span className=\"sm:hidden\">Class</span>\r\n                                    {activeTab === \"class\" && (\r\n                                        <motion.div\r\n                                            layoutId=\"activeRankingTab\"\r\n                                            className=\"w-2 h-2 bg-white rounded-full\"\r\n                                        />\r\n                                    )}\r\n                                </motion.button>\r\n                            </div>\r\n                        </Card>\r\n                    </motion.div>\r\n\r\n                    {/* Modern Leaderboard */}\r\n                    <motion.div\r\n                        initial={{ opacity: 0, y: 20 }}\r\n                        animate={{ opacity: 1, y: 0 }}\r\n                        transition={{ delay: 0.4 }}\r\n                    >\r\n                        {rankingData.length > 0 ? (\r\n                            <Card className=\"overflow-hidden\">\r\n                                {/* Enhanced Leaderboard Header - Responsive */}\r\n                                <div className=\"bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 text-white p-4 sm:p-6 relative overflow-hidden\">\r\n                                    {/* Background Pattern - Responsive */}\r\n                                    <div className=\"absolute inset-0 opacity-10\">\r\n                                        <div className=\"absolute top-2 left-2 sm:top-4 sm:left-4 text-3xl sm:text-4xl md:text-6xl\">🏆</div>\r\n                                        <div className=\"absolute top-4 right-4 sm:top-8 sm:right-8 text-2xl sm:text-3xl md:text-4xl\">⭐</div>\r\n                                        <div className=\"absolute bottom-2 left-1/4 sm:bottom-4 text-xl sm:text-2xl md:text-3xl\">🎯</div>\r\n                                        <div className=\"absolute bottom-3 right-1/3 sm:bottom-6 text-3xl sm:text-4xl md:text-5xl\">💎</div>\r\n                                    </div>\r\n\r\n                                    <div className=\"relative z-10\">\r\n                                        <div className=\"flex flex-col sm:flex-row items-center justify-center space-y-2 sm:space-y-0 sm:space-x-3 mb-3 sm:mb-2\">\r\n                                            <TbTrophy className=\"w-8 h-8 sm:w-10 sm:h-10 text-yellow-300 animate-bounce\" />\r\n                                            <h2 className=\"text-xl sm:text-2xl md:text-3xl font-black bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent text-center\">\r\n                                                {activeTab === \"overall\" ? \"Global Leaderboard\" : \"Class Champions\"}\r\n                                            </h2>\r\n                                            <TbTrophy className=\"w-8 h-8 sm:w-10 sm:h-10 text-yellow-300 animate-bounce hidden sm:block\" style={{ animationDelay: '0.5s' }} />\r\n                                        </div>\r\n                                        <p className=\"text-center text-blue-100 text-sm sm:text-base md:text-lg font-semibold\">\r\n                                            {activeTab === \"overall\"\r\n                                                ? \"🌟 Elite performers from all classes competing for glory! 🌟\"\r\n                                                : `🎓 Class ${userData?.class || 'your class'} top achievers! 🎓`\r\n                                            }\r\n                                        </p>\r\n                                        <div className=\"text-center mt-3\">\r\n                                            <span className=\"bg-white/20 px-3 py-2 sm:px-4 rounded-full text-xs sm:text-sm font-bold block sm:inline\">\r\n                                                💪 Earn points by acing quizzes • 🏅 Climb the ranks • 👑 Become a champion!\r\n                                            </span>\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n\r\n                                {/* Top 3 Podium - Responsive */}\r\n                                <div className=\"bg-gradient-to-br from-blue-50 to-purple-50 p-3 sm:p-6 border-b\">\r\n                                    <h3 className=\"text-lg sm:text-xl font-bold text-center mb-4 sm:mb-6 text-gray-800\">🏆 Champions Podium 🏆</h3>\r\n                                    <div className=\"flex justify-center items-end space-x-2 sm:space-x-4 overflow-x-auto pb-2\">\r\n                                        {(activeTab === \"overall\"\r\n                                            ? rankingData.slice(0, 3)\r\n                                            : rankingData.filter(user => user.userClass === userData?.class).slice(0, 3)\r\n                                        ).map((user, index) => {\r\n                                            const positions = [1, 0, 2]; // Silver, Diamond, Bronze order for visual appeal\r\n                                            const actualPosition = positions.indexOf(index);\r\n                                            const heights = ['h-16 sm:h-20 md:h-24', 'h-20 sm:h-24 md:h-32', 'h-12 sm:h-16 md:h-20']; // Responsive heights for podium effect\r\n                                            const badges = [\r\n                                                { icon: \"🥈\", color: \"from-gray-400 to-gray-600\", title: \"Silver\" },\r\n                                                { icon: \"💎\", color: \"from-blue-400 to-cyan-600\", title: \"Diamond\" },\r\n                                                { icon: \"🥉\", color: \"from-amber-400 to-orange-600\", title: \"Bronze\" }\r\n                                            ];\r\n\r\n                                            return (\r\n                                                <motion.div\r\n                                                    key={user.userId}\r\n                                                    initial={{ opacity: 0, y: 50 }}\r\n                                                    animate={{ opacity: 1, y: 0 }}\r\n                                                    transition={{ delay: index * 0.2 }}\r\n                                                    className=\"text-center\"\r\n                                                >\r\n                                                    {/* User Avatar - Optimized Size */}\r\n                                                    <div className=\"mb-2 sm:mb-3\">\r\n                                                        <div className=\"w-10 h-10 sm:w-12 sm:h-12 md:w-14 md:h-14 mx-auto rounded-full overflow-hidden border-2 sm:border-3 border-white shadow-lg\">\r\n                                                            {user.userPhoto ? (\r\n                                                                <img src={user.userPhoto} alt=\"profile\" className=\"w-full h-full object-cover\" />\r\n                                                            ) : (\r\n                                                                <div className=\"w-full h-full bg-gray-200 flex items-center justify-center\">\r\n                                                                    <TbUser className=\"w-3 h-3 sm:w-4 sm:h-4 md:w-5 md:h-5 text-gray-400\" />\r\n                                                                </div>\r\n                                                            )}\r\n                                                        </div>\r\n                                                        <div className=\"text-lg sm:text-xl md:text-2xl mt-1 sm:mt-2\">{badges[index].icon}</div>\r\n                                                    </div>\r\n\r\n                                                    {/* Podium - Responsive */}\r\n                                                    <div className={`${heights[index]} w-16 sm:w-18 md:w-20 bg-gradient-to-t ${badges[index].color} rounded-t-lg flex flex-col justify-end p-1 sm:p-2 shadow-lg`}>\r\n                                                        <div className=\"text-white text-center\">\r\n                                                            <div className=\"font-bold text-sm sm:text-base md:text-lg\">{actualPosition + 1}</div>\r\n                                                            <div className=\"text-xs hidden sm:block\">{badges[index].title}</div>\r\n                                                        </div>\r\n                                                    </div>\r\n\r\n                                                    {/* User Info - Responsive */}\r\n                                                    <div className=\"mt-1 sm:mt-2\">\r\n                                                        <div className=\"font-bold text-xs sm:text-sm truncate w-16 sm:w-18 md:w-20\">{user.userName}</div>\r\n                                                        <div className=\"text-xs text-yellow-600 font-semibold\">{user.totalPoints || 0} pts</div>\r\n                                                        <div className={`text-xs font-bold mt-1 ${\r\n                                                            user.subscriptionStatus === \"active\" ? 'text-green-600' : 'text-red-600'\r\n                                                        }`}>\r\n                                                            {user.subscriptionStatus === \"active\" ? \"Premium\" : \"Expired\"}\r\n                                                        </div>\r\n                                                    </div>\r\n                                                </motion.div>\r\n                                            );\r\n                                        })}\r\n                                    </div>\r\n                                </div>\r\n\r\n                                {/* Leaderboard Content - Responsive */}\r\n                                <div className=\"p-3 sm:p-4 md:p-6\">\r\n                                    <div className=\"space-y-3 sm:space-y-4\">\r\n                                        {(activeTab === \"overall\"\r\n                                            ? rankingData\r\n                                            : rankingData.filter(user => user.userClass === userData?.class)\r\n                                        ).map((user, index) => {\r\n                                            const isCurrentUser = user.userId.includes(userData?._id);\r\n                                            const getRankBadge = (position) => {\r\n                                                if (position === 0) return {\r\n                                                    icon: \"💎\",\r\n                                                    color: \"text-blue-600\",\r\n                                                    bg: \"bg-gradient-to-br from-blue-100 to-cyan-100\",\r\n                                                    border: \"border-blue-300\",\r\n                                                    title: \"Diamond\",\r\n                                                    glow: \"shadow-blue-200\"\r\n                                                };\r\n                                                if (position === 1) return {\r\n                                                    icon: \"🥈\",\r\n                                                    color: \"text-gray-600\",\r\n                                                    bg: \"bg-gradient-to-br from-gray-100 to-slate-100\",\r\n                                                    border: \"border-gray-300\",\r\n                                                    title: \"Silver\",\r\n                                                    glow: \"shadow-gray-200\"\r\n                                                };\r\n                                                if (position === 2) return {\r\n                                                    icon: \"🥉\",\r\n                                                    color: \"text-amber-600\",\r\n                                                    bg: \"bg-gradient-to-br from-amber-100 to-orange-100\",\r\n                                                    border: \"border-amber-300\",\r\n                                                    title: \"Bronze\",\r\n                                                    glow: \"shadow-amber-200\"\r\n                                                };\r\n                                                return {\r\n                                                    icon: position + 1,\r\n                                                    color: \"text-gray-600\",\r\n                                                    bg: \"bg-gray-50\",\r\n                                                    border: \"border-gray-200\",\r\n                                                    title: `Rank ${position + 1}`,\r\n                                                    glow: \"shadow-gray-100\"\r\n                                                };\r\n                                            };\r\n\r\n                                            const rankInfo = getRankBadge(index);\r\n\r\n                                            return (\r\n                                                <motion.div\r\n                                                    key={user.userId}\r\n                                                    initial={{ opacity: 0, x: -20 }}\r\n                                                    animate={{ opacity: 1, x: 0 }}\r\n                                                    transition={{ delay: index * 0.1 }}\r\n                                                    className={`flex items-center space-x-2 sm:space-x-3 md:space-x-4 p-3 sm:p-4 rounded-xl transition-all duration-200 ${\r\n                                                        isCurrentUser\r\n                                                            ? 'bg-primary-50 border-2 border-primary-200 shadow-md'\r\n                                                            : user.subscriptionStatus === \"expired\"\r\n                                                            ? 'bg-gray-100 border border-gray-300 opacity-75'\r\n                                                            : 'bg-gray-50 hover:bg-gray-100'\r\n                                                    }`}\r\n                                                >\r\n                                                    {/* Enhanced Rank Badge - Responsive */}\r\n                                                    <div className={`relative w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 rounded-full flex items-center justify-center ${rankInfo.bg} ${rankInfo.border} border-2 ${rankInfo.glow} shadow-lg transition-all duration-300 hover:scale-110 flex-shrink-0`}>\r\n                                                        {index < 3 ? (\r\n                                                            <div className=\"text-center\">\r\n                                                                <div className=\"text-lg sm:text-xl md:text-2xl mb-0.5 sm:mb-1\">{rankInfo.icon}</div>\r\n                                                                <div className={`text-xs font-bold ${rankInfo.color} hidden sm:block`}>{rankInfo.title}</div>\r\n                                                            </div>\r\n                                                        ) : (\r\n                                                            <div className=\"text-center\">\r\n                                                                <span className=\"font-black text-sm sm:text-base md:text-lg text-gray-700\">{rankInfo.icon}</span>\r\n                                                                <div className=\"text-xs font-semibold text-gray-500 hidden sm:block\">Rank</div>\r\n                                                            </div>\r\n                                                        )}\r\n\r\n                                                        {/* Glow effect for top 3 */}\r\n                                                        {index < 3 && (\r\n                                                            <div className={`absolute inset-0 rounded-full ${rankInfo.bg} opacity-50 blur-md -z-10 animate-pulse`}></div>\r\n                                                        )}\r\n                                                    </div>\r\n\r\n                                                    {/* Profile Picture - Optimized Size */}\r\n                                                    <div className=\"w-8 h-8 sm:w-9 sm:h-9 md:w-10 md:h-10 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center flex-shrink-0\">\r\n                                                        {user.userPhoto ? (\r\n                                                            <img\r\n                                                                src={user.userPhoto}\r\n                                                                alt=\"profile\"\r\n                                                                className=\"w-full h-full object-cover\"\r\n                                                                onError={(e) => { e.target.src = image }}\r\n                                                            />\r\n                                                        ) : (\r\n                                                            <TbUser className=\"w-3 h-3 sm:w-4 sm:h-4 md:w-5 md:h-5 text-gray-400\" />\r\n                                                        )}\r\n                                                    </div>\r\n\r\n                                                    {/* Enhanced User Info - Responsive */}\r\n                                                    <div className=\"flex-1 min-w-0\">\r\n                                                        <div className=\"flex flex-col sm:flex-row sm:items-center space-y-1 sm:space-y-0 sm:space-x-2 mb-2\">\r\n                                                            <h3 className={`font-bold text-sm sm:text-base md:text-lg truncate ${\r\n                                                                isCurrentUser ? 'text-primary-900' : 'text-gray-900'\r\n                                                            }`}>\r\n                                                                {user.userName}\r\n                                                            </h3>\r\n                                                            <div className=\"flex items-center space-x-1 sm:space-x-2\">\r\n                                                                {isCurrentUser && (\r\n                                                                    <span className=\"bg-gradient-to-r from-blue-500 to-purple-500 text-white text-xs px-2 py-1 rounded-full font-bold\">You</span>\r\n                                                                )}\r\n                                                                {index < 3 && (\r\n                                                                    <span className={`text-xs px-2 py-1 rounded-full font-bold hidden sm:inline ${\r\n                                                                        index === 0 ? 'bg-blue-100 text-blue-800' :\r\n                                                                        index === 1 ? 'bg-gray-100 text-gray-800' :\r\n                                                                        'bg-amber-100 text-amber-800'\r\n                                                                    }`}>\r\n                                                                        {rankInfo.title} Champion\r\n                                                                    </span>\r\n                                                                )}\r\n                                                                {/* Subscription Status Badge */}\r\n                                                                <span className={`text-xs px-2 py-1 rounded-full font-bold ${\r\n                                                                    user.subscriptionStatus === \"active\"\r\n                                                                        ? 'bg-green-100 text-green-800'\r\n                                                                        : 'bg-red-100 text-red-800'\r\n                                                                }`}>\r\n                                                                    {user.subscriptionStatus === \"active\" ? \"Premium\" : \"Expired\"}\r\n                                                                </span>\r\n                                                            </div>\r\n                                                        </div>\r\n\r\n                                                        {/* Points and Stats - Responsive */}\r\n                                                        <div className=\"flex flex-wrap items-center gap-2 sm:gap-3 md:gap-4 mb-2\">\r\n                                                            <div className=\"flex items-center space-x-1\">\r\n                                                                <div className=\"w-2 h-2 bg-yellow-400 rounded-full\"></div>\r\n                                                                <span className=\"font-bold text-yellow-600 text-xs sm:text-sm\">{user.totalPoints || 0} pts</span>\r\n                                                            </div>\r\n                                                            <div className=\"flex items-center space-x-1\">\r\n                                                                <div className=\"w-2 h-2 bg-green-400 rounded-full\"></div>\r\n                                                                <span className=\"font-semibold text-green-600 text-xs sm:text-sm\">{user.passedExamsCount || 0} passed</span>\r\n                                                            </div>\r\n                                                            <div className=\"flex items-center space-x-1\">\r\n                                                                <div className=\"w-2 h-2 bg-blue-400 rounded-full\"></div>\r\n                                                                <span className=\"font-semibold text-blue-600 text-xs sm:text-sm\">{user.quizzesTaken || 0} quizzes</span>\r\n                                                            </div>\r\n                                                        </div>\r\n\r\n                                                        <div className=\"flex flex-col sm:flex-row sm:items-center space-y-1 sm:space-y-0 sm:space-x-4 text-xs sm:text-sm text-gray-600\">\r\n                                                            <div className=\"flex items-center space-x-1\">\r\n                                                                <TbSchool className=\"w-3 h-3 sm:w-4 sm:h-4\" />\r\n                                                                <span className=\"truncate\">{user.userSchool || 'Not Enrolled'}</span>\r\n                                                            </div>\r\n                                                            <div className=\"flex items-center space-x-1\">\r\n                                                                <TbUsers className=\"w-4 h-4\" />\r\n                                                                <span>{user.userClass || 'Not Enrolled'}</span>\r\n                                                            </div>\r\n                                                        </div>\r\n                                                    </div>\r\n\r\n                                                    {/* Score */}\r\n                                                    <div className=\"text-right\">\r\n                                                        <div className={`text-2xl font-bold ${\r\n                                                            isCurrentUser ? 'text-primary-600' : 'text-gray-900'\r\n                                                        }`}>\r\n                                                            {user.score}\r\n                                                        </div>\r\n                                                        <div className=\"text-xs text-gray-500\">points</div>\r\n                                                    </div>\r\n                                                </motion.div>\r\n                                            );\r\n                                        })}\r\n                                    </div>\r\n                                </div>\r\n                            </Card>\r\n                        ) : (\r\n                            <Card className=\"p-12 text-center\">\r\n                                <TbChartBar className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\r\n                                <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">No Rankings Yet</h3>\r\n                                <p className=\"text-gray-600\">\r\n                                    Complete some quizzes to see your ranking on the leaderboard!\r\n                                </p>\r\n                            </Card>\r\n                        )}\r\n                    </motion.div>\r\n\r\n                </div>\r\n            )}\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default Ranking;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAO,aAAa;AACpB,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,uBAAuB,QAAQ,2BAA2B;AACnE,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,OAAO,QAAQ,MAAM;AAC9B,OAAOC,SAAS,MAAM,+BAA+B;AACrD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,IAAI,EAAEC,MAAM,EAAEC,OAAO,QAAQ,4BAA4B;AAClE,OAAOC,KAAK,MAAM,4BAA4B;AAC9C,SAASC,qBAAqB,QAAQ,iBAAiB;AACvD,SACEC,QAAQ,EACRC,OAAO,EACPC,OAAO,EACPC,OAAO,EACPC,QAAQ,EACRC,MAAM,EACNC,UAAU,EACVC,MAAM,EACNC,OAAO,QACF,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACgC,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoC,QAAQ,EAAEC,WAAW,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACsC,SAAS,EAAEC,YAAY,CAAC,GAAGvC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;;EAGvD,MAAMwC,QAAQ,GAAGjC,WAAW,CAAC,CAAC;EAE9B,MAAMkC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMvC,uBAAuB,CAAC,CAAC;MAChD,IAAIuC,QAAQ,CAACC,OAAO,EAAE;QAClBd,cAAc,CAACa,QAAQ,CAACE,IAAI,CAAC;MACjC,CAAC,MAAM;QACHvC,OAAO,CAACwC,KAAK,CAACH,QAAQ,CAACrC,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAOwC,KAAK,EAAE;MACZxC,OAAO,CAACwC,KAAK,CAACA,KAAK,CAACxC,OAAO,CAAC;IAChC;EACJ,CAAC;EAGD,MAAMyC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACA,MAAMJ,QAAQ,GAAG,MAAMtC,WAAW,CAAC,CAAC;MACpC,IAAIsC,QAAQ,CAACC,OAAO,EAAE;QAClB,IAAID,QAAQ,CAACE,IAAI,CAACV,OAAO,EAAE;UACvBC,UAAU,CAAC,IAAI,CAAC;QACpB,CAAC,MAAM;UACHA,UAAU,CAAC,KAAK,CAAC;UACjBF,WAAW,CAACS,QAAQ,CAACE,IAAI,CAAC;UAC1B,MAAMH,YAAY,CAAC,CAAC;UACpBD,QAAQ,CAAChC,WAAW,CAAC,CAAC,CAAC;QAC3B;MACJ,CAAC,MAAM;QACHH,OAAO,CAACwC,KAAK,CAACH,QAAQ,CAACrC,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAOwC,KAAK,EAAE;MACZxC,OAAO,CAACwC,KAAK,CAACA,KAAK,CAACxC,OAAO,CAAC;IAChC;EACJ,CAAC;EAEDN,SAAS,CAAC,MAAM;IACZ,IAAIgD,MAAM,CAACC,UAAU,GAAG,GAAG,EAAE;MACzBX,WAAW,CAAC,IAAI,CAAC;IACrB,CAAC,MACI;MACDA,WAAW,CAAC,KAAK,CAAC;IACtB;IACA,IAAIY,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE;MAC/BV,QAAQ,CAAC/B,WAAW,CAAC,CAAC,CAAC;MACvBqC,WAAW,CAAC,CAAC;IACjB;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,YAAY,GAAGA,CAAA,KAAM;IACvB,MAAMzB,OAAO,GAAGE,WAAW,CACtBwB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,MAAM;MACnBD,IAAI;MACJE,OAAO,EAAED,KAAK,GAAG;IACrB,CAAC,CAAC,CAAC,CACFE,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACJ,IAAI,CAACK,MAAM,CAACC,QAAQ,CAAC3B,QAAQ,CAAC4B,GAAG,CAAC,CAAC;IAC9D7B,cAAc,CAACL,OAAO,CAAC;EAC3B,CAAC;EAED3B,SAAS,CAAC,MAAM;IACZ,IAAI6B,WAAW,EAAE;MACbuB,YAAY,CAAC,CAAC;IAClB;EACJ,CAAC,EAAE,CAACvB,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAMiC,kBAAkB,GAAIH,MAAM,IAAK;IACnC,MAAMI,MAAM,GAAGJ,MAAM,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IACjC,MAAMC,MAAM,GAAGN,MAAM,CAACK,KAAK,CAAC,CAAC,CAAC,CAAC;IAC/B,OAAQ,GAAED,MAAO,QAAOE,MAAO,EAAC;EACpC,CAAC;EAED,oBACIvC,OAAA;IAAKwC,SAAS,EAAC,wDAAwD;IAAAC,QAAA,EAClE,CAAChC,OAAO,iBACLT,OAAA;MAAKwC,SAAS,EAAC,uBAAuB;MAAAC,QAAA,gBAElCzC,OAAA,CAACxB,MAAM,CAACkE,GAAG;QACPC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BL,SAAS,EAAC,0BAA0B;QAAAC,QAAA,gBAEpCzC,OAAA;UAAKwC,SAAS,EAAC,4JAA4J;UAAAC,QAAA,eACvKzC,OAAA,CAACV,QAAQ;YAACkD,SAAS,EAAC;UAAgD;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE,CAAC,eACNlD,OAAA;UAAIwC,SAAS,EAAC,uEAAuE;UAAAC,QAAA,EAAC;QAAW;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtGlD,OAAA;UAAGwC,SAAS,EAAC,+CAA+C;UAAAC,QAAA,EAAC;QAE7D;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGblD,OAAA,CAACxB,MAAM,CAACkE,GAAG;QACPC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BM,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3BZ,SAAS,EAAC,cAAc;QAAAC,QAAA,eAExBzC,OAAA,CAACf,IAAI;UAACuD,SAAS,EAAC,KAAK;UAAAC,QAAA,eACjBzC,OAAA;YAAKwC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACvBzC,OAAA,CAACxB,MAAM,CAAC6E,MAAM;cACVC,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAC1Bf,SAAS,EAAG,oKACR3B,SAAS,KAAK,SAAS,GACjB,qCAAqC,GACrC,iCACT,EAAE;cACH4C,OAAO,EAAEA,CAAA,KAAM3C,YAAY,CAAC,SAAS,CAAE;cAAA2B,QAAA,gBAEvCzC,OAAA,CAACP,OAAO;gBAAC+C,SAAS,EAAC;cAAuB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7ClD,OAAA;gBAAMwC,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAC;cAAe;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzDlD,OAAA;gBAAMwC,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAC;cAAO;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EACzCrC,SAAS,KAAK,SAAS,iBACpBb,OAAA,CAACxB,MAAM,CAACkE,GAAG;gBACPgB,QAAQ,EAAC,kBAAkB;gBAC3BlB,SAAS,EAAC;cAA+B;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU,CAAC,eAChBlD,OAAA,CAACxB,MAAM,CAAC6E,MAAM;cACVC,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAC1Bf,SAAS,EAAG,oKACR3B,SAAS,KAAK,OAAO,GACf,qCAAqC,GACrC,iCACT,EAAE;cACH4C,OAAO,EAAEA,CAAA,KAAM3C,YAAY,CAAC,OAAO,CAAE;cAAA2B,QAAA,gBAErCzC,OAAA,CAACN,QAAQ;gBAAC8C,SAAS,EAAC;cAAuB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9ClD,OAAA;gBAAMwC,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAC;cAAa;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvDlD,OAAA;gBAAMwC,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAC;cAAK;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EACvCrC,SAAS,KAAK,OAAO,iBAClBb,OAAA,CAACxB,MAAM,CAACkE,GAAG;gBACPgB,QAAQ,EAAC,kBAAkB;gBAC3BlB,SAAS,EAAC;cAA+B;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGblD,OAAA,CAACxB,MAAM,CAACkE,GAAG;QACPC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BM,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAAX,QAAA,EAE1BtC,WAAW,CAACwD,MAAM,GAAG,CAAC,gBACnB3D,OAAA,CAACf,IAAI;UAACuD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAE7BzC,OAAA;YAAKwC,SAAS,EAAC,4GAA4G;YAAAC,QAAA,gBAEvHzC,OAAA;cAAKwC,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBACxCzC,OAAA;gBAAKwC,SAAS,EAAC,2EAA2E;gBAAAC,QAAA,EAAC;cAAE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACnGlD,OAAA;gBAAKwC,SAAS,EAAC,6EAA6E;gBAAAC,QAAA,EAAC;cAAC;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpGlD,OAAA;gBAAKwC,SAAS,EAAC,wEAAwE;gBAAAC,QAAA,EAAC;cAAE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChGlD,OAAA;gBAAKwC,SAAS,EAAC,0EAA0E;gBAAAC,QAAA,EAAC;cAAE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjG,CAAC,eAENlD,OAAA;cAAKwC,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC1BzC,OAAA;gBAAKwC,SAAS,EAAC,wGAAwG;gBAAAC,QAAA,gBACnHzC,OAAA,CAACV,QAAQ;kBAACkD,SAAS,EAAC;gBAAwD;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC/ElD,OAAA;kBAAIwC,SAAS,EAAC,qIAAqI;kBAAAC,QAAA,EAC9I5B,SAAS,KAAK,SAAS,GAAG,oBAAoB,GAAG;gBAAiB;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CAAC,eACLlD,OAAA,CAACV,QAAQ;kBAACkD,SAAS,EAAC,wEAAwE;kBAACoB,KAAK,EAAE;oBAAEC,cAAc,EAAE;kBAAO;gBAAE;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjI,CAAC,eACNlD,OAAA;gBAAGwC,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,EACjF5B,SAAS,KAAK,SAAS,GAClB,8DAA8D,GAC7D,YAAW,CAAAN,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEuD,KAAK,KAAI,YAAa;cAAmB;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEtE,CAAC,eACJlD,OAAA;gBAAKwC,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,eAC7BzC,OAAA;kBAAMwC,SAAS,EAAC,yFAAyF;kBAAAC,QAAA,EAAC;gBAE1G;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGNlD,OAAA;YAAKwC,SAAS,EAAC,iEAAiE;YAAAC,QAAA,gBAC5EzC,OAAA;cAAIwC,SAAS,EAAC,qEAAqE;cAAAC,QAAA,EAAC;YAAsB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/GlD,OAAA;cAAKwC,SAAS,EAAC,2EAA2E;cAAAC,QAAA,EACrF,CAAC5B,SAAS,KAAK,SAAS,GACnBV,WAAW,CAACmC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GACvBnC,WAAW,CAAC4B,MAAM,CAACH,IAAI,IAAIA,IAAI,CAACmC,SAAS,MAAKxD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEuD,KAAK,EAAC,CAACxB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAC9EX,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;gBACnB,MAAMmC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBAC7B,MAAMC,cAAc,GAAGD,SAAS,CAACE,OAAO,CAACrC,KAAK,CAAC;gBAC/C,MAAMsC,OAAO,GAAG,CAAC,sBAAsB,EAAE,sBAAsB,EAAE,sBAAsB,CAAC,CAAC,CAAC;gBAC1F,MAAMC,MAAM,GAAG,CACX;kBAAEC,IAAI,EAAE,IAAI;kBAAEC,KAAK,EAAE,2BAA2B;kBAAEC,KAAK,EAAE;gBAAS,CAAC,EACnE;kBAAEF,IAAI,EAAE,IAAI;kBAAEC,KAAK,EAAE,2BAA2B;kBAAEC,KAAK,EAAE;gBAAU,CAAC,EACpE;kBAAEF,IAAI,EAAE,IAAI;kBAAEC,KAAK,EAAE,8BAA8B;kBAAEC,KAAK,EAAE;gBAAS,CAAC,CACzE;gBAED,oBACIvE,OAAA,CAACxB,MAAM,CAACkE,GAAG;kBAEPC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAG,CAAE;kBAC/BC,OAAO,EAAE;oBAAEF,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAE,CAAE;kBAC9BM,UAAU,EAAE;oBAAEC,KAAK,EAAEvB,KAAK,GAAG;kBAAI,CAAE;kBACnCW,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAGvBzC,OAAA;oBAAKwC,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBACzBzC,OAAA;sBAAKwC,SAAS,EAAC,4HAA4H;sBAAAC,QAAA,EACtIb,IAAI,CAAC4C,SAAS,gBACXxE,OAAA;wBAAKyE,GAAG,EAAE7C,IAAI,CAAC4C,SAAU;wBAACE,GAAG,EAAC,SAAS;wBAAClC,SAAS,EAAC;sBAA4B;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,gBAEjFlD,OAAA;wBAAKwC,SAAS,EAAC,4DAA4D;wBAAAC,QAAA,eACvEzC,OAAA,CAACH,MAAM;0BAAC2C,SAAS,EAAC;wBAAmD;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvE;oBACR;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC,eACNlD,OAAA;sBAAKwC,SAAS,EAAC,6CAA6C;sBAAAC,QAAA,EAAE2B,MAAM,CAACvC,KAAK,CAAC,CAACwC;oBAAI;sBAAAtB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtF,CAAC,eAGNlD,OAAA;oBAAKwC,SAAS,EAAG,GAAE2B,OAAO,CAACtC,KAAK,CAAE,0CAAyCuC,MAAM,CAACvC,KAAK,CAAC,CAACyC,KAAM,8DAA8D;oBAAA7B,QAAA,eACzJzC,OAAA;sBAAKwC,SAAS,EAAC,wBAAwB;sBAAAC,QAAA,gBACnCzC,OAAA;wBAAKwC,SAAS,EAAC,2CAA2C;wBAAAC,QAAA,EAAEwB,cAAc,GAAG;sBAAC;wBAAAlB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACrFlD,OAAA;wBAAKwC,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EAAE2B,MAAM,CAACvC,KAAK,CAAC,CAAC0C;sBAAK;wBAAAxB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eAGNlD,OAAA;oBAAKwC,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBACzBzC,OAAA;sBAAKwC,SAAS,EAAC,4DAA4D;sBAAAC,QAAA,EAAEb,IAAI,CAAC+C;oBAAQ;sBAAA5B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjGlD,OAAA;sBAAKwC,SAAS,EAAC,uCAAuC;sBAAAC,QAAA,GAAEb,IAAI,CAACgD,WAAW,IAAI,CAAC,EAAC,MAAI;oBAAA;sBAAA7B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACxFlD,OAAA;sBAAKwC,SAAS,EAAG,0BACbZ,IAAI,CAACiD,kBAAkB,KAAK,QAAQ,GAAG,gBAAgB,GAAG,cAC7D,EAAE;sBAAApC,QAAA,EACEb,IAAI,CAACiD,kBAAkB,KAAK,QAAQ,GAAG,SAAS,GAAG;oBAAS;sBAAA9B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA,GArCDtB,IAAI,CAACK,MAAM;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAsCR,CAAC;cAErB,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGNlD,OAAA;YAAKwC,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAC9BzC,OAAA;cAAKwC,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAClC,CAAC5B,SAAS,KAAK,SAAS,GACnBV,WAAW,GACXA,WAAW,CAAC4B,MAAM,CAACH,IAAI,IAAIA,IAAI,CAACmC,SAAS,MAAKxD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEuD,KAAK,EAAC,EAClEnC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;gBACnB,MAAMiD,aAAa,GAAGlD,IAAI,CAACK,MAAM,CAACC,QAAQ,CAAC3B,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE4B,GAAG,CAAC;gBACzD,MAAM4C,YAAY,GAAIC,QAAQ,IAAK;kBAC/B,IAAIA,QAAQ,KAAK,CAAC,EAAE,OAAO;oBACvBX,IAAI,EAAE,IAAI;oBACVC,KAAK,EAAE,eAAe;oBACtBW,EAAE,EAAE,6CAA6C;oBACjDC,MAAM,EAAE,iBAAiB;oBACzBX,KAAK,EAAE,SAAS;oBAChBY,IAAI,EAAE;kBACV,CAAC;kBACD,IAAIH,QAAQ,KAAK,CAAC,EAAE,OAAO;oBACvBX,IAAI,EAAE,IAAI;oBACVC,KAAK,EAAE,eAAe;oBACtBW,EAAE,EAAE,8CAA8C;oBAClDC,MAAM,EAAE,iBAAiB;oBACzBX,KAAK,EAAE,QAAQ;oBACfY,IAAI,EAAE;kBACV,CAAC;kBACD,IAAIH,QAAQ,KAAK,CAAC,EAAE,OAAO;oBACvBX,IAAI,EAAE,IAAI;oBACVC,KAAK,EAAE,gBAAgB;oBACvBW,EAAE,EAAE,gDAAgD;oBACpDC,MAAM,EAAE,kBAAkB;oBAC1BX,KAAK,EAAE,QAAQ;oBACfY,IAAI,EAAE;kBACV,CAAC;kBACD,OAAO;oBACHd,IAAI,EAAEW,QAAQ,GAAG,CAAC;oBAClBV,KAAK,EAAE,eAAe;oBACtBW,EAAE,EAAE,YAAY;oBAChBC,MAAM,EAAE,iBAAiB;oBACzBX,KAAK,EAAG,QAAOS,QAAQ,GAAG,CAAE,EAAC;oBAC7BG,IAAI,EAAE;kBACV,CAAC;gBACL,CAAC;gBAED,MAAMC,QAAQ,GAAGL,YAAY,CAAClD,KAAK,CAAC;gBAEpC,oBACI7B,OAAA,CAACxB,MAAM,CAACkE,GAAG;kBAEPC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEyC,CAAC,EAAE,CAAC;kBAAG,CAAE;kBAChCvC,OAAO,EAAE;oBAAEF,OAAO,EAAE,CAAC;oBAAEyC,CAAC,EAAE;kBAAE,CAAE;kBAC9BlC,UAAU,EAAE;oBAAEC,KAAK,EAAEvB,KAAK,GAAG;kBAAI,CAAE;kBACnCW,SAAS,EAAG,2GACRsC,aAAa,GACP,qDAAqD,GACrDlD,IAAI,CAACiD,kBAAkB,KAAK,SAAS,GACrC,+CAA+C,GAC/C,8BACT,EAAE;kBAAApC,QAAA,gBAGHzC,OAAA;oBAAKwC,SAAS,EAAG,oGAAmG4C,QAAQ,CAACH,EAAG,IAAGG,QAAQ,CAACF,MAAO,aAAYE,QAAQ,CAACD,IAAK,sEAAsE;oBAAA1C,QAAA,GAC9OZ,KAAK,GAAG,CAAC,gBACN7B,OAAA;sBAAKwC,SAAS,EAAC,aAAa;sBAAAC,QAAA,gBACxBzC,OAAA;wBAAKwC,SAAS,EAAC,+CAA+C;wBAAAC,QAAA,EAAE2C,QAAQ,CAACf;sBAAI;wBAAAtB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACpFlD,OAAA;wBAAKwC,SAAS,EAAG,qBAAoB4C,QAAQ,CAACd,KAAM,kBAAkB;wBAAA7B,QAAA,EAAE2C,QAAQ,CAACb;sBAAK;wBAAAxB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5F,CAAC,gBAENlD,OAAA;sBAAKwC,SAAS,EAAC,aAAa;sBAAAC,QAAA,gBACxBzC,OAAA;wBAAMwC,SAAS,EAAC,0DAA0D;wBAAAC,QAAA,EAAE2C,QAAQ,CAACf;sBAAI;wBAAAtB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACjGlD,OAAA;wBAAKwC,SAAS,EAAC,qDAAqD;wBAAAC,QAAA,EAAC;sBAAI;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9E,CACR,EAGArB,KAAK,GAAG,CAAC,iBACN7B,OAAA;sBAAKwC,SAAS,EAAG,iCAAgC4C,QAAQ,CAACH,EAAG;oBAAyC;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAC/G;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eAGNlD,OAAA;oBAAKwC,SAAS,EAAC,+HAA+H;oBAAAC,QAAA,EACzIb,IAAI,CAAC4C,SAAS,gBACXxE,OAAA;sBACIyE,GAAG,EAAE7C,IAAI,CAAC4C,SAAU;sBACpBE,GAAG,EAAC,SAAS;sBACblC,SAAS,EAAC,4BAA4B;sBACtC8C,OAAO,EAAGC,CAAC,IAAK;wBAAEA,CAAC,CAACC,MAAM,CAACf,GAAG,GAAGrF,KAAK;sBAAC;oBAAE;sBAAA2D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC,gBAEFlD,OAAA,CAACH,MAAM;sBAAC2C,SAAS,EAAC;oBAAmD;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAC1E;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eAGNlD,OAAA;oBAAKwC,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBAC3BzC,OAAA;sBAAKwC,SAAS,EAAC,oFAAoF;sBAAAC,QAAA,gBAC/FzC,OAAA;wBAAIwC,SAAS,EAAG,sDACZsC,aAAa,GAAG,kBAAkB,GAAG,eACxC,EAAE;wBAAArC,QAAA,EACEb,IAAI,CAAC+C;sBAAQ;wBAAA5B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACd,CAAC,eACLlD,OAAA;wBAAKwC,SAAS,EAAC,0CAA0C;wBAAAC,QAAA,GACpDqC,aAAa,iBACV9E,OAAA;0BAAMwC,SAAS,EAAC,kGAAkG;0BAAAC,QAAA,EAAC;wBAAG;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAC/H,EACArB,KAAK,GAAG,CAAC,iBACN7B,OAAA;0BAAMwC,SAAS,EAAG,6DACdX,KAAK,KAAK,CAAC,GAAG,2BAA2B,GACzCA,KAAK,KAAK,CAAC,GAAG,2BAA2B,GACzC,6BACH,EAAE;0BAAAY,QAAA,GACE2C,QAAQ,CAACb,KAAK,EAAC,WACpB;wBAAA;0BAAAxB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CACT,eAEDlD,OAAA;0BAAMwC,SAAS,EAAG,4CACdZ,IAAI,CAACiD,kBAAkB,KAAK,QAAQ,GAC9B,6BAA6B,GAC7B,yBACT,EAAE;0BAAApC,QAAA,EACEb,IAAI,CAACiD,kBAAkB,KAAK,QAAQ,GAAG,SAAS,GAAG;wBAAS;0BAAA9B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC3D,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eAGNlD,OAAA;sBAAKwC,SAAS,EAAC,0DAA0D;sBAAAC,QAAA,gBACrEzC,OAAA;wBAAKwC,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,gBACxCzC,OAAA;0BAAKwC,SAAS,EAAC;wBAAoC;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eAC1DlD,OAAA;0BAAMwC,SAAS,EAAC,8CAA8C;0BAAAC,QAAA,GAAEb,IAAI,CAACgD,WAAW,IAAI,CAAC,EAAC,MAAI;wBAAA;0BAAA7B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChG,CAAC,eACNlD,OAAA;wBAAKwC,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,gBACxCzC,OAAA;0BAAKwC,SAAS,EAAC;wBAAmC;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACzDlD,OAAA;0BAAMwC,SAAS,EAAC,iDAAiD;0BAAAC,QAAA,GAAEb,IAAI,CAAC6D,gBAAgB,IAAI,CAAC,EAAC,SAAO;wBAAA;0BAAA1C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3G,CAAC,eACNlD,OAAA;wBAAKwC,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,gBACxCzC,OAAA;0BAAKwC,SAAS,EAAC;wBAAkC;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACxDlD,OAAA;0BAAMwC,SAAS,EAAC,gDAAgD;0BAAAC,QAAA,GAAEb,IAAI,CAAC8D,YAAY,IAAI,CAAC,EAAC,UAAQ;wBAAA;0BAAA3C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eAENlD,OAAA;sBAAKwC,SAAS,EAAC,gHAAgH;sBAAAC,QAAA,gBAC3HzC,OAAA;wBAAKwC,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,gBACxCzC,OAAA,CAACN,QAAQ;0BAAC8C,SAAS,EAAC;wBAAuB;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC9ClD,OAAA;0BAAMwC,SAAS,EAAC,UAAU;0BAAAC,QAAA,EAAEb,IAAI,CAAC+D,UAAU,IAAI;wBAAc;0BAAA5C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpE,CAAC,eACNlD,OAAA;wBAAKwC,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,gBACxCzC,OAAA,CAACP,OAAO;0BAAC+C,SAAS,EAAC;wBAAS;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC/BlD,OAAA;0BAAAyC,QAAA,EAAOb,IAAI,CAACmC,SAAS,IAAI;wBAAc;0BAAAhB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9C,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eAGNlD,OAAA;oBAAKwC,SAAS,EAAC,YAAY;oBAAAC,QAAA,gBACvBzC,OAAA;sBAAKwC,SAAS,EAAG,sBACbsC,aAAa,GAAG,kBAAkB,GAAG,eACxC,EAAE;sBAAArC,QAAA,EACEb,IAAI,CAACgE;oBAAK;sBAAA7C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACNlD,OAAA;sBAAKwC,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAM;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC;gBAAA,GAlHDtB,IAAI,CAACK,MAAM;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAmHR,CAAC;cAErB,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,gBAEPlD,OAAA,CAACf,IAAI;UAACuD,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC9BzC,OAAA,CAACJ,UAAU;YAAC4C,SAAS,EAAC;UAAsC;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/DlD,OAAA;YAAIwC,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAe;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7ElD,OAAA;YAAGwC,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAE7B;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEZ;EACR;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAAhD,EAAA,CAvbKD,OAAO;EAAA,QASQnB,WAAW;AAAA;AAAA+G,EAAA,GAT1B5F,OAAO;AAybb,eAAeA,OAAO;AAAC,IAAA4F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}