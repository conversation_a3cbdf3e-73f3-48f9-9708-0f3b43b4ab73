{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\QuizStart.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { getExamById } from '../../../apicalls/exams';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport { Card, Button, Loading } from '../../../components/modern';\nimport { TbClock, TbQuestionMark, TbTrophy, TbAlertTriangle, TbPlayerPlay, TbArrowLeft, TbBrain } from 'react-icons/tb';\nimport './responsive.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuizStart = () => {\n  _s();\n  var _examData$questions, _examData$questions2, _user$name, _user$name$charAt;\n  const [examData, setExamData] = useState(null);\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const {\n    user\n  } = useSelector(state => state.user);\n  useEffect(() => {\n    const fetchExamData = async () => {\n      try {\n        dispatch(ShowLoading());\n        const response = await getExamById({\n          examId: id\n        });\n        dispatch(HideLoading());\n        if (response.success) {\n          setExamData(response.data);\n        } else {\n          message.error(response.message);\n          navigate('/user/quiz');\n        }\n      } catch (error) {\n        dispatch(HideLoading());\n        message.error(error.message);\n        navigate('/user/quiz');\n      }\n    };\n    if (id) {\n      fetchExamData();\n    }\n  }, [id, dispatch, navigate]);\n  useEffect(() => {\n    document.body.classList.add('quiz-fullscreen');\n    return () => {\n      document.body.classList.remove('quiz-fullscreen');\n    };\n  }, []);\n  const handleStartQuiz = () => {\n    navigate(`/quiz/${id}/play`);\n  };\n  if (!examData) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(Loading, {\n        fullScreen: true,\n        text: \"Loading quiz details...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-600 via-indigo-700 to-purple-800 flex items-center justify-center p-4 relative overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute -top-40 -right-40 w-80 h-80 bg-white/10 rounded-full blur-3xl animate-pulse\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-white/10 rounded-full blur-3xl animate-pulse delay-1000\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-white/5 rounded-full blur-3xl animate-pulse delay-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"quiz-start-card\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        className: \"quiz-start-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"inline-flex items-center justify-center w-24 h-24 bg-white/20 rounded-full mb-6 backdrop-blur-sm shadow-2xl\",\n          children: /*#__PURE__*/_jsxDEV(TbBrain, {\n            className: \"w-12 h-12 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"quiz-start-title text-4xl sm:text-5xl font-bold text-white mb-4\",\n          children: examData.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"quiz-start-subtitle text-lg sm:text-xl text-white/90 max-w-2xl mx-auto leading-relaxed\",\n          children: \"Ready to challenge yourself? Test your knowledge and track your progress with this comprehensive quiz.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center space-x-6 mt-6 text-white/80\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-2 h-2 bg-white rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm\",\n              children: [((_examData$questions = examData.questions) === null || _examData$questions === void 0 ? void 0 : _examData$questions.length) || 0, \" Questions\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-2 h-2 bg-white rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm\",\n              children: [examData.duration || 0, \" Minutes\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-2 h-2 bg-white rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm\",\n              children: [\"Class \", examData.class]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.2\n        },\n        className: \"quiz-start-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"quiz-info-item\",\n          children: [/*#__PURE__*/_jsxDEV(TbQuestionMark, {\n            className: \"quiz-info-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"quiz-info-value\",\n            children: ((_examData$questions2 = examData.questions) === null || _examData$questions2 === void 0 ? void 0 : _examData$questions2.length) || 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"quiz-info-label\",\n            children: \"Questions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"quiz-info-item\",\n          children: [/*#__PURE__*/_jsxDEV(TbClock, {\n            className: \"quiz-info-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"quiz-info-value\",\n            children: examData.duration || 30\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"quiz-info-label\",\n            children: \"Minutes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"quiz-info-item\",\n          children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n            className: \"quiz-info-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"quiz-info-value\",\n            children: [examData.passingPercentage || 70, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"quiz-info-label\",\n            children: \"Pass Mark\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.3\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"p-6 mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"heading-3 mb-6 flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(TbAlertTriangle, {\n              className: \"w-6 h-6 text-warning-600 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 15\n            }, this), \" Instructions\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4 text-gray-700\",\n            children: [\"Read each question carefully before answering\", \"You can navigate between questions using Previous/Next buttons\", \"Make sure to answer all questions before submitting\", \"Keep an eye on the timer - the quiz will auto-submit when time runs out\"].map((instruction, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                x: 20\n              },\n              animate: {\n                opacity: 1,\n                x: 0\n              },\n              transition: {\n                delay: 0.4 + index * 0.1\n              },\n              className: \"flex items-start space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-primary-600 font-bold text-sm\",\n                  children: index + 1\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"leading-relaxed\",\n                children: instruction\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.4\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"p-6 mb-8 bg-gradient-to-r from-primary-50 to-blue-50 border-primary-200\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-primary-600 font-bold text-lg\",\n                children: (user === null || user === void 0 ? void 0 : (_user$name = user.name) === null || _user$name === void 0 ? void 0 : (_user$name$charAt = _user$name.charAt(0)) === null || _user$name$charAt === void 0 ? void 0 : _user$name$charAt.toUpperCase()) || 'U'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-semibold text-gray-900\",\n                children: [\"Welcome, \", (user === null || user === void 0 ? void 0 : user.name) || 'Student', \"!\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600\",\n                children: [\"Level: \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"badge-primary\",\n                  children: (user === null || user === void 0 ? void 0 : user.level) || 'Primary'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 158,\n                  columnNumber: 26\n                }, this), \" \\u2022 Class: \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"badge-primary\",\n                  children: (user === null || user === void 0 ? void 0 : user.class) || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 158,\n                  columnNumber: 101\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.6\n        },\n        className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          size: \"lg\",\n          onClick: () => navigate('/user/quiz'),\n          icon: /*#__PURE__*/_jsxDEV(TbArrowLeft, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 19\n          }, this),\n          className: \"sm:w-auto w-full\",\n          children: \"Back to Quizzes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"gradient\",\n          size: \"lg\",\n          onClick: handleStartQuiz,\n          icon: /*#__PURE__*/_jsxDEV(TbPlayerPlay, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 19\n          }, this),\n          iconPosition: \"right\",\n          className: \"sm:w-auto w-full\",\n          children: \"Start Quiz\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 72,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizStart, \"+k6XuQiozenodmM12ysuBxR6GPY=\", false, function () {\n  return [useParams, useNavigate, useDispatch, useSelector];\n});\n_c = QuizStart;\nexport default QuizStart;\nvar _c;\n$RefreshReg$(_c, \"QuizStart\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "useDispatch", "useSelector", "motion", "message", "getExamById", "HideLoading", "ShowLoading", "Card", "<PERSON><PERSON>", "Loading", "TbClock", "TbQuestionMark", "TbTrophy", "TbAlertTriangle", "TbPlayerPlay", "TbArrowLeft", "TbBrain", "jsxDEV", "_jsxDEV", "QuizStart", "_s", "_examData$questions", "_examData$questions2", "_user$name", "_user$name$charAt", "examData", "setExamData", "id", "navigate", "dispatch", "user", "state", "fetchExamData", "response", "examId", "success", "data", "error", "document", "body", "classList", "add", "remove", "handleStartQuiz", "className", "children", "fullScreen", "text", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "y", "animate", "name", "questions", "length", "duration", "class", "transition", "delay", "passingPercentage", "map", "instruction", "index", "x", "char<PERSON>t", "toUpperCase", "level", "variant", "size", "onClick", "icon", "iconPosition", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/QuizStart.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { getExamById } from '../../../apicalls/exams';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport { Card, Button, Loading } from '../../../components/modern';\nimport {\n  TbClock,\n  TbQuestionMark,\n  TbTrophy,\n  TbAlertTriangle,\n  TbPlayerPlay,\n  TbArrowLeft,\n  TbBrain\n} from 'react-icons/tb';\nimport './responsive.css';\n\nconst QuizStart = () => {\n  const [examData, setExamData] = useState(null);\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const { user } = useSelector((state) => state.user);\n\n  useEffect(() => {\n    const fetchExamData = async () => {\n      try {\n        dispatch(ShowLoading());\n        const response = await getExamById({ examId: id });\n        dispatch(HideLoading());\n\n        if (response.success) {\n          setExamData(response.data);\n        } else {\n          message.error(response.message);\n          navigate('/user/quiz');\n        }\n      } catch (error) {\n        dispatch(HideLoading());\n        message.error(error.message);\n        navigate('/user/quiz');\n      }\n    };\n\n    if (id) {\n      fetchExamData();\n    }\n  }, [id, dispatch, navigate]);\n\n  useEffect(() => {\n    document.body.classList.add('quiz-fullscreen');\n    return () => {\n      document.body.classList.remove('quiz-fullscreen');\n    };\n  }, []);\n\n  const handleStartQuiz = () => {\n    navigate(`/quiz/${id}/play`);\n  };\n\n  if (!examData) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 flex items-center justify-center\">\n        <Loading fullScreen text=\"Loading quiz details...\" />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-600 via-indigo-700 to-purple-800 flex items-center justify-center p-4 relative overflow-hidden\">\n      {/* Animated Background Elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute -top-40 -right-40 w-80 h-80 bg-white/10 rounded-full blur-3xl animate-pulse\"></div>\n        <div className=\"absolute -bottom-40 -left-40 w-80 h-80 bg-white/10 rounded-full blur-3xl animate-pulse delay-1000\"></div>\n        <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-white/5 rounded-full blur-3xl animate-pulse delay-500\"></div>\n      </div>\n\n      <div className=\"quiz-start-card\">\n        <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} className=\"quiz-start-header\">\n          <div className=\"inline-flex items-center justify-center w-24 h-24 bg-white/20 rounded-full mb-6 backdrop-blur-sm shadow-2xl\">\n            <TbBrain className=\"w-12 h-12 text-white\" />\n          </div>\n          <h1 className=\"quiz-start-title text-4xl sm:text-5xl font-bold text-white mb-4\">{examData.name}</h1>\n          <p className=\"quiz-start-subtitle text-lg sm:text-xl text-white/90 max-w-2xl mx-auto leading-relaxed\">\n            Ready to challenge yourself? Test your knowledge and track your progress with this comprehensive quiz.\n          </p>\n          <div className=\"flex items-center justify-center space-x-6 mt-6 text-white/80\">\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-2 h-2 bg-white rounded-full\"></div>\n              <span className=\"text-sm\">{examData.questions?.length || 0} Questions</span>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-2 h-2 bg-white rounded-full\"></div>\n              <span className=\"text-sm\">{examData.duration || 0} Minutes</span>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-2 h-2 bg-white rounded-full\"></div>\n              <span className=\"text-sm\">Class {examData.class}</span>\n            </div>\n          </div>\n        </motion.div>\n\n        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.2 }} className=\"quiz-start-info\">\n          <div className=\"quiz-info-item\">\n            <TbQuestionMark className=\"quiz-info-icon\" />\n            <div className=\"quiz-info-value\">{examData.questions?.length || 0}</div>\n            <div className=\"quiz-info-label\">Questions</div>\n          </div>\n          <div className=\"quiz-info-item\">\n            <TbClock className=\"quiz-info-icon\" />\n            <div className=\"quiz-info-value\">{examData.duration || 30}</div>\n            <div className=\"quiz-info-label\">Minutes</div>\n          </div>\n          <div className=\"quiz-info-item\">\n            <TbTrophy className=\"quiz-info-icon\" />\n            <div className=\"quiz-info-value\">{examData.passingPercentage || 70}%</div>\n            <div className=\"quiz-info-label\">Pass Mark</div>\n          </div>\n        </motion.div>\n\n        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.3 }}>\n          <Card className=\"p-6 mb-6\">\n            <h2 className=\"heading-3 mb-6 flex items-center\">\n              <TbAlertTriangle className=\"w-6 h-6 text-warning-600 mr-2\" /> Instructions\n            </h2>\n            <div className=\"space-y-4 text-gray-700\">\n              {[\"Read each question carefully before answering\", \"You can navigate between questions using Previous/Next buttons\", \"Make sure to answer all questions before submitting\", \"Keep an eye on the timer - the quiz will auto-submit when time runs out\"].map((instruction, index) => (\n                <motion.div\n                  key={index}\n                  initial={{ opacity: 0, x: 20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  transition={{ delay: 0.4 + index * 0.1 }}\n                  className=\"flex items-start space-x-3\"\n                >\n                  <div className=\"w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5\">\n                    <span className=\"text-primary-600 font-bold text-sm\">{index + 1}</span>\n                  </div>\n                  <p className=\"leading-relaxed\">{instruction}</p>\n                </motion.div>\n              ))}\n            </div>\n          </Card>\n        </motion.div>\n\n        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.4 }}>\n          <Card className=\"p-6 mb-8 bg-gradient-to-r from-primary-50 to-blue-50 border-primary-200\">\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center\">\n                <span className=\"text-primary-600 font-bold text-lg\">\n                  {user?.name?.charAt(0)?.toUpperCase() || 'U'}\n                </span>\n              </div>\n              <div>\n                <h3 className=\"font-semibold text-gray-900\">Welcome, {user?.name || 'Student'}!</h3>\n                <p className=\"text-gray-600\">\n                  Level: <span className=\"badge-primary\">{user?.level || 'Primary'}</span> • Class: <span className=\"badge-primary\">{user?.class || 'N/A'}</span>\n                </p>\n              </div>\n            </div>\n          </Card>\n        </motion.div>\n\n        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.6 }} className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n          <Button\n            variant=\"secondary\"\n            size=\"lg\"\n            onClick={() => navigate('/user/quiz')}\n            icon={<TbArrowLeft />}\n            className=\"sm:w-auto w-full\"\n          >\n            Back to Quizzes\n          </Button>\n          <Button\n            variant=\"gradient\"\n            size=\"lg\"\n            onClick={handleStartQuiz}\n            icon={<TbPlayerPlay />}\n            iconPosition=\"right\"\n            className=\"sm:w-auto w-full\"\n          >\n            Start Quiz\n          </Button>\n        </motion.div>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizStart;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,IAAI,EAAEC,MAAM,EAAEC,OAAO,QAAQ,4BAA4B;AAClE,SACEC,OAAO,EACPC,cAAc,EACdC,QAAQ,EACRC,eAAe,EACfC,YAAY,EACZC,WAAW,EACXC,OAAO,QACF,gBAAgB;AACvB,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,mBAAA,EAAAC,oBAAA,EAAAC,UAAA,EAAAC,iBAAA;EACtB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM;IAAE+B;EAAG,CAAC,GAAG7B,SAAS,CAAC,CAAC;EAC1B,MAAM8B,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAC9B,MAAM8B,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE8B;EAAK,CAAC,GAAG7B,WAAW,CAAE8B,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EAEnDjC,SAAS,CAAC,MAAM;IACd,MAAMmC,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACFH,QAAQ,CAACvB,WAAW,CAAC,CAAC,CAAC;QACvB,MAAM2B,QAAQ,GAAG,MAAM7B,WAAW,CAAC;UAAE8B,MAAM,EAAEP;QAAG,CAAC,CAAC;QAClDE,QAAQ,CAACxB,WAAW,CAAC,CAAC,CAAC;QAEvB,IAAI4B,QAAQ,CAACE,OAAO,EAAE;UACpBT,WAAW,CAACO,QAAQ,CAACG,IAAI,CAAC;QAC5B,CAAC,MAAM;UACLjC,OAAO,CAACkC,KAAK,CAACJ,QAAQ,CAAC9B,OAAO,CAAC;UAC/ByB,QAAQ,CAAC,YAAY,CAAC;QACxB;MACF,CAAC,CAAC,OAAOS,KAAK,EAAE;QACdR,QAAQ,CAACxB,WAAW,CAAC,CAAC,CAAC;QACvBF,OAAO,CAACkC,KAAK,CAACA,KAAK,CAAClC,OAAO,CAAC;QAC5ByB,QAAQ,CAAC,YAAY,CAAC;MACxB;IACF,CAAC;IAED,IAAID,EAAE,EAAE;MACNK,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAACL,EAAE,EAAEE,QAAQ,EAAED,QAAQ,CAAC,CAAC;EAE5B/B,SAAS,CAAC,MAAM;IACdyC,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAC9C,OAAO,MAAM;MACXH,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,iBAAiB,CAAC;IACnD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5Bf,QAAQ,CAAE,SAAQD,EAAG,OAAM,CAAC;EAC9B,CAAC;EAED,IAAI,CAACF,QAAQ,EAAE;IACb,oBACEP,OAAA;MAAK0B,SAAS,EAAC,yFAAyF;MAAAC,QAAA,eACtG3B,OAAA,CAACT,OAAO;QAACqC,UAAU;QAACC,IAAI,EAAC;MAAyB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC;EAEV;EAEA,oBACEjC,OAAA;IAAK0B,SAAS,EAAC,yIAAyI;IAAAC,QAAA,gBAEtJ3B,OAAA;MAAK0B,SAAS,EAAC,kCAAkC;MAAAC,QAAA,gBAC/C3B,OAAA;QAAK0B,SAAS,EAAC;MAAsF;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC5GjC,OAAA;QAAK0B,SAAS,EAAC;MAAmG;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACzHjC,OAAA;QAAK0B,SAAS,EAAC;MAA0I;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7J,CAAC,eAENjC,OAAA;MAAK0B,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B3B,OAAA,CAAChB,MAAM,CAACkD,GAAG;QAACC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAACC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAACX,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBACvG3B,OAAA;UAAK0B,SAAS,EAAC,6GAA6G;UAAAC,QAAA,eAC1H3B,OAAA,CAACF,OAAO;YAAC4B,SAAS,EAAC;UAAsB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACNjC,OAAA;UAAI0B,SAAS,EAAC,iEAAiE;UAAAC,QAAA,EAAEpB,QAAQ,CAACgC;QAAI;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpGjC,OAAA;UAAG0B,SAAS,EAAC,wFAAwF;UAAAC,QAAA,EAAC;QAEtG;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJjC,OAAA;UAAK0B,SAAS,EAAC,+DAA+D;UAAAC,QAAA,gBAC5E3B,OAAA;YAAK0B,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1C3B,OAAA;cAAK0B,SAAS,EAAC;YAA+B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrDjC,OAAA;cAAM0B,SAAS,EAAC,SAAS;cAAAC,QAAA,GAAE,EAAAxB,mBAAA,GAAAI,QAAQ,CAACiC,SAAS,cAAArC,mBAAA,uBAAlBA,mBAAA,CAAoBsC,MAAM,KAAI,CAAC,EAAC,YAAU;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE,CAAC,eACNjC,OAAA;YAAK0B,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1C3B,OAAA;cAAK0B,SAAS,EAAC;YAA+B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrDjC,OAAA;cAAM0B,SAAS,EAAC,SAAS;cAAAC,QAAA,GAAEpB,QAAQ,CAACmC,QAAQ,IAAI,CAAC,EAAC,UAAQ;YAAA;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eACNjC,OAAA;YAAK0B,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1C3B,OAAA;cAAK0B,SAAS,EAAC;YAA+B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrDjC,OAAA;cAAM0B,SAAS,EAAC,SAAS;cAAAC,QAAA,GAAC,QAAM,EAACpB,QAAQ,CAACoC,KAAK;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAEbjC,OAAA,CAAChB,MAAM,CAACkD,GAAG;QAACC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAACC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAACO,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAACnB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAChI3B,OAAA;UAAK0B,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B3B,OAAA,CAACP,cAAc;YAACiC,SAAS,EAAC;UAAgB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7CjC,OAAA;YAAK0B,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAE,EAAAvB,oBAAA,GAAAG,QAAQ,CAACiC,SAAS,cAAApC,oBAAA,uBAAlBA,oBAAA,CAAoBqC,MAAM,KAAI;UAAC;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxEjC,OAAA;YAAK0B,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eACNjC,OAAA;UAAK0B,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B3B,OAAA,CAACR,OAAO;YAACkC,SAAS,EAAC;UAAgB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtCjC,OAAA;YAAK0B,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAEpB,QAAQ,CAACmC,QAAQ,IAAI;UAAE;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChEjC,OAAA;YAAK0B,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAO;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eACNjC,OAAA;UAAK0B,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B3B,OAAA,CAACN,QAAQ;YAACgC,SAAS,EAAC;UAAgB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvCjC,OAAA;YAAK0B,SAAS,EAAC,iBAAiB;YAAAC,QAAA,GAAEpB,QAAQ,CAACuC,iBAAiB,IAAI,EAAE,EAAC,GAAC;UAAA;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1EjC,OAAA;YAAK0B,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAEbjC,OAAA,CAAChB,MAAM,CAACkD,GAAG;QAACC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAACC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAACO,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAAlB,QAAA,eACpG3B,OAAA,CAACX,IAAI;UAACqC,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACxB3B,OAAA;YAAI0B,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC9C3B,OAAA,CAACL,eAAe;cAAC+B,SAAS,EAAC;YAA+B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,iBAC/D;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLjC,OAAA;YAAK0B,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EACrC,CAAC,+CAA+C,EAAE,gEAAgE,EAAE,qDAAqD,EAAE,yEAAyE,CAAC,CAACoB,GAAG,CAAC,CAACC,WAAW,EAAEC,KAAK,kBAC5QjD,OAAA,CAAChB,MAAM,CAACkD,GAAG;cAETC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEc,CAAC,EAAE;cAAG,CAAE;cAC/BZ,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEc,CAAC,EAAE;cAAE,CAAE;cAC9BN,UAAU,EAAE;gBAAEC,KAAK,EAAE,GAAG,GAAGI,KAAK,GAAG;cAAI,CAAE;cACzCvB,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBAEtC3B,OAAA;gBAAK0B,SAAS,EAAC,2FAA2F;gBAAAC,QAAA,eACxG3B,OAAA;kBAAM0B,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,EAAEsB,KAAK,GAAG;gBAAC;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC,eACNjC,OAAA;gBAAG0B,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAEqB;cAAW;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA,GAT3CgB,KAAK;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAUA,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAEbjC,OAAA,CAAChB,MAAM,CAACkD,GAAG;QAACC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAACC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAACO,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAAlB,QAAA,eACpG3B,OAAA,CAACX,IAAI;UAACqC,SAAS,EAAC,yEAAyE;UAAAC,QAAA,eACvF3B,OAAA;YAAK0B,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1C3B,OAAA;cAAK0B,SAAS,EAAC,wEAAwE;cAAAC,QAAA,eACrF3B,OAAA;gBAAM0B,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EACjD,CAAAf,IAAI,aAAJA,IAAI,wBAAAP,UAAA,GAAJO,IAAI,CAAE2B,IAAI,cAAAlC,UAAA,wBAAAC,iBAAA,GAAVD,UAAA,CAAY8C,MAAM,CAAC,CAAC,CAAC,cAAA7C,iBAAA,uBAArBA,iBAAA,CAAuB8C,WAAW,CAAC,CAAC,KAAI;cAAG;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNjC,OAAA;cAAA2B,QAAA,gBACE3B,OAAA;gBAAI0B,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,GAAC,WAAS,EAAC,CAAAf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2B,IAAI,KAAI,SAAS,EAAC,GAAC;cAAA;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpFjC,OAAA;gBAAG0B,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAC,SACpB,eAAA3B,OAAA;kBAAM0B,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAE,CAAAf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyC,KAAK,KAAI;gBAAS;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,mBAAU,eAAAjC,OAAA;kBAAM0B,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAE,CAAAf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+B,KAAK,KAAI;gBAAK;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9I,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAEbjC,OAAA,CAAChB,MAAM,CAACkD,GAAG;QAACC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAACC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAACO,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAACnB,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC/J3B,OAAA,CAACV,MAAM;UACLgE,OAAO,EAAC,WAAW;UACnBC,IAAI,EAAC,IAAI;UACTC,OAAO,EAAEA,CAAA,KAAM9C,QAAQ,CAAC,YAAY,CAAE;UACtC+C,IAAI,eAAEzD,OAAA,CAACH,WAAW;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtBP,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAC7B;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTjC,OAAA,CAACV,MAAM;UACLgE,OAAO,EAAC,UAAU;UAClBC,IAAI,EAAC,IAAI;UACTC,OAAO,EAAE/B,eAAgB;UACzBgC,IAAI,eAAEzD,OAAA,CAACJ,YAAY;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvByB,YAAY,EAAC,OAAO;UACpBhC,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAC7B;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/B,EAAA,CAzKID,SAAS;EAAA,QAEErB,SAAS,EACPC,WAAW,EACXC,WAAW,EACXC,WAAW;AAAA;AAAA4E,EAAA,GALxB1D,SAAS;AA2Kf,eAAeA,SAAS;AAAC,IAAA0D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}