{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Ranking\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { getAllReportsForRanking } from \"../../../apicalls/reports\";\nimport { getUserInfo } from \"../../../apicalls/users\";\nimport { message } from \"antd\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport UserRankingList from \"../../../components/modern/UserRankingList\";\nimport { TbTrophy, TbUsers, TbSchool, TbChartBar } from \"react-icons/tb\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Ranking = () => {\n  _s();\n  const [rankingData, setRankingData] = useState('');\n  const [userRanking, setUserRanking] = useState('');\n  const [userData, setUserData] = useState('');\n  const [isAdmin, setIsAdmin] = useState(false);\n  const [isMobile, setIsMobile] = useState(false);\n  const [activeTab, setActiveTab] = useState(\"overall\"); // \"overall\" or \"class\"\n  const currentUserRef = useRef(null);\n  const dispatch = useDispatch();\n\n  // Function to scroll to current user\n  const scrollToCurrentUser = () => {\n    if (currentUserRef.current) {\n      currentUserRef.current.scrollIntoView({\n        behavior: 'smooth',\n        block: 'center'\n      });\n    }\n  };\n  const fetchReports = async () => {\n    try {\n      const response = await getAllReportsForRanking();\n      if (response.success) {\n        setRankingData(response.data);\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  const getUserData = async () => {\n    try {\n      const response = await getUserInfo();\n      if (response.success) {\n        if (response.data.isAdmin) {\n          setIsAdmin(true);\n        } else {\n          setIsAdmin(false);\n          setUserData(response.data);\n          await fetchReports();\n          dispatch(HideLoading());\n        }\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  useEffect(() => {\n    if (window.innerWidth < 700) {\n      setIsMobile(true);\n    } else {\n      setIsMobile(false);\n    }\n    if (localStorage.getItem(\"token\")) {\n      dispatch(ShowLoading());\n      getUserData();\n    }\n  }, []);\n  const getUserStats = () => {\n    const Ranking = rankingData.map((user, index) => ({\n      user,\n      ranking: index + 1\n    })).filter(item => item.user.userId.includes(userData._id));\n    setUserRanking(Ranking);\n  };\n  useEffect(() => {\n    if (rankingData) {\n      getUserStats();\n    }\n  }, [rankingData]);\n\n  // Helper function to format user ID for mobile devices\n  const formatMobileUserId = userId => {\n    const prefix = userId.slice(0, 4);\n    const suffix = userId.slice(-4);\n    return `${prefix}.....${suffix}`;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50\",\n    children: !isAdmin && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-modern py-8\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        className: \"text-center mb-6 sm:mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"inline-flex items-center justify-center w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full mb-3 sm:mb-4\",\n          children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n            className: \"w-6 h-6 sm:w-7 sm:h-7 md:w-8 md:h-8 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"heading-2 text-gradient mb-3 sm:mb-4 text-2xl sm:text-3xl md:text-4xl\",\n          children: \"Leaderboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-base sm:text-lg md:text-xl text-gray-600\",\n          children: \"See how you rank against other students\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.2\n        },\n        className: \"mb-6 sm:mb-8\",\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"p-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(motion.button, {\n              whileHover: {\n                scale: 1.02\n              },\n              whileTap: {\n                scale: 0.98\n              },\n              className: `flex items-center space-x-1 sm:space-x-2 px-3 sm:px-6 py-2 sm:py-3 rounded-lg font-medium transition-all duration-200 flex-1 justify-center text-sm sm:text-base ${activeTab === \"overall\" ? 'bg-primary-600 text-white shadow-md' : 'text-gray-600 hover:bg-gray-100'}`,\n              onClick: () => setActiveTab(\"overall\"),\n              children: [/*#__PURE__*/_jsxDEV(TbUsers, {\n                className: \"w-4 h-4 sm:w-5 sm:h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"hidden sm:inline\",\n                children: \"Overall Ranking\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"sm:hidden\",\n                children: \"Overall\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 37\n              }, this), activeTab === \"overall\" && /*#__PURE__*/_jsxDEV(motion.div, {\n                layoutId: \"activeRankingTab\",\n                className: \"w-2 h-2 bg-white rounded-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n              whileHover: {\n                scale: 1.02\n              },\n              whileTap: {\n                scale: 0.98\n              },\n              className: `flex items-center space-x-1 sm:space-x-2 px-3 sm:px-6 py-2 sm:py-3 rounded-lg font-medium transition-all duration-200 flex-1 justify-center text-sm sm:text-base ${activeTab === \"class\" ? 'bg-primary-600 text-white shadow-md' : 'text-gray-600 hover:bg-gray-100'}`,\n              onClick: () => setActiveTab(\"class\"),\n              children: [/*#__PURE__*/_jsxDEV(TbSchool, {\n                className: \"w-4 h-4 sm:w-5 sm:h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"hidden sm:inline\",\n                children: \"Class Ranking\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"sm:hidden\",\n                children: \"Class\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 37\n              }, this), activeTab === \"class\" && /*#__PURE__*/_jsxDEV(motion.div, {\n                layoutId: \"activeRankingTab\",\n                className: \"w-2 h-2 bg-white rounded-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 21\n      }, this), userData && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.3\n        },\n        className: \"mb-6\",\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"bg-gradient-to-r from-blue-50 to-purple-50 border-2 border-blue-200\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-bold text-gray-800 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(TbUser, {\n                  className: \"w-5 h-5 mr-2 text-blue-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 45\n                }, this), \"Your Position\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"bg-blue-100 text-blue-800 text-xs font-bold px-2 py-1 rounded-full\",\n                  children: [\"#\", userRanking && userRanking.length > 0 ? userRanking[0].ranking : 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"bg-yellow-100 text-yellow-800 text-xs font-bold px-2 py-1 rounded-full\",\n                  children: [userData.totalPoints || 0, \" pts\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-14 h-14 rounded-full p-0.5 flex items-center justify-center flex-shrink-0 ${userData.subscriptionStatus === \"active\" ? 'bg-gradient-to-r from-green-400 to-green-600' : userData.subscriptionStatus === \"free\" ? 'bg-gradient-to-r from-blue-400 to-blue-600' : 'bg-gradient-to-r from-red-400 to-red-600'}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-full h-full rounded-full overflow-hidden bg-gray-200 flex items-center justify-center\",\n                  children: userData.userPhoto ? /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: userData.userPhoto,\n                    alt: \"profile\",\n                    className: \"w-full h-full object-cover\",\n                    onError: e => {\n                      e.target.src = image;\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 210,\n                    columnNumber: 53\n                  }, this) : /*#__PURE__*/_jsxDEV(TbUser, {\n                    className: \"w-7 h-7 text-gray-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 217,\n                    columnNumber: 53\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 45\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2 mb-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-bold text-gray-900\",\n                    children: userData.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 225,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"bg-gradient-to-r from-blue-500 to-purple-500 text-white text-xs px-2 py-1 rounded-full font-bold\",\n                    children: \"You\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 226,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-4 text-sm text-gray-600\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [\"Class \", userData.class]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 229,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u2022\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 230,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: userData.school\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 231,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 228,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-right\",\n                children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: scrollToCurrentUser,\n                  className: \"bg-gradient-to-r from-blue-500 to-purple-500 text-white text-xs px-3 py-1.5 rounded-full font-bold mb-2 hover:shadow-md transition-all duration-200\",\n                  children: \"Find Me in List\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"Performance\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3 mt-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-green-600 font-bold\",\n                      children: userData.passedExamsCount || 0\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 248,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-500\",\n                      children: \"Passed\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 249,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 247,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-blue-600 font-bold\",\n                      children: userData.quizzesTaken || 0\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 252,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-500\",\n                      children: \"Quizzes\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 253,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 251,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 33\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 29\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 25\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.4\n        },\n        children: rankingData.length > 0 ? /*#__PURE__*/_jsxDEV(Card, {\n          className: \"overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 text-white p-4 sm:p-6 relative overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 opacity-10\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute top-2 left-2 sm:top-4 sm:left-4 text-3xl sm:text-4xl md:text-6xl\",\n                children: \"\\uD83C\\uDFC6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute top-4 right-4 sm:top-8 sm:right-8 text-2xl sm:text-3xl md:text-4xl\",\n                children: \"\\u2B50\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute bottom-2 left-1/4 sm:bottom-4 text-xl sm:text-2xl md:text-3xl\",\n                children: \"\\uD83C\\uDFAF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute bottom-3 right-1/3 sm:bottom-6 text-3xl sm:text-4xl md:text-5xl\",\n                children: \"\\uD83D\\uDC8E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative z-10\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col sm:flex-row items-center justify-center space-y-2 sm:space-y-0 sm:space-x-3 mb-3 sm:mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n                  className: \"w-8 h-8 sm:w-10 sm:h-10 text-yellow-300 animate-bounce\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-xl sm:text-2xl md:text-3xl font-black bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent text-center\",\n                  children: activeTab === \"overall\" ? \"Global Leaderboard\" : \"Class Champions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(TbTrophy, {\n                  className: \"w-8 h-8 sm:w-10 sm:h-10 text-yellow-300 animate-bounce hidden sm:block\",\n                  style: {\n                    animationDelay: '0.5s'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-center text-blue-100 text-sm sm:text-base md:text-lg font-semibold\",\n                children: activeTab === \"overall\" ? \"🌟 Elite performers from all classes competing for glory! 🌟\" : `🎓 Class ${(userData === null || userData === void 0 ? void 0 : userData.class) || 'your class'} top achievers! 🎓`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center mt-3\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"bg-white/20 px-3 py-2 sm:px-4 rounded-full text-xs sm:text-sm font-bold block sm:inline\",\n                  children: \"\\uD83D\\uDCAA Earn points by acing quizzes \\u2022 \\uD83C\\uDFC5 Climb the ranks \\u2022 \\uD83D\\uDC51 Become a champion!\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 45\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-br from-blue-50 to-purple-50 p-3 sm:p-6 border-b\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg sm:text-xl font-bold text-center mb-4 sm:mb-6 text-gray-800\",\n              children: \"\\uD83C\\uDFC6 Champions Podium \\uD83C\\uDFC6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-center items-end space-x-2 sm:space-x-4 overflow-x-auto pb-2\",\n              children: (activeTab === \"overall\" ? rankingData.slice(0, 3) : rankingData.filter(user => user.userClass === (userData === null || userData === void 0 ? void 0 : userData.class)).slice(0, 3)).map((user, index) => {\n                const positions = [1, 0, 2]; // Silver, Diamond, Bronze order for visual appeal\n                const actualPosition = positions.indexOf(index);\n                const heights = ['h-16 sm:h-20 md:h-24', 'h-20 sm:h-24 md:h-32', 'h-12 sm:h-16 md:h-20']; // Responsive heights for podium effect\n                const badges = [{\n                  icon: \"🥈\",\n                  color: \"from-gray-400 to-gray-600\",\n                  title: \"Silver\"\n                }, {\n                  icon: \"💎\",\n                  color: \"from-blue-400 to-cyan-600\",\n                  title: \"Diamond\"\n                }, {\n                  icon: \"🥉\",\n                  color: \"from-amber-400 to-orange-600\",\n                  title: \"Bronze\"\n                }];\n                return /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 50\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: index * 0.2\n                  },\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-2 sm:mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `w-16 h-16 mx-auto rounded-full p-1 shadow-lg ${user.subscriptionStatus === \"active\" ? 'bg-gradient-to-r from-green-400 to-green-600' : user.subscriptionStatus === \"free\" ? 'bg-gradient-to-r from-blue-400 to-blue-600' : 'bg-gradient-to-r from-red-400 to-red-600'}`,\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-full h-full rounded-full overflow-hidden bg-gray-200 flex items-center justify-center\",\n                        children: user.userPhoto ? /*#__PURE__*/_jsxDEV(\"img\", {\n                          src: user.userPhoto,\n                          alt: \"profile\",\n                          className: \"w-full h-full object-cover\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 339,\n                          columnNumber: 69\n                        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-full h-full bg-gray-200 flex items-center justify-center\",\n                          children: /*#__PURE__*/_jsxDEV(TbUser, {\n                            className: \"w-8 h-8 text-gray-400\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 342,\n                            columnNumber: 73\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 341,\n                          columnNumber: 69\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 337,\n                        columnNumber: 61\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 330,\n                      columnNumber: 57\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-lg sm:text-xl md:text-2xl mt-1 sm:mt-2\",\n                      children: badges[index].icon\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 347,\n                      columnNumber: 57\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 329,\n                    columnNumber: 53\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `${heights[index]} w-16 sm:w-18 md:w-20 bg-gradient-to-t ${badges[index].color} rounded-t-lg flex flex-col justify-end p-1 sm:p-2 shadow-lg`,\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white text-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"font-bold text-sm sm:text-base md:text-lg\",\n                        children: actualPosition + 1\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 353,\n                        columnNumber: 61\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-xs hidden sm:block\",\n                        children: badges[index].title\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 354,\n                        columnNumber: 61\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 352,\n                      columnNumber: 57\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 351,\n                    columnNumber: 53\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-1 sm:mt-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-bold text-xs sm:text-sm truncate w-16 sm:w-18 md:w-20\",\n                      children: user.userName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 360,\n                      columnNumber: 57\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-yellow-600 font-semibold\",\n                      children: [user.totalPoints || 0, \" pts\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 361,\n                      columnNumber: 57\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `text-xs font-bold mt-1 ${user.subscriptionStatus === \"active\" ? 'text-green-600' : user.subscriptionStatus === \"free\" ? 'text-blue-600' : 'text-red-600'}`,\n                      children: user.subscriptionStatus === \"active\" ? \"Premium\" : user.subscriptionStatus === \"free\" ? \"Free\" : \"Expired\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 362,\n                      columnNumber: 57\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 359,\n                    columnNumber: 53\n                  }, this)]\n                }, user.userId, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 49\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 sm:p-4 md:p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3 sm:space-y-4\",\n              children: (activeTab === \"overall\" ? rankingData : rankingData.filter(user => user.userClass === (userData === null || userData === void 0 ? void 0 : userData.class))).map((user, index) => {\n                const isCurrentUser = user.userId.includes(userData === null || userData === void 0 ? void 0 : userData._id);\n                const getRankBadge = position => {\n                  if (position === 0) return {\n                    icon: \"💎\",\n                    color: \"text-blue-600\",\n                    bg: \"bg-gradient-to-br from-blue-100 to-cyan-100\",\n                    border: \"border-blue-300\",\n                    title: \"Diamond\",\n                    glow: \"shadow-blue-200\"\n                  };\n                  if (position === 1) return {\n                    icon: \"🥈\",\n                    color: \"text-gray-600\",\n                    bg: \"bg-gradient-to-br from-gray-100 to-slate-100\",\n                    border: \"border-gray-300\",\n                    title: \"Silver\",\n                    glow: \"shadow-gray-200\"\n                  };\n                  if (position === 2) return {\n                    icon: \"🥉\",\n                    color: \"text-amber-600\",\n                    bg: \"bg-gradient-to-br from-amber-100 to-orange-100\",\n                    border: \"border-amber-300\",\n                    title: \"Bronze\",\n                    glow: \"shadow-amber-200\"\n                  };\n                  return {\n                    icon: position + 1,\n                    color: \"text-gray-600\",\n                    bg: \"bg-gray-50\",\n                    border: \"border-gray-200\",\n                    title: `Rank ${position + 1}`,\n                    glow: \"shadow-gray-100\"\n                  };\n                };\n                const rankInfo = getRankBadge(index);\n                return /*#__PURE__*/_jsxDEV(motion.div, {\n                  ref: isCurrentUser ? currentUserRef : null,\n                  initial: {\n                    opacity: 0,\n                    x: -20\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0\n                  },\n                  transition: {\n                    delay: index * 0.1\n                  },\n                  className: `flex items-center space-x-2 sm:space-x-3 md:space-x-4 p-3 sm:p-4 rounded-xl transition-all duration-200 ${isCurrentUser ? 'bg-gradient-to-r from-blue-50 to-purple-50 border-2 border-blue-300 shadow-lg ring-2 ring-blue-200 ring-opacity-50 transform scale-[1.02]' : user.subscriptionStatus === \"expired\" ? 'bg-gray-100 border border-gray-300 opacity-75' : user.subscriptionStatus === \"active\" ? 'bg-green-50 border border-green-200' : 'bg-gray-50 hover:bg-gray-100'}`,\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `relative w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 rounded-full flex items-center justify-center ${rankInfo.bg} ${rankInfo.border} border-2 ${rankInfo.glow} shadow-lg transition-all duration-300 hover:scale-110 flex-shrink-0`,\n                    children: [index < 3 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-lg sm:text-xl md:text-2xl mb-0.5 sm:mb-1\",\n                        children: rankInfo.icon\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 442,\n                        columnNumber: 65\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `text-xs font-bold ${rankInfo.color} hidden sm:block`,\n                        children: rankInfo.title\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 443,\n                        columnNumber: 65\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 441,\n                      columnNumber: 61\n                    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-black text-sm sm:text-base md:text-lg text-gray-700\",\n                        children: rankInfo.icon\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 447,\n                        columnNumber: 65\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-xs font-semibold text-gray-500 hidden sm:block\",\n                        children: \"Rank\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 448,\n                        columnNumber: 65\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 446,\n                      columnNumber: 61\n                    }, this), index < 3 && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `absolute inset-0 rounded-full ${rankInfo.bg} opacity-50 blur-md -z-10 animate-pulse`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 454,\n                      columnNumber: 61\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 439,\n                    columnNumber: 53\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `w-12 h-12 rounded-full p-0.5 flex items-center justify-center flex-shrink-0 ${user.subscriptionStatus === \"active\" ? 'bg-gradient-to-r from-green-400 to-green-600' : user.subscriptionStatus === \"free\" ? 'bg-gradient-to-r from-blue-400 to-blue-600' : 'bg-gradient-to-r from-red-400 to-red-600'}`,\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-full h-full rounded-full overflow-hidden bg-gray-200 flex items-center justify-center\",\n                      children: user.userPhoto ? /*#__PURE__*/_jsxDEV(\"img\", {\n                        src: user.userPhoto,\n                        alt: \"profile\",\n                        className: \"w-full h-full object-cover\",\n                        onError: e => {\n                          e.target.src = image;\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 468,\n                        columnNumber: 65\n                      }, this) : /*#__PURE__*/_jsxDEV(TbUser, {\n                        className: \"w-6 h-6 text-gray-400\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 475,\n                        columnNumber: 65\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 466,\n                      columnNumber: 57\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 459,\n                    columnNumber: 53\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex flex-col sm:flex-row sm:items-center space-y-1 sm:space-y-0 sm:space-x-2 mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: `font-bold text-sm sm:text-base md:text-lg truncate ${isCurrentUser ? 'text-primary-900' : 'text-gray-900'}`,\n                        children: user.userName\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 483,\n                        columnNumber: 61\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-1 sm:space-x-2\",\n                        children: [isCurrentUser && /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"bg-gradient-to-r from-blue-500 to-purple-500 text-white text-xs px-2 py-1 rounded-full font-bold\",\n                          children: \"You\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 490,\n                          columnNumber: 69\n                        }, this), index < 3 && /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: `text-xs px-2 py-1 rounded-full font-bold hidden sm:inline ${index === 0 ? 'bg-blue-100 text-blue-800' : index === 1 ? 'bg-gray-100 text-gray-800' : 'bg-amber-100 text-amber-800'}`,\n                          children: [rankInfo.title, \" Champion\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 493,\n                          columnNumber: 69\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: `text-xs px-2 py-1 rounded-full font-bold ${user.subscriptionStatus === \"active\" ? 'bg-green-100 text-green-800' : user.subscriptionStatus === \"free\" ? 'bg-blue-100 text-blue-800' : 'bg-red-100 text-red-800'}`,\n                          children: user.subscriptionStatus === \"active\" ? \"Premium\" : user.subscriptionStatus === \"free\" ? \"Free\" : \"Expired\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 502,\n                          columnNumber: 65\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 488,\n                        columnNumber: 61\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 482,\n                      columnNumber: 57\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex flex-wrap items-center gap-2 sm:gap-3 md:gap-4 mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-2 h-2 bg-yellow-400 rounded-full\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 518,\n                          columnNumber: 65\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"font-bold text-yellow-600 text-xs sm:text-sm\",\n                          children: [user.totalPoints || 0, \" pts\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 519,\n                          columnNumber: 65\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 517,\n                        columnNumber: 61\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-2 h-2 bg-green-400 rounded-full\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 522,\n                          columnNumber: 65\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"font-semibold text-green-600 text-xs sm:text-sm\",\n                          children: [user.passedExamsCount || 0, \" passed\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 523,\n                          columnNumber: 65\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 521,\n                        columnNumber: 61\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 526,\n                          columnNumber: 65\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"font-semibold text-blue-600 text-xs sm:text-sm\",\n                          children: [user.quizzesTaken || 0, \" quizzes\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 527,\n                          columnNumber: 65\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 525,\n                        columnNumber: 61\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 516,\n                      columnNumber: 57\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex flex-col sm:flex-row sm:items-center space-y-1 sm:space-y-0 sm:space-x-4 text-xs sm:text-sm text-gray-600\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-1\",\n                        children: [/*#__PURE__*/_jsxDEV(TbSchool, {\n                          className: \"w-3 h-3 sm:w-4 sm:h-4\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 533,\n                          columnNumber: 65\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"truncate\",\n                          children: user.userSchool || 'Not Enrolled'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 534,\n                          columnNumber: 65\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 532,\n                        columnNumber: 61\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-1\",\n                        children: [/*#__PURE__*/_jsxDEV(TbUsers, {\n                          className: \"w-4 h-4\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 537,\n                          columnNumber: 65\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: user.userClass || 'Not Enrolled'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 538,\n                          columnNumber: 65\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 536,\n                        columnNumber: 61\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 531,\n                      columnNumber: 57\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 481,\n                    columnNumber: 53\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-right\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `text-2xl font-bold ${isCurrentUser ? 'text-primary-600' : 'text-gray-900'}`,\n                      children: user.score\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 545,\n                      columnNumber: 57\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-500\",\n                      children: \"points\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 550,\n                      columnNumber: 57\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 544,\n                    columnNumber: 53\n                  }, this)]\n                }, user.userId, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 422,\n                  columnNumber: 49\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 29\n        }, this) : /*#__PURE__*/_jsxDEV(Card, {\n          className: \"p-12 text-center\",\n          children: [/*#__PURE__*/_jsxDEV(TbChartBar, {\n            className: \"w-16 h-16 text-gray-400 mx-auto mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 560,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-semibold text-gray-900 mb-2\",\n            children: \"No Rankings Yet\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 561,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Complete some quizzes to see your ranking on the leaderboard!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 562,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 559,\n          columnNumber: 29\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 21\n      }, this), userData && /*#__PURE__*/_jsxDEV(motion.button, {\n        initial: {\n          opacity: 0,\n          scale: 0\n        },\n        animate: {\n          opacity: 1,\n          scale: 1\n        },\n        transition: {\n          delay: 1\n        },\n        whileHover: {\n          scale: 1.1\n        },\n        whileTap: {\n          scale: 0.9\n        },\n        onClick: scrollToCurrentUser,\n        className: \"fixed bottom-6 right-6 bg-gradient-to-r from-blue-500 to-purple-500 text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 z-50\",\n        title: \"Find me in ranking\",\n        children: /*#__PURE__*/_jsxDEV(TbUser, {\n          className: \"w-6 h-6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 581,\n          columnNumber: 29\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 571,\n        columnNumber: 25\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 17\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 103,\n    columnNumber: 9\n  }, this);\n};\n_s(Ranking, \"wOjg51y0cglm8LK6qxSbINFgQb4=\", false, function () {\n  return [useDispatch];\n});\n_c = Ranking;\nexport default Ranking;\nvar _c;\n$RefreshReg$(_c, \"Ranking\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "motion", "getAllReportsForRanking", "getUserInfo", "message", "useDispatch", "useSelector", "HideLoading", "ShowLoading", "UserRankingList", "TbTrophy", "TbUsers", "TbSchool", "TbChartBar", "jsxDEV", "_jsxDEV", "Ranking", "_s", "rankingData", "setRankingData", "userRanking", "setUserRanking", "userData", "setUserData", "isAdmin", "setIsAdmin", "isMobile", "setIsMobile", "activeTab", "setActiveTab", "currentUserRef", "useRef", "dispatch", "scrollToCurrentUser", "current", "scrollIntoView", "behavior", "block", "fetchReports", "response", "success", "data", "error", "getUserData", "window", "innerWidth", "localStorage", "getItem", "getUserStats", "map", "user", "index", "ranking", "filter", "item", "userId", "includes", "_id", "formatMobileUserId", "prefix", "slice", "suffix", "className", "children", "div", "initial", "opacity", "y", "animate", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "transition", "delay", "Card", "button", "whileHover", "scale", "whileTap", "onClick", "layoutId", "TbUser", "length", "totalPoints", "subscriptionStatus", "userPhoto", "src", "alt", "onError", "e", "target", "image", "name", "class", "school", "passedExamsCount", "quizzesTaken", "style", "animationDelay", "userClass", "positions", "actualPosition", "indexOf", "heights", "badges", "icon", "color", "title", "userName", "isCurrentUser", "getRankBadge", "position", "bg", "border", "glow", "rankInfo", "ref", "x", "userSchool", "score", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Ranking/index.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { getAllReportsForRanking } from \"../../../apicalls/reports\";\r\nimport { getUserInfo } from \"../../../apicalls/users\";\r\nimport { message } from \"antd\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport UserRankingList from \"../../../components/modern/UserRankingList\";\r\nimport { TbTrophy, TbUsers, TbSchool, TbChartBar } from \"react-icons/tb\";\r\n\r\nconst Ranking = () => {\r\n    const [rankingData, setRankingData] = useState('');\r\n    const [userRanking, setUserRanking] = useState('');\r\n    const [userData, setUserData] = useState('');\r\n    const [isAdmin, setIsAdmin] = useState(false);\r\n    const [isMobile, setIsMobile] = useState(false);\r\n    const [activeTab, setActiveTab] = useState(\"overall\"); // \"overall\" or \"class\"\r\n    const currentUserRef = useRef(null);\r\n\r\n    const dispatch = useDispatch();\r\n\r\n    // Function to scroll to current user\r\n    const scrollToCurrentUser = () => {\r\n        if (currentUserRef.current) {\r\n            currentUserRef.current.scrollIntoView({\r\n                behavior: 'smooth',\r\n                block: 'center'\r\n            });\r\n        }\r\n    };\r\n\r\n    const fetchReports = async () => {\r\n        try {\r\n            const response = await getAllReportsForRanking();\r\n            if (response.success) {\r\n                setRankingData(response.data);\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n    }\r\n\r\n\r\n    const getUserData = async () => {\r\n        try {\r\n            const response = await getUserInfo();\r\n            if (response.success) {\r\n                if (response.data.isAdmin) {\r\n                    setIsAdmin(true);\r\n                } else {\r\n                    setIsAdmin(false);\r\n                    setUserData(response.data);\r\n                    await fetchReports();\r\n                    dispatch(HideLoading());\r\n                }\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (window.innerWidth < 700) {\r\n            setIsMobile(true);\r\n        }\r\n        else {\r\n            setIsMobile(false);\r\n        }\r\n        if (localStorage.getItem(\"token\")) {\r\n            dispatch(ShowLoading());\r\n            getUserData();\r\n        }\r\n    }, []);\r\n\r\n    const getUserStats = () => {\r\n        const Ranking = rankingData\r\n            .map((user, index) => ({\r\n                user,\r\n                ranking: index + 1,\r\n            }))\r\n            .filter((item) => item.user.userId.includes(userData._id));\r\n        setUserRanking(Ranking);\r\n    }\r\n\r\n    useEffect(() => {\r\n        if (rankingData) {\r\n            getUserStats();\r\n        }\r\n    }, [rankingData]);\r\n\r\n    // Helper function to format user ID for mobile devices\r\n    const formatMobileUserId = (userId) => {\r\n        const prefix = userId.slice(0, 4);\r\n        const suffix = userId.slice(-4);\r\n        return `${prefix}.....${suffix}`;\r\n    };\r\n\r\n    return (\r\n        <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50\">\r\n            {!isAdmin && (\r\n                <div className=\"container-modern py-8\">\r\n                    {/* Modern Header - Responsive */}\r\n                    <motion.div\r\n                        initial={{ opacity: 0, y: -20 }}\r\n                        animate={{ opacity: 1, y: 0 }}\r\n                        className=\"text-center mb-6 sm:mb-8\"\r\n                    >\r\n                        <div className=\"inline-flex items-center justify-center w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full mb-3 sm:mb-4\">\r\n                            <TbTrophy className=\"w-6 h-6 sm:w-7 sm:h-7 md:w-8 md:h-8 text-white\" />\r\n                        </div>\r\n                        <h1 className=\"heading-2 text-gradient mb-3 sm:mb-4 text-2xl sm:text-3xl md:text-4xl\">Leaderboard</h1>\r\n                        <p className=\"text-base sm:text-lg md:text-xl text-gray-600\">\r\n                            See how you rank against other students\r\n                        </p>\r\n                    </motion.div>\r\n\r\n                    {/* Modern Tabs - Responsive */}\r\n                    <motion.div\r\n                        initial={{ opacity: 0, y: 20 }}\r\n                        animate={{ opacity: 1, y: 0 }}\r\n                        transition={{ delay: 0.2 }}\r\n                        className=\"mb-6 sm:mb-8\"\r\n                    >\r\n                        <Card className=\"p-2\">\r\n                            <div className=\"flex gap-2\">\r\n                                <motion.button\r\n                                    whileHover={{ scale: 1.02 }}\r\n                                    whileTap={{ scale: 0.98 }}\r\n                                    className={`flex items-center space-x-1 sm:space-x-2 px-3 sm:px-6 py-2 sm:py-3 rounded-lg font-medium transition-all duration-200 flex-1 justify-center text-sm sm:text-base ${\r\n                                        activeTab === \"overall\"\r\n                                            ? 'bg-primary-600 text-white shadow-md'\r\n                                            : 'text-gray-600 hover:bg-gray-100'\r\n                                    }`}\r\n                                    onClick={() => setActiveTab(\"overall\")}\r\n                                >\r\n                                    <TbUsers className=\"w-4 h-4 sm:w-5 sm:h-5\" />\r\n                                    <span className=\"hidden sm:inline\">Overall Ranking</span>\r\n                                    <span className=\"sm:hidden\">Overall</span>\r\n                                    {activeTab === \"overall\" && (\r\n                                        <motion.div\r\n                                            layoutId=\"activeRankingTab\"\r\n                                            className=\"w-2 h-2 bg-white rounded-full\"\r\n                                        />\r\n                                    )}\r\n                                </motion.button>\r\n                                <motion.button\r\n                                    whileHover={{ scale: 1.02 }}\r\n                                    whileTap={{ scale: 0.98 }}\r\n                                    className={`flex items-center space-x-1 sm:space-x-2 px-3 sm:px-6 py-2 sm:py-3 rounded-lg font-medium transition-all duration-200 flex-1 justify-center text-sm sm:text-base ${\r\n                                        activeTab === \"class\"\r\n                                            ? 'bg-primary-600 text-white shadow-md'\r\n                                            : 'text-gray-600 hover:bg-gray-100'\r\n                                    }`}\r\n                                    onClick={() => setActiveTab(\"class\")}\r\n                                >\r\n                                    <TbSchool className=\"w-4 h-4 sm:w-5 sm:h-5\" />\r\n                                    <span className=\"hidden sm:inline\">Class Ranking</span>\r\n                                    <span className=\"sm:hidden\">Class</span>\r\n                                    {activeTab === \"class\" && (\r\n                                        <motion.div\r\n                                            layoutId=\"activeRankingTab\"\r\n                                            className=\"w-2 h-2 bg-white rounded-full\"\r\n                                        />\r\n                                    )}\r\n                                </motion.button>\r\n                            </div>\r\n                        </Card>\r\n                    </motion.div>\r\n\r\n                    {/* Pinned User Profile - Your Position */}\r\n                    {userData && (\r\n                        <motion.div\r\n                            initial={{ opacity: 0, y: 20 }}\r\n                            animate={{ opacity: 1, y: 0 }}\r\n                            transition={{ delay: 0.3 }}\r\n                            className=\"mb-6\"\r\n                        >\r\n                            <Card className=\"bg-gradient-to-r from-blue-50 to-purple-50 border-2 border-blue-200\">\r\n                                <div className=\"p-4\">\r\n                                    <div className=\"flex items-center justify-between mb-3\">\r\n                                        <h3 className=\"text-lg font-bold text-gray-800 flex items-center\">\r\n                                            <TbUser className=\"w-5 h-5 mr-2 text-blue-600\" />\r\n                                            Your Position\r\n                                        </h3>\r\n                                        <div className=\"flex items-center space-x-2\">\r\n                                            <span className=\"bg-blue-100 text-blue-800 text-xs font-bold px-2 py-1 rounded-full\">\r\n                                                #{userRanking && userRanking.length > 0 ? userRanking[0].ranking : 'N/A'}\r\n                                            </span>\r\n                                            <span className=\"bg-yellow-100 text-yellow-800 text-xs font-bold px-2 py-1 rounded-full\">\r\n                                                {userData.totalPoints || 0} pts\r\n                                            </span>\r\n                                        </div>\r\n                                    </div>\r\n\r\n                                    <div className=\"flex items-center space-x-4\">\r\n                                        {/* User's Profile Picture */}\r\n                                        <div className={`w-14 h-14 rounded-full p-0.5 flex items-center justify-center flex-shrink-0 ${\r\n                                            userData.subscriptionStatus === \"active\"\r\n                                                ? 'bg-gradient-to-r from-green-400 to-green-600'\r\n                                                : userData.subscriptionStatus === \"free\"\r\n                                                ? 'bg-gradient-to-r from-blue-400 to-blue-600'\r\n                                                : 'bg-gradient-to-r from-red-400 to-red-600'\r\n                                        }`}>\r\n                                            <div className=\"w-full h-full rounded-full overflow-hidden bg-gray-200 flex items-center justify-center\">\r\n                                                {userData.userPhoto ? (\r\n                                                    <img\r\n                                                        src={userData.userPhoto}\r\n                                                        alt=\"profile\"\r\n                                                        className=\"w-full h-full object-cover\"\r\n                                                        onError={(e) => { e.target.src = image }}\r\n                                                    />\r\n                                                ) : (\r\n                                                    <TbUser className=\"w-7 h-7 text-gray-400\" />\r\n                                                )}\r\n                                            </div>\r\n                                        </div>\r\n\r\n                                        {/* User Info */}\r\n                                        <div className=\"flex-1\">\r\n                                            <div className=\"flex items-center space-x-2 mb-1\">\r\n                                                <h4 className=\"font-bold text-gray-900\">{userData.name}</h4>\r\n                                                <span className=\"bg-gradient-to-r from-blue-500 to-purple-500 text-white text-xs px-2 py-1 rounded-full font-bold\">You</span>\r\n                                            </div>\r\n                                            <div className=\"flex items-center space-x-4 text-sm text-gray-600\">\r\n                                                <span>Class {userData.class}</span>\r\n                                                <span>•</span>\r\n                                                <span>{userData.school}</span>\r\n                                            </div>\r\n                                        </div>\r\n\r\n                                        {/* Quick Stats & Find Me Button */}\r\n                                        <div className=\"text-right\">\r\n                                            <motion.button\r\n                                                whileHover={{ scale: 1.05 }}\r\n                                                whileTap={{ scale: 0.95 }}\r\n                                                onClick={scrollToCurrentUser}\r\n                                                className=\"bg-gradient-to-r from-blue-500 to-purple-500 text-white text-xs px-3 py-1.5 rounded-full font-bold mb-2 hover:shadow-md transition-all duration-200\"\r\n                                            >\r\n                                                Find Me in List\r\n                                            </motion.button>\r\n                                            <div className=\"text-sm text-gray-600\">Performance</div>\r\n                                            <div className=\"flex items-center space-x-3 mt-1\">\r\n                                                <div className=\"text-center\">\r\n                                                    <div className=\"text-xs text-green-600 font-bold\">{userData.passedExamsCount || 0}</div>\r\n                                                    <div className=\"text-xs text-gray-500\">Passed</div>\r\n                                                </div>\r\n                                                <div className=\"text-center\">\r\n                                                    <div className=\"text-xs text-blue-600 font-bold\">{userData.quizzesTaken || 0}</div>\r\n                                                    <div className=\"text-xs text-gray-500\">Quizzes</div>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                            </Card>\r\n                        </motion.div>\r\n                    )}\r\n\r\n                    {/* Modern Leaderboard */}\r\n                    <motion.div\r\n                        initial={{ opacity: 0, y: 20 }}\r\n                        animate={{ opacity: 1, y: 0 }}\r\n                        transition={{ delay: 0.4 }}\r\n                    >\r\n                        {rankingData.length > 0 ? (\r\n                            <Card className=\"overflow-hidden\">\r\n                                {/* Enhanced Leaderboard Header - Responsive */}\r\n                                <div className=\"bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 text-white p-4 sm:p-6 relative overflow-hidden\">\r\n                                    {/* Background Pattern - Responsive */}\r\n                                    <div className=\"absolute inset-0 opacity-10\">\r\n                                        <div className=\"absolute top-2 left-2 sm:top-4 sm:left-4 text-3xl sm:text-4xl md:text-6xl\">🏆</div>\r\n                                        <div className=\"absolute top-4 right-4 sm:top-8 sm:right-8 text-2xl sm:text-3xl md:text-4xl\">⭐</div>\r\n                                        <div className=\"absolute bottom-2 left-1/4 sm:bottom-4 text-xl sm:text-2xl md:text-3xl\">🎯</div>\r\n                                        <div className=\"absolute bottom-3 right-1/3 sm:bottom-6 text-3xl sm:text-4xl md:text-5xl\">💎</div>\r\n                                    </div>\r\n\r\n                                    <div className=\"relative z-10\">\r\n                                        <div className=\"flex flex-col sm:flex-row items-center justify-center space-y-2 sm:space-y-0 sm:space-x-3 mb-3 sm:mb-2\">\r\n                                            <TbTrophy className=\"w-8 h-8 sm:w-10 sm:h-10 text-yellow-300 animate-bounce\" />\r\n                                            <h2 className=\"text-xl sm:text-2xl md:text-3xl font-black bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent text-center\">\r\n                                                {activeTab === \"overall\" ? \"Global Leaderboard\" : \"Class Champions\"}\r\n                                            </h2>\r\n                                            <TbTrophy className=\"w-8 h-8 sm:w-10 sm:h-10 text-yellow-300 animate-bounce hidden sm:block\" style={{ animationDelay: '0.5s' }} />\r\n                                        </div>\r\n                                        <p className=\"text-center text-blue-100 text-sm sm:text-base md:text-lg font-semibold\">\r\n                                            {activeTab === \"overall\"\r\n                                                ? \"🌟 Elite performers from all classes competing for glory! 🌟\"\r\n                                                : `🎓 Class ${userData?.class || 'your class'} top achievers! 🎓`\r\n                                            }\r\n                                        </p>\r\n                                        <div className=\"text-center mt-3\">\r\n                                            <span className=\"bg-white/20 px-3 py-2 sm:px-4 rounded-full text-xs sm:text-sm font-bold block sm:inline\">\r\n                                                💪 Earn points by acing quizzes • 🏅 Climb the ranks • 👑 Become a champion!\r\n                                            </span>\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n\r\n                                {/* Top 3 Podium - Responsive */}\r\n                                <div className=\"bg-gradient-to-br from-blue-50 to-purple-50 p-3 sm:p-6 border-b\">\r\n                                    <h3 className=\"text-lg sm:text-xl font-bold text-center mb-4 sm:mb-6 text-gray-800\">🏆 Champions Podium 🏆</h3>\r\n                                    <div className=\"flex justify-center items-end space-x-2 sm:space-x-4 overflow-x-auto pb-2\">\r\n                                        {(activeTab === \"overall\"\r\n                                            ? rankingData.slice(0, 3)\r\n                                            : rankingData.filter(user => user.userClass === userData?.class).slice(0, 3)\r\n                                        ).map((user, index) => {\r\n                                            const positions = [1, 0, 2]; // Silver, Diamond, Bronze order for visual appeal\r\n                                            const actualPosition = positions.indexOf(index);\r\n                                            const heights = ['h-16 sm:h-20 md:h-24', 'h-20 sm:h-24 md:h-32', 'h-12 sm:h-16 md:h-20']; // Responsive heights for podium effect\r\n                                            const badges = [\r\n                                                { icon: \"🥈\", color: \"from-gray-400 to-gray-600\", title: \"Silver\" },\r\n                                                { icon: \"💎\", color: \"from-blue-400 to-cyan-600\", title: \"Diamond\" },\r\n                                                { icon: \"🥉\", color: \"from-amber-400 to-orange-600\", title: \"Bronze\" }\r\n                                            ];\r\n\r\n                                            return (\r\n                                                <motion.div\r\n                                                    key={user.userId}\r\n                                                    initial={{ opacity: 0, y: 50 }}\r\n                                                    animate={{ opacity: 1, y: 0 }}\r\n                                                    transition={{ delay: index * 0.2 }}\r\n                                                    className=\"text-center\"\r\n                                                >\r\n                                                    {/* WhatsApp-style Podium Avatar */}\r\n                                                    <div className=\"mb-2 sm:mb-3\">\r\n                                                        <div className={`w-16 h-16 mx-auto rounded-full p-1 shadow-lg ${\r\n                                                            user.subscriptionStatus === \"active\"\r\n                                                                ? 'bg-gradient-to-r from-green-400 to-green-600'\r\n                                                                : user.subscriptionStatus === \"free\"\r\n                                                                ? 'bg-gradient-to-r from-blue-400 to-blue-600'\r\n                                                                : 'bg-gradient-to-r from-red-400 to-red-600'\r\n                                                        }`}>\r\n                                                            <div className=\"w-full h-full rounded-full overflow-hidden bg-gray-200 flex items-center justify-center\">\r\n                                                                {user.userPhoto ? (\r\n                                                                    <img src={user.userPhoto} alt=\"profile\" className=\"w-full h-full object-cover\" />\r\n                                                                ) : (\r\n                                                                    <div className=\"w-full h-full bg-gray-200 flex items-center justify-center\">\r\n                                                                        <TbUser className=\"w-8 h-8 text-gray-400\" />\r\n                                                                    </div>\r\n                                                                )}\r\n                                                            </div>\r\n                                                        </div>\r\n                                                        <div className=\"text-lg sm:text-xl md:text-2xl mt-1 sm:mt-2\">{badges[index].icon}</div>\r\n                                                    </div>\r\n\r\n                                                    {/* Podium - Responsive */}\r\n                                                    <div className={`${heights[index]} w-16 sm:w-18 md:w-20 bg-gradient-to-t ${badges[index].color} rounded-t-lg flex flex-col justify-end p-1 sm:p-2 shadow-lg`}>\r\n                                                        <div className=\"text-white text-center\">\r\n                                                            <div className=\"font-bold text-sm sm:text-base md:text-lg\">{actualPosition + 1}</div>\r\n                                                            <div className=\"text-xs hidden sm:block\">{badges[index].title}</div>\r\n                                                        </div>\r\n                                                    </div>\r\n\r\n                                                    {/* User Info - Responsive */}\r\n                                                    <div className=\"mt-1 sm:mt-2\">\r\n                                                        <div className=\"font-bold text-xs sm:text-sm truncate w-16 sm:w-18 md:w-20\">{user.userName}</div>\r\n                                                        <div className=\"text-xs text-yellow-600 font-semibold\">{user.totalPoints || 0} pts</div>\r\n                                                        <div className={`text-xs font-bold mt-1 ${\r\n                                                            user.subscriptionStatus === \"active\" ? 'text-green-600' :\r\n                                                            user.subscriptionStatus === \"free\" ? 'text-blue-600' : 'text-red-600'\r\n                                                        }`}>\r\n                                                            {user.subscriptionStatus === \"active\" ? \"Premium\" :\r\n                                                             user.subscriptionStatus === \"free\" ? \"Free\" : \"Expired\"}\r\n                                                        </div>\r\n                                                    </div>\r\n                                                </motion.div>\r\n                                            );\r\n                                        })}\r\n                                    </div>\r\n                                </div>\r\n\r\n                                {/* Leaderboard Content - Responsive */}\r\n                                <div className=\"p-3 sm:p-4 md:p-6\">\r\n                                    <div className=\"space-y-3 sm:space-y-4\">\r\n                                        {(activeTab === \"overall\"\r\n                                            ? rankingData\r\n                                            : rankingData.filter(user => user.userClass === userData?.class)\r\n                                        ).map((user, index) => {\r\n                                            const isCurrentUser = user.userId.includes(userData?._id);\r\n                                            const getRankBadge = (position) => {\r\n                                                if (position === 0) return {\r\n                                                    icon: \"💎\",\r\n                                                    color: \"text-blue-600\",\r\n                                                    bg: \"bg-gradient-to-br from-blue-100 to-cyan-100\",\r\n                                                    border: \"border-blue-300\",\r\n                                                    title: \"Diamond\",\r\n                                                    glow: \"shadow-blue-200\"\r\n                                                };\r\n                                                if (position === 1) return {\r\n                                                    icon: \"🥈\",\r\n                                                    color: \"text-gray-600\",\r\n                                                    bg: \"bg-gradient-to-br from-gray-100 to-slate-100\",\r\n                                                    border: \"border-gray-300\",\r\n                                                    title: \"Silver\",\r\n                                                    glow: \"shadow-gray-200\"\r\n                                                };\r\n                                                if (position === 2) return {\r\n                                                    icon: \"🥉\",\r\n                                                    color: \"text-amber-600\",\r\n                                                    bg: \"bg-gradient-to-br from-amber-100 to-orange-100\",\r\n                                                    border: \"border-amber-300\",\r\n                                                    title: \"Bronze\",\r\n                                                    glow: \"shadow-amber-200\"\r\n                                                };\r\n                                                return {\r\n                                                    icon: position + 1,\r\n                                                    color: \"text-gray-600\",\r\n                                                    bg: \"bg-gray-50\",\r\n                                                    border: \"border-gray-200\",\r\n                                                    title: `Rank ${position + 1}`,\r\n                                                    glow: \"shadow-gray-100\"\r\n                                                };\r\n                                            };\r\n\r\n                                            const rankInfo = getRankBadge(index);\r\n\r\n                                            return (\r\n                                                <motion.div\r\n                                                    key={user.userId}\r\n                                                    ref={isCurrentUser ? currentUserRef : null}\r\n                                                    initial={{ opacity: 0, x: -20 }}\r\n                                                    animate={{ opacity: 1, x: 0 }}\r\n                                                    transition={{ delay: index * 0.1 }}\r\n                                                    className={`flex items-center space-x-2 sm:space-x-3 md:space-x-4 p-3 sm:p-4 rounded-xl transition-all duration-200 ${\r\n                                                        isCurrentUser\r\n                                                            ? 'bg-gradient-to-r from-blue-50 to-purple-50 border-2 border-blue-300 shadow-lg ring-2 ring-blue-200 ring-opacity-50 transform scale-[1.02]'\r\n                                                            : user.subscriptionStatus === \"expired\"\r\n                                                            ? 'bg-gray-100 border border-gray-300 opacity-75'\r\n                                                            : user.subscriptionStatus === \"active\"\r\n                                                            ? 'bg-green-50 border border-green-200'\r\n                                                            : 'bg-gray-50 hover:bg-gray-100'\r\n                                                    }`}\r\n                                                >\r\n                                                    {/* Enhanced Rank Badge - Responsive */}\r\n                                                    <div className={`relative w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 rounded-full flex items-center justify-center ${rankInfo.bg} ${rankInfo.border} border-2 ${rankInfo.glow} shadow-lg transition-all duration-300 hover:scale-110 flex-shrink-0`}>\r\n                                                        {index < 3 ? (\r\n                                                            <div className=\"text-center\">\r\n                                                                <div className=\"text-lg sm:text-xl md:text-2xl mb-0.5 sm:mb-1\">{rankInfo.icon}</div>\r\n                                                                <div className={`text-xs font-bold ${rankInfo.color} hidden sm:block`}>{rankInfo.title}</div>\r\n                                                            </div>\r\n                                                        ) : (\r\n                                                            <div className=\"text-center\">\r\n                                                                <span className=\"font-black text-sm sm:text-base md:text-lg text-gray-700\">{rankInfo.icon}</span>\r\n                                                                <div className=\"text-xs font-semibold text-gray-500 hidden sm:block\">Rank</div>\r\n                                                            </div>\r\n                                                        )}\r\n\r\n                                                        {/* Glow effect for top 3 */}\r\n                                                        {index < 3 && (\r\n                                                            <div className={`absolute inset-0 rounded-full ${rankInfo.bg} opacity-50 blur-md -z-10 animate-pulse`}></div>\r\n                                                        )}\r\n                                                    </div>\r\n\r\n                                                    {/* WhatsApp-style Profile Picture */}\r\n                                                    <div className={`w-12 h-12 rounded-full p-0.5 flex items-center justify-center flex-shrink-0 ${\r\n                                                        user.subscriptionStatus === \"active\"\r\n                                                            ? 'bg-gradient-to-r from-green-400 to-green-600'\r\n                                                            : user.subscriptionStatus === \"free\"\r\n                                                            ? 'bg-gradient-to-r from-blue-400 to-blue-600'\r\n                                                            : 'bg-gradient-to-r from-red-400 to-red-600'\r\n                                                    }`}>\r\n                                                        <div className=\"w-full h-full rounded-full overflow-hidden bg-gray-200 flex items-center justify-center\">\r\n                                                            {user.userPhoto ? (\r\n                                                                <img\r\n                                                                    src={user.userPhoto}\r\n                                                                    alt=\"profile\"\r\n                                                                    className=\"w-full h-full object-cover\"\r\n                                                                    onError={(e) => { e.target.src = image }}\r\n                                                                />\r\n                                                            ) : (\r\n                                                                <TbUser className=\"w-6 h-6 text-gray-400\" />\r\n                                                            )}\r\n                                                        </div>\r\n                                                    </div>\r\n\r\n                                                    {/* Enhanced User Info - Responsive */}\r\n                                                    <div className=\"flex-1 min-w-0\">\r\n                                                        <div className=\"flex flex-col sm:flex-row sm:items-center space-y-1 sm:space-y-0 sm:space-x-2 mb-2\">\r\n                                                            <h3 className={`font-bold text-sm sm:text-base md:text-lg truncate ${\r\n                                                                isCurrentUser ? 'text-primary-900' : 'text-gray-900'\r\n                                                            }`}>\r\n                                                                {user.userName}\r\n                                                            </h3>\r\n                                                            <div className=\"flex items-center space-x-1 sm:space-x-2\">\r\n                                                                {isCurrentUser && (\r\n                                                                    <span className=\"bg-gradient-to-r from-blue-500 to-purple-500 text-white text-xs px-2 py-1 rounded-full font-bold\">You</span>\r\n                                                                )}\r\n                                                                {index < 3 && (\r\n                                                                    <span className={`text-xs px-2 py-1 rounded-full font-bold hidden sm:inline ${\r\n                                                                        index === 0 ? 'bg-blue-100 text-blue-800' :\r\n                                                                        index === 1 ? 'bg-gray-100 text-gray-800' :\r\n                                                                        'bg-amber-100 text-amber-800'\r\n                                                                    }`}>\r\n                                                                        {rankInfo.title} Champion\r\n                                                                    </span>\r\n                                                                )}\r\n                                                                {/* Subscription Status Badge */}\r\n                                                                <span className={`text-xs px-2 py-1 rounded-full font-bold ${\r\n                                                                    user.subscriptionStatus === \"active\"\r\n                                                                        ? 'bg-green-100 text-green-800'\r\n                                                                        : user.subscriptionStatus === \"free\"\r\n                                                                        ? 'bg-blue-100 text-blue-800'\r\n                                                                        : 'bg-red-100 text-red-800'\r\n                                                                }`}>\r\n                                                                    {user.subscriptionStatus === \"active\" ? \"Premium\" :\r\n                                                                     user.subscriptionStatus === \"free\" ? \"Free\" : \"Expired\"}\r\n                                                                </span>\r\n                                                            </div>\r\n                                                        </div>\r\n\r\n                                                        {/* Points and Stats - Responsive */}\r\n                                                        <div className=\"flex flex-wrap items-center gap-2 sm:gap-3 md:gap-4 mb-2\">\r\n                                                            <div className=\"flex items-center space-x-1\">\r\n                                                                <div className=\"w-2 h-2 bg-yellow-400 rounded-full\"></div>\r\n                                                                <span className=\"font-bold text-yellow-600 text-xs sm:text-sm\">{user.totalPoints || 0} pts</span>\r\n                                                            </div>\r\n                                                            <div className=\"flex items-center space-x-1\">\r\n                                                                <div className=\"w-2 h-2 bg-green-400 rounded-full\"></div>\r\n                                                                <span className=\"font-semibold text-green-600 text-xs sm:text-sm\">{user.passedExamsCount || 0} passed</span>\r\n                                                            </div>\r\n                                                            <div className=\"flex items-center space-x-1\">\r\n                                                                <div className=\"w-2 h-2 bg-blue-400 rounded-full\"></div>\r\n                                                                <span className=\"font-semibold text-blue-600 text-xs sm:text-sm\">{user.quizzesTaken || 0} quizzes</span>\r\n                                                            </div>\r\n                                                        </div>\r\n\r\n                                                        <div className=\"flex flex-col sm:flex-row sm:items-center space-y-1 sm:space-y-0 sm:space-x-4 text-xs sm:text-sm text-gray-600\">\r\n                                                            <div className=\"flex items-center space-x-1\">\r\n                                                                <TbSchool className=\"w-3 h-3 sm:w-4 sm:h-4\" />\r\n                                                                <span className=\"truncate\">{user.userSchool || 'Not Enrolled'}</span>\r\n                                                            </div>\r\n                                                            <div className=\"flex items-center space-x-1\">\r\n                                                                <TbUsers className=\"w-4 h-4\" />\r\n                                                                <span>{user.userClass || 'Not Enrolled'}</span>\r\n                                                            </div>\r\n                                                        </div>\r\n                                                    </div>\r\n\r\n                                                    {/* Score */}\r\n                                                    <div className=\"text-right\">\r\n                                                        <div className={`text-2xl font-bold ${\r\n                                                            isCurrentUser ? 'text-primary-600' : 'text-gray-900'\r\n                                                        }`}>\r\n                                                            {user.score}\r\n                                                        </div>\r\n                                                        <div className=\"text-xs text-gray-500\">points</div>\r\n                                                    </div>\r\n                                                </motion.div>\r\n                                            );\r\n                                        })}\r\n                                    </div>\r\n                                </div>\r\n                            </Card>\r\n                        ) : (\r\n                            <Card className=\"p-12 text-center\">\r\n                                <TbChartBar className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\r\n                                <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">No Rankings Yet</h3>\r\n                                <p className=\"text-gray-600\">\r\n                                    Complete some quizzes to see your ranking on the leaderboard!\r\n                                </p>\r\n                            </Card>\r\n                        )}\r\n                    </motion.div>\r\n\r\n                    {/* Floating Action Button - Find Me */}\r\n                    {userData && (\r\n                        <motion.button\r\n                            initial={{ opacity: 0, scale: 0 }}\r\n                            animate={{ opacity: 1, scale: 1 }}\r\n                            transition={{ delay: 1 }}\r\n                            whileHover={{ scale: 1.1 }}\r\n                            whileTap={{ scale: 0.9 }}\r\n                            onClick={scrollToCurrentUser}\r\n                            className=\"fixed bottom-6 right-6 bg-gradient-to-r from-blue-500 to-purple-500 text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 z-50\"\r\n                            title=\"Find me in ranking\"\r\n                        >\r\n                            <TbUser className=\"w-6 h-6\" />\r\n                        </motion.button>\r\n                    )}\r\n\r\n                </div>\r\n            )}\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default Ranking;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,uBAAuB,QAAQ,2BAA2B;AACnE,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,OAAOC,eAAe,MAAM,4CAA4C;AACxE,SAASC,QAAQ,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzE,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACoB,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACsB,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0B,QAAQ,EAAEC,WAAW,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC4B,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;EACvD,MAAM8B,cAAc,GAAGC,MAAM,CAAC,IAAI,CAAC;EAEnC,MAAMC,QAAQ,GAAG3B,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM4B,mBAAmB,GAAGA,CAAA,KAAM;IAC9B,IAAIH,cAAc,CAACI,OAAO,EAAE;MACxBJ,cAAc,CAACI,OAAO,CAACC,cAAc,CAAC;QAClCC,QAAQ,EAAE,QAAQ;QAClBC,KAAK,EAAE;MACX,CAAC,CAAC;IACN;EACJ,CAAC;EAED,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMrC,uBAAuB,CAAC,CAAC;MAChD,IAAIqC,QAAQ,CAACC,OAAO,EAAE;QAClBrB,cAAc,CAACoB,QAAQ,CAACE,IAAI,CAAC;MACjC,CAAC,MAAM;QACHrC,OAAO,CAACsC,KAAK,CAACH,QAAQ,CAACnC,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAOsC,KAAK,EAAE;MACZtC,OAAO,CAACsC,KAAK,CAACA,KAAK,CAACtC,OAAO,CAAC;IAChC;EACJ,CAAC;EAGD,MAAMuC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACA,MAAMJ,QAAQ,GAAG,MAAMpC,WAAW,CAAC,CAAC;MACpC,IAAIoC,QAAQ,CAACC,OAAO,EAAE;QAClB,IAAID,QAAQ,CAACE,IAAI,CAACjB,OAAO,EAAE;UACvBC,UAAU,CAAC,IAAI,CAAC;QACpB,CAAC,MAAM;UACHA,UAAU,CAAC,KAAK,CAAC;UACjBF,WAAW,CAACgB,QAAQ,CAACE,IAAI,CAAC;UAC1B,MAAMH,YAAY,CAAC,CAAC;UACpBN,QAAQ,CAACzB,WAAW,CAAC,CAAC,CAAC;QAC3B;MACJ,CAAC,MAAM;QACHH,OAAO,CAACsC,KAAK,CAACH,QAAQ,CAACnC,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAOsC,KAAK,EAAE;MACZtC,OAAO,CAACsC,KAAK,CAACA,KAAK,CAACtC,OAAO,CAAC;IAChC;EACJ,CAAC;EAEDL,SAAS,CAAC,MAAM;IACZ,IAAI6C,MAAM,CAACC,UAAU,GAAG,GAAG,EAAE;MACzBlB,WAAW,CAAC,IAAI,CAAC;IACrB,CAAC,MACI;MACDA,WAAW,CAAC,KAAK,CAAC;IACtB;IACA,IAAImB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE;MAC/Bf,QAAQ,CAACxB,WAAW,CAAC,CAAC,CAAC;MACvBmC,WAAW,CAAC,CAAC;IACjB;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,YAAY,GAAGA,CAAA,KAAM;IACvB,MAAMhC,OAAO,GAAGE,WAAW,CACtB+B,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,MAAM;MACnBD,IAAI;MACJE,OAAO,EAAED,KAAK,GAAG;IACrB,CAAC,CAAC,CAAC,CACFE,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACJ,IAAI,CAACK,MAAM,CAACC,QAAQ,CAAClC,QAAQ,CAACmC,GAAG,CAAC,CAAC;IAC9DpC,cAAc,CAACL,OAAO,CAAC;EAC3B,CAAC;EAEDjB,SAAS,CAAC,MAAM;IACZ,IAAImB,WAAW,EAAE;MACb8B,YAAY,CAAC,CAAC;IAClB;EACJ,CAAC,EAAE,CAAC9B,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAMwC,kBAAkB,GAAIH,MAAM,IAAK;IACnC,MAAMI,MAAM,GAAGJ,MAAM,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IACjC,MAAMC,MAAM,GAAGN,MAAM,CAACK,KAAK,CAAC,CAAC,CAAC,CAAC;IAC/B,OAAQ,GAAED,MAAO,QAAOE,MAAO,EAAC;EACpC,CAAC;EAED,oBACI9C,OAAA;IAAK+C,SAAS,EAAC,wDAAwD;IAAAC,QAAA,EAClE,CAACvC,OAAO,iBACLT,OAAA;MAAK+C,SAAS,EAAC,uBAAuB;MAAAC,QAAA,gBAElChD,OAAA,CAACd,MAAM,CAAC+D,GAAG;QACPC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BL,SAAS,EAAC,0BAA0B;QAAAC,QAAA,gBAEpChD,OAAA;UAAK+C,SAAS,EAAC,4JAA4J;UAAAC,QAAA,eACvKhD,OAAA,CAACL,QAAQ;YAACoD,SAAS,EAAC;UAAgD;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE,CAAC,eACNzD,OAAA;UAAI+C,SAAS,EAAC,uEAAuE;UAAAC,QAAA,EAAC;QAAW;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtGzD,OAAA;UAAG+C,SAAS,EAAC,+CAA+C;UAAAC,QAAA,EAAC;QAE7D;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGbzD,OAAA,CAACd,MAAM,CAAC+D,GAAG;QACPC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BM,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3BZ,SAAS,EAAC,cAAc;QAAAC,QAAA,eAExBhD,OAAA,CAAC4D,IAAI;UAACb,SAAS,EAAC,KAAK;UAAAC,QAAA,eACjBhD,OAAA;YAAK+C,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACvBhD,OAAA,CAACd,MAAM,CAAC2E,MAAM;cACVC,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAC1BhB,SAAS,EAAG,oKACRlC,SAAS,KAAK,SAAS,GACjB,qCAAqC,GACrC,iCACT,EAAE;cACHoD,OAAO,EAAEA,CAAA,KAAMnD,YAAY,CAAC,SAAS,CAAE;cAAAkC,QAAA,gBAEvChD,OAAA,CAACJ,OAAO;gBAACmD,SAAS,EAAC;cAAuB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7CzD,OAAA;gBAAM+C,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAC;cAAe;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzDzD,OAAA;gBAAM+C,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAC;cAAO;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EACzC5C,SAAS,KAAK,SAAS,iBACpBb,OAAA,CAACd,MAAM,CAAC+D,GAAG;gBACPiB,QAAQ,EAAC,kBAAkB;gBAC3BnB,SAAS,EAAC;cAA+B;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU,CAAC,eAChBzD,OAAA,CAACd,MAAM,CAAC2E,MAAM;cACVC,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAC1BhB,SAAS,EAAG,oKACRlC,SAAS,KAAK,OAAO,GACf,qCAAqC,GACrC,iCACT,EAAE;cACHoD,OAAO,EAAEA,CAAA,KAAMnD,YAAY,CAAC,OAAO,CAAE;cAAAkC,QAAA,gBAErChD,OAAA,CAACH,QAAQ;gBAACkD,SAAS,EAAC;cAAuB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9CzD,OAAA;gBAAM+C,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAC;cAAa;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvDzD,OAAA;gBAAM+C,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAC;cAAK;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EACvC5C,SAAS,KAAK,OAAO,iBAClBb,OAAA,CAACd,MAAM,CAAC+D,GAAG;gBACPiB,QAAQ,EAAC,kBAAkB;gBAC3BnB,SAAS,EAAC;cAA+B;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAGZlD,QAAQ,iBACLP,OAAA,CAACd,MAAM,CAAC+D,GAAG;QACPC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BM,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3BZ,SAAS,EAAC,MAAM;QAAAC,QAAA,eAEhBhD,OAAA,CAAC4D,IAAI;UAACb,SAAS,EAAC,qEAAqE;UAAAC,QAAA,eACjFhD,OAAA;YAAK+C,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAChBhD,OAAA;cAAK+C,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACnDhD,OAAA;gBAAI+C,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,gBAC7DhD,OAAA,CAACmE,MAAM;kBAACpB,SAAS,EAAC;gBAA4B;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,iBAErD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLzD,OAAA;gBAAK+C,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBACxChD,OAAA;kBAAM+C,SAAS,EAAC,oEAAoE;kBAAAC,QAAA,GAAC,GAChF,EAAC3C,WAAW,IAAIA,WAAW,CAAC+D,MAAM,GAAG,CAAC,GAAG/D,WAAW,CAAC,CAAC,CAAC,CAACgC,OAAO,GAAG,KAAK;gBAAA;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE,CAAC,eACPzD,OAAA;kBAAM+C,SAAS,EAAC,wEAAwE;kBAAAC,QAAA,GACnFzC,QAAQ,CAAC8D,WAAW,IAAI,CAAC,EAAC,MAC/B;gBAAA;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENzD,OAAA;cAAK+C,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAExChD,OAAA;gBAAK+C,SAAS,EAAG,+EACbxC,QAAQ,CAAC+D,kBAAkB,KAAK,QAAQ,GAClC,8CAA8C,GAC9C/D,QAAQ,CAAC+D,kBAAkB,KAAK,MAAM,GACtC,4CAA4C,GAC5C,0CACT,EAAE;gBAAAtB,QAAA,eACChD,OAAA;kBAAK+C,SAAS,EAAC,yFAAyF;kBAAAC,QAAA,EACnGzC,QAAQ,CAACgE,SAAS,gBACfvE,OAAA;oBACIwE,GAAG,EAAEjE,QAAQ,CAACgE,SAAU;oBACxBE,GAAG,EAAC,SAAS;oBACb1B,SAAS,EAAC,4BAA4B;oBACtC2B,OAAO,EAAGC,CAAC,IAAK;sBAAEA,CAAC,CAACC,MAAM,CAACJ,GAAG,GAAGK,KAAK;oBAAC;kBAAE;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC,gBAEFzD,OAAA,CAACmE,MAAM;oBAACpB,SAAS,EAAC;kBAAuB;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAC9C;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAGNzD,OAAA;gBAAK+C,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACnBhD,OAAA;kBAAK+C,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,gBAC7ChD,OAAA;oBAAI+C,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,EAAEzC,QAAQ,CAACuE;kBAAI;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC5DzD,OAAA;oBAAM+C,SAAS,EAAC,kGAAkG;oBAAAC,QAAA,EAAC;kBAAG;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5H,CAAC,eACNzD,OAAA;kBAAK+C,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,gBAC9DhD,OAAA;oBAAAgD,QAAA,GAAM,QAAM,EAACzC,QAAQ,CAACwE,KAAK;kBAAA;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACnCzD,OAAA;oBAAAgD,QAAA,EAAM;kBAAC;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACdzD,OAAA;oBAAAgD,QAAA,EAAOzC,QAAQ,CAACyE;kBAAM;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAGNzD,OAAA;gBAAK+C,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACvBhD,OAAA,CAACd,MAAM,CAAC2E,MAAM;kBACVC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAE/C,mBAAoB;kBAC7B6B,SAAS,EAAC,qJAAqJ;kBAAAC,QAAA,EAClK;gBAED;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC,eAChBzD,OAAA;kBAAK+C,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAW;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACxDzD,OAAA;kBAAK+C,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,gBAC7ChD,OAAA;oBAAK+C,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBACxBhD,OAAA;sBAAK+C,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAEzC,QAAQ,CAAC0E,gBAAgB,IAAI;oBAAC;sBAAA3B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACxFzD,OAAA;sBAAK+C,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAM;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC,eACNzD,OAAA;oBAAK+C,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBACxBhD,OAAA;sBAAK+C,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAEzC,QAAQ,CAAC2E,YAAY,IAAI;oBAAC;sBAAA5B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACnFzD,OAAA;sBAAK+C,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAO;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACf,eAGDzD,OAAA,CAACd,MAAM,CAAC+D,GAAG;QACPC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BM,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAAX,QAAA,EAE1B7C,WAAW,CAACiE,MAAM,GAAG,CAAC,gBACnBpE,OAAA,CAAC4D,IAAI;UAACb,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAE7BhD,OAAA;YAAK+C,SAAS,EAAC,4GAA4G;YAAAC,QAAA,gBAEvHhD,OAAA;cAAK+C,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBACxChD,OAAA;gBAAK+C,SAAS,EAAC,2EAA2E;gBAAAC,QAAA,EAAC;cAAE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACnGzD,OAAA;gBAAK+C,SAAS,EAAC,6EAA6E;gBAAAC,QAAA,EAAC;cAAC;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpGzD,OAAA;gBAAK+C,SAAS,EAAC,wEAAwE;gBAAAC,QAAA,EAAC;cAAE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChGzD,OAAA;gBAAK+C,SAAS,EAAC,0EAA0E;gBAAAC,QAAA,EAAC;cAAE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjG,CAAC,eAENzD,OAAA;cAAK+C,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC1BhD,OAAA;gBAAK+C,SAAS,EAAC,wGAAwG;gBAAAC,QAAA,gBACnHhD,OAAA,CAACL,QAAQ;kBAACoD,SAAS,EAAC;gBAAwD;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC/EzD,OAAA;kBAAI+C,SAAS,EAAC,qIAAqI;kBAAAC,QAAA,EAC9InC,SAAS,KAAK,SAAS,GAAG,oBAAoB,GAAG;gBAAiB;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CAAC,eACLzD,OAAA,CAACL,QAAQ;kBAACoD,SAAS,EAAC,wEAAwE;kBAACoC,KAAK,EAAE;oBAAEC,cAAc,EAAE;kBAAO;gBAAE;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjI,CAAC,eACNzD,OAAA;gBAAG+C,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,EACjFnC,SAAS,KAAK,SAAS,GAClB,8DAA8D,GAC7D,YAAW,CAAAN,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEwE,KAAK,KAAI,YAAa;cAAmB;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEtE,CAAC,eACJzD,OAAA;gBAAK+C,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,eAC7BhD,OAAA;kBAAM+C,SAAS,EAAC,yFAAyF;kBAAAC,QAAA,EAAC;gBAE1G;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGNzD,OAAA;YAAK+C,SAAS,EAAC,iEAAiE;YAAAC,QAAA,gBAC5EhD,OAAA;cAAI+C,SAAS,EAAC,qEAAqE;cAAAC,QAAA,EAAC;YAAsB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/GzD,OAAA;cAAK+C,SAAS,EAAC,2EAA2E;cAAAC,QAAA,EACrF,CAACnC,SAAS,KAAK,SAAS,GACnBV,WAAW,CAAC0C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GACvB1C,WAAW,CAACmC,MAAM,CAACH,IAAI,IAAIA,IAAI,CAACkD,SAAS,MAAK9E,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEwE,KAAK,EAAC,CAAClC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAC9EX,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;gBACnB,MAAMkD,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBAC7B,MAAMC,cAAc,GAAGD,SAAS,CAACE,OAAO,CAACpD,KAAK,CAAC;gBAC/C,MAAMqD,OAAO,GAAG,CAAC,sBAAsB,EAAE,sBAAsB,EAAE,sBAAsB,CAAC,CAAC,CAAC;gBAC1F,MAAMC,MAAM,GAAG,CACX;kBAAEC,IAAI,EAAE,IAAI;kBAAEC,KAAK,EAAE,2BAA2B;kBAAEC,KAAK,EAAE;gBAAS,CAAC,EACnE;kBAAEF,IAAI,EAAE,IAAI;kBAAEC,KAAK,EAAE,2BAA2B;kBAAEC,KAAK,EAAE;gBAAU,CAAC,EACpE;kBAAEF,IAAI,EAAE,IAAI;kBAAEC,KAAK,EAAE,8BAA8B;kBAAEC,KAAK,EAAE;gBAAS,CAAC,CACzE;gBAED,oBACI7F,OAAA,CAACd,MAAM,CAAC+D,GAAG;kBAEPC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAG,CAAE;kBAC/BC,OAAO,EAAE;oBAAEF,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAE,CAAE;kBAC9BM,UAAU,EAAE;oBAAEC,KAAK,EAAEvB,KAAK,GAAG;kBAAI,CAAE;kBACnCW,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAGvBhD,OAAA;oBAAK+C,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBACzBhD,OAAA;sBAAK+C,SAAS,EAAG,gDACbZ,IAAI,CAACmC,kBAAkB,KAAK,QAAQ,GAC9B,8CAA8C,GAC9CnC,IAAI,CAACmC,kBAAkB,KAAK,MAAM,GAClC,4CAA4C,GAC5C,0CACT,EAAE;sBAAAtB,QAAA,eACChD,OAAA;wBAAK+C,SAAS,EAAC,yFAAyF;wBAAAC,QAAA,EACnGb,IAAI,CAACoC,SAAS,gBACXvE,OAAA;0BAAKwE,GAAG,EAAErC,IAAI,CAACoC,SAAU;0BAACE,GAAG,EAAC,SAAS;0BAAC1B,SAAS,EAAC;wBAA4B;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,gBAEjFzD,OAAA;0BAAK+C,SAAS,EAAC,4DAA4D;0BAAAC,QAAA,eACvEhD,OAAA,CAACmE,MAAM;4BAACpB,SAAS,EAAC;0BAAuB;4BAAAO,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC3C;sBACR;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACNzD,OAAA;sBAAK+C,SAAS,EAAC,6CAA6C;sBAAAC,QAAA,EAAE0C,MAAM,CAACtD,KAAK,CAAC,CAACuD;oBAAI;sBAAArC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtF,CAAC,eAGNzD,OAAA;oBAAK+C,SAAS,EAAG,GAAE0C,OAAO,CAACrD,KAAK,CAAE,0CAAyCsD,MAAM,CAACtD,KAAK,CAAC,CAACwD,KAAM,8DAA8D;oBAAA5C,QAAA,eACzJhD,OAAA;sBAAK+C,SAAS,EAAC,wBAAwB;sBAAAC,QAAA,gBACnChD,OAAA;wBAAK+C,SAAS,EAAC,2CAA2C;wBAAAC,QAAA,EAAEuC,cAAc,GAAG;sBAAC;wBAAAjC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACrFzD,OAAA;wBAAK+C,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EAAE0C,MAAM,CAACtD,KAAK,CAAC,CAACyD;sBAAK;wBAAAvC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eAGNzD,OAAA;oBAAK+C,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBACzBhD,OAAA;sBAAK+C,SAAS,EAAC,4DAA4D;sBAAAC,QAAA,EAAEb,IAAI,CAAC2D;oBAAQ;sBAAAxC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjGzD,OAAA;sBAAK+C,SAAS,EAAC,uCAAuC;sBAAAC,QAAA,GAAEb,IAAI,CAACkC,WAAW,IAAI,CAAC,EAAC,MAAI;oBAAA;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACxFzD,OAAA;sBAAK+C,SAAS,EAAG,0BACbZ,IAAI,CAACmC,kBAAkB,KAAK,QAAQ,GAAG,gBAAgB,GACvDnC,IAAI,CAACmC,kBAAkB,KAAK,MAAM,GAAG,eAAe,GAAG,cAC1D,EAAE;sBAAAtB,QAAA,EACEb,IAAI,CAACmC,kBAAkB,KAAK,QAAQ,GAAG,SAAS,GAChDnC,IAAI,CAACmC,kBAAkB,KAAK,MAAM,GAAG,MAAM,GAAG;oBAAS;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA,GA/CDtB,IAAI,CAACK,MAAM;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAgDR,CAAC;cAErB,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGNzD,OAAA;YAAK+C,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAC9BhD,OAAA;cAAK+C,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAClC,CAACnC,SAAS,KAAK,SAAS,GACnBV,WAAW,GACXA,WAAW,CAACmC,MAAM,CAACH,IAAI,IAAIA,IAAI,CAACkD,SAAS,MAAK9E,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEwE,KAAK,EAAC,EAClE7C,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;gBACnB,MAAM2D,aAAa,GAAG5D,IAAI,CAACK,MAAM,CAACC,QAAQ,CAAClC,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEmC,GAAG,CAAC;gBACzD,MAAMsD,YAAY,GAAIC,QAAQ,IAAK;kBAC/B,IAAIA,QAAQ,KAAK,CAAC,EAAE,OAAO;oBACvBN,IAAI,EAAE,IAAI;oBACVC,KAAK,EAAE,eAAe;oBACtBM,EAAE,EAAE,6CAA6C;oBACjDC,MAAM,EAAE,iBAAiB;oBACzBN,KAAK,EAAE,SAAS;oBAChBO,IAAI,EAAE;kBACV,CAAC;kBACD,IAAIH,QAAQ,KAAK,CAAC,EAAE,OAAO;oBACvBN,IAAI,EAAE,IAAI;oBACVC,KAAK,EAAE,eAAe;oBACtBM,EAAE,EAAE,8CAA8C;oBAClDC,MAAM,EAAE,iBAAiB;oBACzBN,KAAK,EAAE,QAAQ;oBACfO,IAAI,EAAE;kBACV,CAAC;kBACD,IAAIH,QAAQ,KAAK,CAAC,EAAE,OAAO;oBACvBN,IAAI,EAAE,IAAI;oBACVC,KAAK,EAAE,gBAAgB;oBACvBM,EAAE,EAAE,gDAAgD;oBACpDC,MAAM,EAAE,kBAAkB;oBAC1BN,KAAK,EAAE,QAAQ;oBACfO,IAAI,EAAE;kBACV,CAAC;kBACD,OAAO;oBACHT,IAAI,EAAEM,QAAQ,GAAG,CAAC;oBAClBL,KAAK,EAAE,eAAe;oBACtBM,EAAE,EAAE,YAAY;oBAChBC,MAAM,EAAE,iBAAiB;oBACzBN,KAAK,EAAG,QAAOI,QAAQ,GAAG,CAAE,EAAC;oBAC7BG,IAAI,EAAE;kBACV,CAAC;gBACL,CAAC;gBAED,MAAMC,QAAQ,GAAGL,YAAY,CAAC5D,KAAK,CAAC;gBAEpC,oBACIpC,OAAA,CAACd,MAAM,CAAC+D,GAAG;kBAEPqD,GAAG,EAAEP,aAAa,GAAGhF,cAAc,GAAG,IAAK;kBAC3CmC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEoD,CAAC,EAAE,CAAC;kBAAG,CAAE;kBAChClD,OAAO,EAAE;oBAAEF,OAAO,EAAE,CAAC;oBAAEoD,CAAC,EAAE;kBAAE,CAAE;kBAC9B7C,UAAU,EAAE;oBAAEC,KAAK,EAAEvB,KAAK,GAAG;kBAAI,CAAE;kBACnCW,SAAS,EAAG,2GACRgD,aAAa,GACP,2IAA2I,GAC3I5D,IAAI,CAACmC,kBAAkB,KAAK,SAAS,GACrC,+CAA+C,GAC/CnC,IAAI,CAACmC,kBAAkB,KAAK,QAAQ,GACpC,qCAAqC,GACrC,8BACT,EAAE;kBAAAtB,QAAA,gBAGHhD,OAAA;oBAAK+C,SAAS,EAAG,oGAAmGsD,QAAQ,CAACH,EAAG,IAAGG,QAAQ,CAACF,MAAO,aAAYE,QAAQ,CAACD,IAAK,sEAAsE;oBAAApD,QAAA,GAC9OZ,KAAK,GAAG,CAAC,gBACNpC,OAAA;sBAAK+C,SAAS,EAAC,aAAa;sBAAAC,QAAA,gBACxBhD,OAAA;wBAAK+C,SAAS,EAAC,+CAA+C;wBAAAC,QAAA,EAAEqD,QAAQ,CAACV;sBAAI;wBAAArC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACpFzD,OAAA;wBAAK+C,SAAS,EAAG,qBAAoBsD,QAAQ,CAACT,KAAM,kBAAkB;wBAAA5C,QAAA,EAAEqD,QAAQ,CAACR;sBAAK;wBAAAvC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5F,CAAC,gBAENzD,OAAA;sBAAK+C,SAAS,EAAC,aAAa;sBAAAC,QAAA,gBACxBhD,OAAA;wBAAM+C,SAAS,EAAC,0DAA0D;wBAAAC,QAAA,EAAEqD,QAAQ,CAACV;sBAAI;wBAAArC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACjGzD,OAAA;wBAAK+C,SAAS,EAAC,qDAAqD;wBAAAC,QAAA,EAAC;sBAAI;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9E,CACR,EAGArB,KAAK,GAAG,CAAC,iBACNpC,OAAA;sBAAK+C,SAAS,EAAG,iCAAgCsD,QAAQ,CAACH,EAAG;oBAAyC;sBAAA5C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAC/G;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eAGNzD,OAAA;oBAAK+C,SAAS,EAAG,+EACbZ,IAAI,CAACmC,kBAAkB,KAAK,QAAQ,GAC9B,8CAA8C,GAC9CnC,IAAI,CAACmC,kBAAkB,KAAK,MAAM,GAClC,4CAA4C,GAC5C,0CACT,EAAE;oBAAAtB,QAAA,eACChD,OAAA;sBAAK+C,SAAS,EAAC,yFAAyF;sBAAAC,QAAA,EACnGb,IAAI,CAACoC,SAAS,gBACXvE,OAAA;wBACIwE,GAAG,EAAErC,IAAI,CAACoC,SAAU;wBACpBE,GAAG,EAAC,SAAS;wBACb1B,SAAS,EAAC,4BAA4B;wBACtC2B,OAAO,EAAGC,CAAC,IAAK;0BAAEA,CAAC,CAACC,MAAM,CAACJ,GAAG,GAAGK,KAAK;wBAAC;sBAAE;wBAAAvB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5C,CAAC,gBAEFzD,OAAA,CAACmE,MAAM;wBAACpB,SAAS,EAAC;sBAAuB;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAC9C;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eAGNzD,OAAA;oBAAK+C,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBAC3BhD,OAAA;sBAAK+C,SAAS,EAAC,oFAAoF;sBAAAC,QAAA,gBAC/FhD,OAAA;wBAAI+C,SAAS,EAAG,sDACZgD,aAAa,GAAG,kBAAkB,GAAG,eACxC,EAAE;wBAAA/C,QAAA,EACEb,IAAI,CAAC2D;sBAAQ;wBAAAxC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACd,CAAC,eACLzD,OAAA;wBAAK+C,SAAS,EAAC,0CAA0C;wBAAAC,QAAA,GACpD+C,aAAa,iBACV/F,OAAA;0BAAM+C,SAAS,EAAC,kGAAkG;0BAAAC,QAAA,EAAC;wBAAG;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAC/H,EACArB,KAAK,GAAG,CAAC,iBACNpC,OAAA;0BAAM+C,SAAS,EAAG,6DACdX,KAAK,KAAK,CAAC,GAAG,2BAA2B,GACzCA,KAAK,KAAK,CAAC,GAAG,2BAA2B,GACzC,6BACH,EAAE;0BAAAY,QAAA,GACEqD,QAAQ,CAACR,KAAK,EAAC,WACpB;wBAAA;0BAAAvC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CACT,eAEDzD,OAAA;0BAAM+C,SAAS,EAAG,4CACdZ,IAAI,CAACmC,kBAAkB,KAAK,QAAQ,GAC9B,6BAA6B,GAC7BnC,IAAI,CAACmC,kBAAkB,KAAK,MAAM,GAClC,2BAA2B,GAC3B,yBACT,EAAE;0BAAAtB,QAAA,EACEb,IAAI,CAACmC,kBAAkB,KAAK,QAAQ,GAAG,SAAS,GAChDnC,IAAI,CAACmC,kBAAkB,KAAK,MAAM,GAAG,MAAM,GAAG;wBAAS;0BAAAhB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eAGNzD,OAAA;sBAAK+C,SAAS,EAAC,0DAA0D;sBAAAC,QAAA,gBACrEhD,OAAA;wBAAK+C,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,gBACxChD,OAAA;0BAAK+C,SAAS,EAAC;wBAAoC;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eAC1DzD,OAAA;0BAAM+C,SAAS,EAAC,8CAA8C;0BAAAC,QAAA,GAAEb,IAAI,CAACkC,WAAW,IAAI,CAAC,EAAC,MAAI;wBAAA;0BAAAf,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChG,CAAC,eACNzD,OAAA;wBAAK+C,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,gBACxChD,OAAA;0BAAK+C,SAAS,EAAC;wBAAmC;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACzDzD,OAAA;0BAAM+C,SAAS,EAAC,iDAAiD;0BAAAC,QAAA,GAAEb,IAAI,CAAC8C,gBAAgB,IAAI,CAAC,EAAC,SAAO;wBAAA;0BAAA3B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3G,CAAC,eACNzD,OAAA;wBAAK+C,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,gBACxChD,OAAA;0BAAK+C,SAAS,EAAC;wBAAkC;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACxDzD,OAAA;0BAAM+C,SAAS,EAAC,gDAAgD;0BAAAC,QAAA,GAAEb,IAAI,CAAC+C,YAAY,IAAI,CAAC,EAAC,UAAQ;wBAAA;0BAAA5B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eAENzD,OAAA;sBAAK+C,SAAS,EAAC,gHAAgH;sBAAAC,QAAA,gBAC3HhD,OAAA;wBAAK+C,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,gBACxChD,OAAA,CAACH,QAAQ;0BAACkD,SAAS,EAAC;wBAAuB;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC9CzD,OAAA;0BAAM+C,SAAS,EAAC,UAAU;0BAAAC,QAAA,EAAEb,IAAI,CAACqE,UAAU,IAAI;wBAAc;0BAAAlD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpE,CAAC,eACNzD,OAAA;wBAAK+C,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,gBACxChD,OAAA,CAACJ,OAAO;0BAACmD,SAAS,EAAC;wBAAS;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC/BzD,OAAA;0BAAAgD,QAAA,EAAOb,IAAI,CAACkD,SAAS,IAAI;wBAAc;0BAAA/B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9C,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eAGNzD,OAAA;oBAAK+C,SAAS,EAAC,YAAY;oBAAAC,QAAA,gBACvBhD,OAAA;sBAAK+C,SAAS,EAAG,sBACbgD,aAAa,GAAG,kBAAkB,GAAG,eACxC,EAAE;sBAAA/C,QAAA,EACEb,IAAI,CAACsE;oBAAK;sBAAAnD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACNzD,OAAA;sBAAK+C,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAM;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC;gBAAA,GAhIDtB,IAAI,CAACK,MAAM;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAiIR,CAAC;cAErB,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,gBAEPzD,OAAA,CAAC4D,IAAI;UAACb,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC9BhD,OAAA,CAACF,UAAU;YAACiD,SAAS,EAAC;UAAsC;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/DzD,OAAA;YAAI+C,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAe;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7EzD,OAAA;YAAG+C,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAE7B;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,EAGZlD,QAAQ,iBACLP,OAAA,CAACd,MAAM,CAAC2E,MAAM;QACVX,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEY,KAAK,EAAE;QAAE,CAAE;QAClCV,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEY,KAAK,EAAE;QAAE,CAAE;QAClCL,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAE,CAAE;QACzBG,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3BC,QAAQ,EAAE;UAAED,KAAK,EAAE;QAAI,CAAE;QACzBE,OAAO,EAAE/C,mBAAoB;QAC7B6B,SAAS,EAAC,4JAA4J;QACtK8C,KAAK,EAAC,oBAAoB;QAAA7C,QAAA,eAE1BhD,OAAA,CAACmE,MAAM;UAACpB,SAAS,EAAC;QAAS;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAClB;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEA;EACR;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAAvD,EAAA,CAlkBKD,OAAO;EAAA,QASQX,WAAW;AAAA;AAAAoH,EAAA,GAT1BzG,OAAO;AAokBb,eAAeA,OAAO;AAAC,IAAAyG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}