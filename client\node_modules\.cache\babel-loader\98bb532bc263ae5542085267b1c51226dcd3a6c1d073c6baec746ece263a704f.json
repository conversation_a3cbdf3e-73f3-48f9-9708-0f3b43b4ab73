{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\QuizRenderer.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport '../pages/user/Quiz/responsive.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst QuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  onAnswerChange,\n  timeLeft,\n  onNext,\n  onPrevious,\n  examTitle = \"Quiz\",\n  isTimeWarning = false\n}) => {\n  _s();\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n  const handleAnswerSelect = answer => {\n    setCurrentAnswer(answer);\n    setIsAnswered(true);\n    onAnswerChange(answer);\n  };\n  const formatTime = seconds => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n  const progressPercentage = (questionIndex + 1) / totalQuestions * 100;\n\n  // Early return for invalid question\n  if (!question || !question.name) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"quiz-container quiz-responsive\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"quiz-content quiz-content-responsive\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"quiz-question-container\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center text-red-600 p-6 bg-red-50 rounded-lg border border-red-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"ri-error-warning-line text-3xl mb-3 block\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold mb-2\",\n              children: \"Question Not Available\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm\",\n              children: \"This question could not be loaded. Please try refreshing the page.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this);\n  }\n  const renderMCQ = () => {\n    if (!question || !question.options || Object.keys(question.options).length === 0) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"quiz-question-container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center text-red-600 p-4 bg-red-50 rounded-lg border border-red-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"ri-error-warning-line text-2xl mb-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"No options available for this question.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this);\n    }\n    const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"quiz-options space-y-3\",\n      children: Object.entries(question.options).map(([key, value], index) => {\n        const optionKey = String(key).trim();\n        const optionValue = String(value || '').trim();\n        const label = optionLabels[index] || optionKey;\n        const isSelected = currentAnswer === optionKey;\n\n        // Skip empty options\n        if (!optionValue) return null;\n        return /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handleAnswerSelect(optionKey),\n          className: `quiz-option w-full text-left transition-all duration-200 ${isSelected ? 'selected' : ''}`,\n          style: {\n            color: isSelected ? '#ffffff' : '#374151'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"quiz-option-letter\",\n            children: label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"quiz-option-text\",\n            style: {\n              color: isSelected ? '#ffffff' : '#000000',\n              fontWeight: isSelected ? '600' : '500'\n            },\n            children: optionValue\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 15\n          }, this)]\n        }, optionKey, true, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this);\n  };\n  const renderFillBlank = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"quiz-question-container space-y-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n      className: \"block text-sm font-medium text-gray-700 mb-2\",\n      children: \"Your Answer:\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n      type: \"text\",\n      value: currentAnswer,\n      onChange: e => handleAnswerSelect(e.target.value),\n      placeholder: \"Type your answer here...\",\n      className: \"quiz-fill-input w-full\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this), currentAnswer && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-emerald-50 to-green-50 rounded-xl p-4 sm:p-6 border border-emerald-200 shadow-sm mt-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-3 sm:space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-br from-emerald-500 to-green-500 rounded-full flex items-center justify-center shadow-sm\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-white text-sm sm:text-base\",\n            children: \"\\u2713\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-emerald-800 font-semibold text-sm sm:text-base\",\n          children: [\"Answer: \", currentAnswer]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 109,\n    columnNumber: 5\n  }, this);\n  const renderImageQuestion = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-8\",\n    children: [question.imageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"inline-block p-2 bg-white rounded-xl shadow-lg border border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: question.imageUrl,\n          alt: \"Question diagram\",\n          className: \"max-w-full max-h-96 rounded-lg mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 9\n    }, this), question.options ? renderMCQ() : renderFillBlank()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 135,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"quiz-container quiz-responsive\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"quiz-progress-bar\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"quiz-progress-fill\",\n        style: {\n          width: `${progressPercentage}%`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"quiz-progress-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"quiz-header-content-improved\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"quiz-title-section\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"quiz-subtitle text-sm sm:text-base\",\n            children: examTitle\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"quiz-timer-center\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `quiz-timer ${isTimeWarning ? 'warning' : ''}`,\n            children: formatTime(timeLeft)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"quiz-question-counter-right\",\n          children: [\"Question \", questionIndex + 1, \" of \", totalQuestions]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"quiz-content pb-20 quiz-content-responsive\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"quiz-question-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"quiz-question-number text-sm sm:text-base\",\n          children: [\"Question \", questionIndex + 1]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"quiz-question-text text-lg sm:text-xl\",\n          children: question.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this), question.image && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"quiz-image-container\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: question.image,\n            alt: \"Question\",\n            className: \"quiz-image\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 13\n        }, this), question.answerType === \"Options\" && renderMCQ(), (question.answerType === \"Free Text\" || question.answerType === \"Fill in the Blank\") && renderFillBlank(), question.answerType === \"Image\" && question.imageUrl && renderImageQuestion()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"quiz-navigation\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onPrevious,\n        disabled: questionIndex === 0,\n        className: `quiz-nav-btn ${questionIndex === 0 ? 'disabled' : 'secondary'}`,\n        title: questionIndex === 0 ? 'This is the first question' : 'Go to previous question',\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"ri-arrow-left-line mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this), \"Previous\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onNext,\n        disabled: !isAnswered,\n        className: `quiz-nav-btn ${!isAnswered ? 'disabled' : 'primary'}`,\n        title: !isAnswered ? 'Please select an answer first' : questionIndex === totalQuestions - 1 ? 'Submit your quiz' : 'Go to next question',\n        children: questionIndex === totalQuestions - 1 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"ri-check-line mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 15\n          }, this), \"Submit Quiz\"]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [\"Next\", /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"ri-arrow-right-line ml-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 207,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 153,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizRenderer, \"GLXCrRLAt2Wgb0CPI+PSeCbLXgs=\");\n_c = QuizRenderer;\nexport default QuizRenderer;\nvar _c;\n$RefreshReg$(_c, \"QuizRenderer\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Quiz<PERSON><PERSON><PERSON>", "question", "questionIndex", "totalQuestions", "<PERSON><PERSON><PERSON><PERSON>", "onAnswerChange", "timeLeft", "onNext", "onPrevious", "examTitle", "isTimeWarning", "_s", "currentAnswer", "setCurrentAnswer", "isAnswered", "setIsAnswered", "handleAnswerSelect", "answer", "formatTime", "seconds", "minutes", "Math", "floor", "remainingSeconds", "toString", "padStart", "progressPercentage", "name", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "renderMCQ", "options", "Object", "keys", "length", "optionLabels", "entries", "map", "key", "value", "index", "optionKey", "String", "trim", "optionValue", "label", "isSelected", "onClick", "style", "color", "fontWeight", "renderFillBlank", "type", "onChange", "e", "target", "placeholder", "renderImageQuestion", "imageUrl", "src", "alt", "width", "image", "answerType", "disabled", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/QuizRenderer.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport '../pages/user/Quiz/responsive.css';\n\nconst QuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  onAnswerChange,\n  timeLeft,\n  onNext,\n  onPrevious,\n  examTitle = \"Quiz\",\n  isTimeWarning = false\n}) => {\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n\n  const handleAnswerSelect = (answer) => {\n    setCurrentAnswer(answer);\n    setIsAnswered(true);\n    onAnswerChange(answer);\n  };\n\n  const formatTime = (seconds) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  const progressPercentage = ((questionIndex + 1) / totalQuestions) * 100;\n\n  // Early return for invalid question\n  if (!question || !question.name) {\n    return (\n      <div className=\"quiz-container quiz-responsive\">\n        <div className=\"quiz-content quiz-content-responsive\">\n          <div className=\"quiz-question-container\">\n            <div className=\"text-center text-red-600 p-6 bg-red-50 rounded-lg border border-red-200\">\n              <i className=\"ri-error-warning-line text-3xl mb-3 block\"></i>\n              <h3 className=\"text-lg font-semibold mb-2\">Question Not Available</h3>\n              <p className=\"text-sm\">This question could not be loaded. Please try refreshing the page.</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  const renderMCQ = () => {\n    if (!question || !question.options || Object.keys(question.options).length === 0) {\n      return (\n        <div className=\"quiz-question-container\">\n          <div className=\"text-center text-red-600 p-4 bg-red-50 rounded-lg border border-red-200\">\n            <i className=\"ri-error-warning-line text-2xl mb-2\"></i>\n            <p>No options available for this question.</p>\n          </div>\n        </div>\n      );\n    }\n\n    const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];\n\n    return (\n      <div className=\"quiz-options space-y-3\">\n        {Object.entries(question.options).map(([key, value], index) => {\n          const optionKey = String(key).trim();\n          const optionValue = String(value || '').trim();\n          const label = optionLabels[index] || optionKey;\n          const isSelected = currentAnswer === optionKey;\n\n          // Skip empty options\n          if (!optionValue) return null;\n\n          return (\n            <button\n              key={optionKey}\n              onClick={() => handleAnswerSelect(optionKey)}\n              className={`quiz-option w-full text-left transition-all duration-200 ${\n                isSelected ? 'selected' : ''\n              }`}\n              style={{\n                color: isSelected ? '#ffffff' : '#374151'\n              }}\n            >\n              <span className=\"quiz-option-letter\">{label}</span>\n              <span\n                className=\"quiz-option-text\"\n                style={{\n                  color: isSelected ? '#ffffff' : '#000000',\n                  fontWeight: isSelected ? '600' : '500'\n                }}\n              >\n                {optionValue}\n              </span>\n            </button>\n          );\n        })}\n      </div>\n    );\n  };\n\n  const renderFillBlank = () => (\n    <div className=\"quiz-question-container space-y-4\">\n      <label className=\"block text-sm font-medium text-gray-700 mb-2\">Your Answer:</label>\n      <input\n        type=\"text\"\n        value={currentAnswer}\n        onChange={(e) => handleAnswerSelect(e.target.value)}\n        placeholder=\"Type your answer here...\"\n        className=\"quiz-fill-input w-full\"\n      />\n\n      {currentAnswer && (\n        <div className=\"bg-gradient-to-r from-emerald-50 to-green-50 rounded-xl p-4 sm:p-6 border border-emerald-200 shadow-sm mt-4\">\n          <div className=\"flex items-center space-x-3 sm:space-x-4\">\n            <div className=\"w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-br from-emerald-500 to-green-500 rounded-full flex items-center justify-center shadow-sm\">\n              <span className=\"text-white text-sm sm:text-base\">✓</span>\n            </div>\n            <p className=\"text-emerald-800 font-semibold text-sm sm:text-base\">\n              Answer: {currentAnswer}\n            </p>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n\n  const renderImageQuestion = () => (\n    <div className=\"space-y-8\">\n      {question.imageUrl && (\n        <div className=\"text-center\">\n          <div className=\"inline-block p-2 bg-white rounded-xl shadow-lg border border-gray-200\">\n            <img\n              src={question.imageUrl}\n              alt=\"Question diagram\"\n              className=\"max-w-full max-h-96 rounded-lg mx-auto\"\n            />\n          </div>\n        </div>\n      )}\n\n      {question.options ? renderMCQ() : renderFillBlank()}\n    </div>\n  );\n\n  return (\n    <div className=\"quiz-container quiz-responsive\">\n      <div className=\"quiz-progress-bar\">\n        <div\n          className=\"quiz-progress-fill\"\n          style={{ width: `${progressPercentage}%` }}\n        />\n      </div>\n\n      <div className=\"quiz-progress-container\">\n        <div className=\"quiz-header-content-improved\">\n          <div className=\"quiz-title-section\">\n            <p className=\"quiz-subtitle text-sm sm:text-base\">\n              {examTitle}\n            </p>\n          </div>\n\n          <div className=\"quiz-timer-center\">\n            <div className={`quiz-timer ${isTimeWarning ? 'warning' : ''}`}>\n              {formatTime(timeLeft)}\n            </div>\n          </div>\n\n          <div className=\"quiz-question-counter-right\">\n            Question {questionIndex + 1} of {totalQuestions}\n          </div>\n        </div>\n      </div>\n\n      <div className=\"quiz-content pb-20 quiz-content-responsive\">\n        <div className=\"quiz-question-container\">\n          <div className=\"quiz-question-number text-sm sm:text-base\">\n            Question {questionIndex + 1}\n          </div>\n\n          <div className=\"quiz-question-text text-lg sm:text-xl\">\n            {question.name}\n          </div>\n\n          {question.image && (\n            <div className=\"quiz-image-container\">\n              <img\n                src={question.image}\n                alt=\"Question\"\n                className=\"quiz-image\"\n              />\n            </div>\n          )}\n\n          {question.answerType === \"Options\" && renderMCQ()}\n          {(question.answerType === \"Free Text\" || question.answerType === \"Fill in the Blank\") && renderFillBlank()}\n          {question.answerType === \"Image\" && question.imageUrl && renderImageQuestion()}\n        </div>\n      </div>\n\n      <div className=\"quiz-navigation\">\n        <button\n          onClick={onPrevious}\n          disabled={questionIndex === 0}\n          className={`quiz-nav-btn ${questionIndex === 0 ? 'disabled' : 'secondary'}`}\n          title={questionIndex === 0 ? 'This is the first question' : 'Go to previous question'}\n        >\n          <i className=\"ri-arrow-left-line mr-2\"></i>\n          Previous\n        </button>\n\n        <button\n          onClick={onNext}\n          disabled={!isAnswered}\n          className={`quiz-nav-btn ${!isAnswered ? 'disabled' : 'primary'}`}\n          title={!isAnswered ? 'Please select an answer first' :\n                 questionIndex === totalQuestions - 1 ? 'Submit your quiz' : 'Go to next question'}\n        >\n          {questionIndex === totalQuestions - 1 ? (\n            <>\n              <i className=\"ri-check-line mr-2\"></i>\n              Submit Quiz\n            </>\n          ) : (\n            <>\n              Next\n              <i className=\"ri-arrow-right-line ml-2\"></i>\n            </>\n          )}\n        </button>\n      </div>\n\n\n    </div>\n  );\n};\n\nexport default QuizRenderer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAE3C,MAAMC,YAAY,GAAGA,CAAC;EACpBC,QAAQ;EACRC,aAAa;EACbC,cAAc;EACdC,cAAc;EACdC,cAAc;EACdC,QAAQ;EACRC,MAAM;EACNC,UAAU;EACVC,SAAS,GAAG,MAAM;EAClBC,aAAa,GAAG;AAClB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGnB,QAAQ,CAACU,cAAc,IAAI,EAAE,CAAC;EACxE,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAEnDC,SAAS,CAAC,MAAM;IACdkB,gBAAgB,CAACT,cAAc,IAAI,EAAE,CAAC;IACtCW,aAAa,CAAC,CAAC,CAACX,cAAc,CAAC;EACjC,CAAC,EAAE,CAACA,cAAc,EAAEF,aAAa,CAAC,CAAC;EAEnC,MAAMc,kBAAkB,GAAIC,MAAM,IAAK;IACrCJ,gBAAgB,CAACI,MAAM,CAAC;IACxBF,aAAa,CAAC,IAAI,CAAC;IACnBV,cAAc,CAACY,MAAM,CAAC;EACxB,CAAC;EAED,MAAMC,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;IACrC,OAAQ,GAAEC,OAAQ,IAAGG,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,EAAC;EACrE,CAAC;EAED,MAAMC,kBAAkB,GAAI,CAACxB,aAAa,GAAG,CAAC,IAAIC,cAAc,GAAI,GAAG;;EAEvE;EACA,IAAI,CAACF,QAAQ,IAAI,CAACA,QAAQ,CAAC0B,IAAI,EAAE;IAC/B,oBACE9B,OAAA;MAAK+B,SAAS,EAAC,gCAAgC;MAAAC,QAAA,eAC7ChC,OAAA;QAAK+B,SAAS,EAAC,sCAAsC;QAAAC,QAAA,eACnDhC,OAAA;UAAK+B,SAAS,EAAC,yBAAyB;UAAAC,QAAA,eACtChC,OAAA;YAAK+B,SAAS,EAAC,yEAAyE;YAAAC,QAAA,gBACtFhC,OAAA;cAAG+B,SAAS,EAAC;YAA2C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7DpC,OAAA;cAAI+B,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtEpC,OAAA;cAAG+B,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAkE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAMC,SAAS,GAAGA,CAAA,KAAM;IACtB,IAAI,CAACjC,QAAQ,IAAI,CAACA,QAAQ,CAACkC,OAAO,IAAIC,MAAM,CAACC,IAAI,CAACpC,QAAQ,CAACkC,OAAO,CAAC,CAACG,MAAM,KAAK,CAAC,EAAE;MAChF,oBACEzC,OAAA;QAAK+B,SAAS,EAAC,yBAAyB;QAAAC,QAAA,eACtChC,OAAA;UAAK+B,SAAS,EAAC,yEAAyE;UAAAC,QAAA,gBACtFhC,OAAA;YAAG+B,SAAS,EAAC;UAAqC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvDpC,OAAA;YAAAgC,QAAA,EAAG;UAAuC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEV;IAEA,MAAMM,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAEnD,oBACE1C,OAAA;MAAK+B,SAAS,EAAC,wBAAwB;MAAAC,QAAA,EACpCO,MAAM,CAACI,OAAO,CAACvC,QAAQ,CAACkC,OAAO,CAAC,CAACM,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,EAAEC,KAAK,KAAK;QAC7D,MAAMC,SAAS,GAAGC,MAAM,CAACJ,GAAG,CAAC,CAACK,IAAI,CAAC,CAAC;QACpC,MAAMC,WAAW,GAAGF,MAAM,CAACH,KAAK,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC;QAC9C,MAAME,KAAK,GAAGV,YAAY,CAACK,KAAK,CAAC,IAAIC,SAAS;QAC9C,MAAMK,UAAU,GAAGtC,aAAa,KAAKiC,SAAS;;QAE9C;QACA,IAAI,CAACG,WAAW,EAAE,OAAO,IAAI;QAE7B,oBACEnD,OAAA;UAEEsD,OAAO,EAAEA,CAAA,KAAMnC,kBAAkB,CAAC6B,SAAS,CAAE;UAC7CjB,SAAS,EAAG,4DACVsB,UAAU,GAAG,UAAU,GAAG,EAC3B,EAAE;UACHE,KAAK,EAAE;YACLC,KAAK,EAAEH,UAAU,GAAG,SAAS,GAAG;UAClC,CAAE;UAAArB,QAAA,gBAEFhC,OAAA;YAAM+B,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAEoB;UAAK;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACnDpC,OAAA;YACE+B,SAAS,EAAC,kBAAkB;YAC5BwB,KAAK,EAAE;cACLC,KAAK,EAAEH,UAAU,GAAG,SAAS,GAAG,SAAS;cACzCI,UAAU,EAAEJ,UAAU,GAAG,KAAK,GAAG;YACnC,CAAE;YAAArB,QAAA,EAEDmB;UAAW;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA,GAlBFY,SAAS;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmBR,CAAC;MAEb,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV,CAAC;EAED,MAAMsB,eAAe,GAAGA,CAAA,kBACtB1D,OAAA;IAAK+B,SAAS,EAAC,mCAAmC;IAAAC,QAAA,gBAChDhC,OAAA;MAAO+B,SAAS,EAAC,8CAA8C;MAAAC,QAAA,EAAC;IAAY;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACpFpC,OAAA;MACE2D,IAAI,EAAC,MAAM;MACXb,KAAK,EAAE/B,aAAc;MACrB6C,QAAQ,EAAGC,CAAC,IAAK1C,kBAAkB,CAAC0C,CAAC,CAACC,MAAM,CAAChB,KAAK,CAAE;MACpDiB,WAAW,EAAC,0BAA0B;MACtChC,SAAS,EAAC;IAAwB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CAAC,EAEDrB,aAAa,iBACZf,OAAA;MAAK+B,SAAS,EAAC,6GAA6G;MAAAC,QAAA,eAC1HhC,OAAA;QAAK+B,SAAS,EAAC,0CAA0C;QAAAC,QAAA,gBACvDhC,OAAA;UAAK+B,SAAS,EAAC,+HAA+H;UAAAC,QAAA,eAC5IhC,OAAA;YAAM+B,SAAS,EAAC,iCAAiC;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC,eACNpC,OAAA;UAAG+B,SAAS,EAAC,qDAAqD;UAAAC,QAAA,GAAC,UACzD,EAACjB,aAAa;QAAA;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;EAED,MAAM4B,mBAAmB,GAAGA,CAAA,kBAC1BhE,OAAA;IAAK+B,SAAS,EAAC,WAAW;IAAAC,QAAA,GACvB5B,QAAQ,CAAC6D,QAAQ,iBAChBjE,OAAA;MAAK+B,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1BhC,OAAA;QAAK+B,SAAS,EAAC,uEAAuE;QAAAC,QAAA,eACpFhC,OAAA;UACEkE,GAAG,EAAE9D,QAAQ,CAAC6D,QAAS;UACvBE,GAAG,EAAC,kBAAkB;UACtBpC,SAAS,EAAC;QAAwC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEAhC,QAAQ,CAACkC,OAAO,GAAGD,SAAS,CAAC,CAAC,GAAGqB,eAAe,CAAC,CAAC;EAAA;IAAAzB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChD,CACN;EAED,oBACEpC,OAAA;IAAK+B,SAAS,EAAC,gCAAgC;IAAAC,QAAA,gBAC7ChC,OAAA;MAAK+B,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChChC,OAAA;QACE+B,SAAS,EAAC,oBAAoB;QAC9BwB,KAAK,EAAE;UAAEa,KAAK,EAAG,GAAEvC,kBAAmB;QAAG;MAAE;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENpC,OAAA;MAAK+B,SAAS,EAAC,yBAAyB;MAAAC,QAAA,eACtChC,OAAA;QAAK+B,SAAS,EAAC,8BAA8B;QAAAC,QAAA,gBAC3ChC,OAAA;UAAK+B,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjChC,OAAA;YAAG+B,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAC9CpB;UAAS;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENpC,OAAA;UAAK+B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChChC,OAAA;YAAK+B,SAAS,EAAG,cAAalB,aAAa,GAAG,SAAS,GAAG,EAAG,EAAE;YAAAmB,QAAA,EAC5DX,UAAU,CAACZ,QAAQ;UAAC;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENpC,OAAA;UAAK+B,SAAS,EAAC,6BAA6B;UAAAC,QAAA,GAAC,WAClC,EAAC3B,aAAa,GAAG,CAAC,EAAC,MAAI,EAACC,cAAc;QAAA;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENpC,OAAA;MAAK+B,SAAS,EAAC,4CAA4C;MAAAC,QAAA,eACzDhC,OAAA;QAAK+B,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACtChC,OAAA;UAAK+B,SAAS,EAAC,2CAA2C;UAAAC,QAAA,GAAC,WAChD,EAAC3B,aAAa,GAAG,CAAC;QAAA;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eAENpC,OAAA;UAAK+B,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACnD5B,QAAQ,CAAC0B;QAAI;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,EAELhC,QAAQ,CAACiE,KAAK,iBACbrE,OAAA;UAAK+B,SAAS,EAAC,sBAAsB;UAAAC,QAAA,eACnChC,OAAA;YACEkE,GAAG,EAAE9D,QAAQ,CAACiE,KAAM;YACpBF,GAAG,EAAC,UAAU;YACdpC,SAAS,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EAEAhC,QAAQ,CAACkE,UAAU,KAAK,SAAS,IAAIjC,SAAS,CAAC,CAAC,EAChD,CAACjC,QAAQ,CAACkE,UAAU,KAAK,WAAW,IAAIlE,QAAQ,CAACkE,UAAU,KAAK,mBAAmB,KAAKZ,eAAe,CAAC,CAAC,EACzGtD,QAAQ,CAACkE,UAAU,KAAK,OAAO,IAAIlE,QAAQ,CAAC6D,QAAQ,IAAID,mBAAmB,CAAC,CAAC;MAAA;QAAA/B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENpC,OAAA;MAAK+B,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BhC,OAAA;QACEsD,OAAO,EAAE3C,UAAW;QACpB4D,QAAQ,EAAElE,aAAa,KAAK,CAAE;QAC9B0B,SAAS,EAAG,gBAAe1B,aAAa,KAAK,CAAC,GAAG,UAAU,GAAG,WAAY,EAAE;QAC5EmE,KAAK,EAAEnE,aAAa,KAAK,CAAC,GAAG,4BAA4B,GAAG,yBAA0B;QAAA2B,QAAA,gBAEtFhC,OAAA;UAAG+B,SAAS,EAAC;QAAyB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,YAE7C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAETpC,OAAA;QACEsD,OAAO,EAAE5C,MAAO;QAChB6D,QAAQ,EAAE,CAACtD,UAAW;QACtBc,SAAS,EAAG,gBAAe,CAACd,UAAU,GAAG,UAAU,GAAG,SAAU,EAAE;QAClEuD,KAAK,EAAE,CAACvD,UAAU,GAAG,+BAA+B,GAC7CZ,aAAa,KAAKC,cAAc,GAAG,CAAC,GAAG,kBAAkB,GAAG,qBAAsB;QAAA0B,QAAA,EAExF3B,aAAa,KAAKC,cAAc,GAAG,CAAC,gBACnCN,OAAA,CAAAE,SAAA;UAAA8B,QAAA,gBACEhC,OAAA;YAAG+B,SAAS,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAExC;QAAA,eAAE,CAAC,gBAEHpC,OAAA,CAAAE,SAAA;UAAA8B,QAAA,GAAE,MAEA,eAAAhC,OAAA;YAAG+B,SAAS,EAAC;UAA0B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA,eAC5C;MACH;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGH,CAAC;AAEV,CAAC;AAACtB,EAAA,CA9OIX,YAAY;AAAAsE,EAAA,GAAZtE,YAAY;AAgPlB,eAAeA,YAAY;AAAC,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}