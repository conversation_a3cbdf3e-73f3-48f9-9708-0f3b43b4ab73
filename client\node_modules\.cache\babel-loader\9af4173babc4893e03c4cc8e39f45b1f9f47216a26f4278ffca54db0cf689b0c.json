{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Ranking\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport './index.css';\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { getAllReportsForRanking } from \"../../../apicalls/reports\";\nimport { getUserInfo } from \"../../../apicalls/users\";\nimport { message } from \"antd\";\nimport PageTitle from \"../../../components/PageTitle\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { Card, Button, Loading } from \"../../../components/modern\";\nimport image from '../../../assets/person.png';\nimport { IoPersonCircleOutline } from \"react-icons/io5\";\nimport { TbTrophy, TbMedal, TbCrown, TbUsers, TbSchool, TbStar, TbChartBar, TbUser, TbAward } from \"react-icons/tb\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Ranking = () => {\n  _s();\n  const [rankingData, setRankingData] = useState('');\n  const [userRanking, setUserRanking] = useState('');\n  const [userData, setUserData] = useState('');\n  const [isAdmin, setIsAdmin] = useState(false);\n  const [isMobile, setIsMobile] = useState(false);\n  const [activeTab, setActiveTab] = useState(\"overall\"); // \"overall\" or \"class\"\n\n  const dispatch = useDispatch();\n  const fetchReports = async () => {\n    try {\n      const response = await getAllReportsForRanking();\n      if (response.success) {\n        setRankingData(response.data);\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  const getUserData = async () => {\n    try {\n      const response = await getUserInfo();\n      if (response.success) {\n        if (response.data.isAdmin) {\n          setIsAdmin(true);\n        } else {\n          setIsAdmin(false);\n          setUserData(response.data);\n          await fetchReports();\n          dispatch(HideLoading());\n        }\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  useEffect(() => {\n    if (window.innerWidth < 700) {\n      setIsMobile(true);\n    } else {\n      setIsMobile(false);\n    }\n    if (localStorage.getItem(\"token\")) {\n      dispatch(ShowLoading());\n      getUserData();\n    }\n  }, []);\n  const getUserStats = () => {\n    const Ranking = rankingData.map((user, index) => ({\n      user,\n      ranking: index + 1\n    })).filter(item => item.user.userId.includes(userData._id));\n    setUserRanking(Ranking);\n  };\n  useEffect(() => {\n    if (rankingData) {\n      getUserStats();\n    }\n  }, [rankingData]);\n\n  // Helper function to format user ID for mobile devices\n  const formatMobileUserId = userId => {\n    const prefix = userId.slice(0, 4);\n    const suffix = userId.slice(-4);\n    return `${prefix}.....${suffix}`;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50\",\n    children: !isAdmin && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-modern py-8\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full mb-4\",\n          children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n            className: \"w-8 h-8 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"heading-2 text-gradient mb-4\",\n          children: \"Leaderboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-gray-600\",\n          children: \"See how you rank against other students\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.2\n        },\n        className: \"mb-8\",\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"p-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(motion.button, {\n              whileHover: {\n                scale: 1.02\n              },\n              whileTap: {\n                scale: 0.98\n              },\n              className: `flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 flex-1 justify-center ${activeTab === \"overall\" ? 'bg-primary-600 text-white shadow-md' : 'text-gray-600 hover:bg-gray-100'}`,\n              onClick: () => setActiveTab(\"overall\"),\n              children: [/*#__PURE__*/_jsxDEV(TbUsers, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Overall Ranking\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 37\n              }, this), activeTab === \"overall\" && /*#__PURE__*/_jsxDEV(motion.div, {\n                layoutId: \"activeRankingTab\",\n                className: \"w-2 h-2 bg-white rounded-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n              whileHover: {\n                scale: 1.02\n              },\n              whileTap: {\n                scale: 0.98\n              },\n              className: `flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 flex-1 justify-center ${activeTab === \"class\" ? 'bg-primary-600 text-white shadow-md' : 'text-gray-600 hover:bg-gray-100'}`,\n              onClick: () => setActiveTab(\"class\"),\n              children: [/*#__PURE__*/_jsxDEV(TbSchool, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Class Ranking\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 37\n              }, this), activeTab === \"class\" && /*#__PURE__*/_jsxDEV(motion.div, {\n                layoutId: \"activeRankingTab\",\n                className: \"w-2 h-2 bg-white rounded-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.4\n        },\n        children: rankingData.length > 0 ? /*#__PURE__*/_jsxDEV(Card, {\n          className: \"overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 text-white p-6 relative overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 opacity-10\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute top-4 left-4 text-6xl\",\n                children: \"\\uD83C\\uDFC6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute top-8 right-8 text-4xl\",\n                children: \"\\u2B50\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute bottom-4 left-1/4 text-3xl\",\n                children: \"\\uD83C\\uDFAF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute bottom-6 right-1/3 text-5xl\",\n                children: \"\\uD83D\\uDC8E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative z-10\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-center space-x-3 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n                  className: \"w-10 h-10 text-yellow-300 animate-bounce\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-3xl font-black bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent\",\n                  children: activeTab === \"overall\" ? \"Global Leaderboard\" : \"Class Champions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(TbTrophy, {\n                  className: \"w-10 h-10 text-yellow-300 animate-bounce\",\n                  style: {\n                    animationDelay: '0.5s'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-center text-blue-100 text-lg font-semibold\",\n                children: activeTab === \"overall\" ? \"🌟 Elite performers from all classes competing for glory! 🌟\" : `🎓 Class ${(userData === null || userData === void 0 ? void 0 : userData.class) || 'your class'} top achievers! 🎓`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center mt-3\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"bg-white/20 px-4 py-2 rounded-full text-sm font-bold\",\n                  children: \"\\uD83D\\uDCAA Earn points by acing quizzes \\u2022 \\uD83C\\uDFC5 Climb the ranks \\u2022 \\uD83D\\uDC51 Become a champion!\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 45\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-br from-blue-50 to-purple-50 p-3 sm:p-6 border-b\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg sm:text-xl font-bold text-center mb-4 sm:mb-6 text-gray-800\",\n              children: \"\\uD83C\\uDFC6 Champions Podium \\uD83C\\uDFC6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-center items-end space-x-2 sm:space-x-4 overflow-x-auto pb-2\",\n              children: (activeTab === \"overall\" ? rankingData.slice(0, 3) : rankingData.filter(user => user.userClass === (userData === null || userData === void 0 ? void 0 : userData.class)).slice(0, 3)).map((user, index) => {\n                const positions = [1, 0, 2]; // Silver, Diamond, Bronze order for visual appeal\n                const actualPosition = positions.indexOf(index);\n                const heights = ['h-16 sm:h-20 md:h-24', 'h-20 sm:h-24 md:h-32', 'h-12 sm:h-16 md:h-20']; // Responsive heights for podium effect\n                const badges = [{\n                  icon: \"🥈\",\n                  color: \"from-gray-400 to-gray-600\",\n                  title: \"Silver\"\n                }, {\n                  icon: \"💎\",\n                  color: \"from-blue-400 to-cyan-600\",\n                  title: \"Diamond\"\n                }, {\n                  icon: \"🥉\",\n                  color: \"from-amber-400 to-orange-600\",\n                  title: \"Bronze\"\n                }];\n                return /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 50\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: index * 0.2\n                  },\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-2 sm:mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 mx-auto rounded-full overflow-hidden border-2 sm:border-4 border-white shadow-lg\",\n                      children: user.userPhoto ? /*#__PURE__*/_jsxDEV(\"img\", {\n                        src: user.userPhoto,\n                        alt: \"profile\",\n                        className: \"w-full h-full object-cover\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 245,\n                        columnNumber: 65\n                      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-full h-full bg-gray-200 flex items-center justify-center\",\n                        children: /*#__PURE__*/_jsxDEV(TbUser, {\n                          className: \"w-4 h-4 sm:w-6 sm:h-6 md:w-8 md:h-8 text-gray-400\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 248,\n                          columnNumber: 69\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 247,\n                        columnNumber: 65\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 243,\n                      columnNumber: 57\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-lg sm:text-xl md:text-2xl mt-1 sm:mt-2\",\n                      children: badges[index].icon\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 252,\n                      columnNumber: 57\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 53\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `${heights[index]} w-16 sm:w-18 md:w-20 bg-gradient-to-t ${badges[index].color} rounded-t-lg flex flex-col justify-end p-1 sm:p-2 shadow-lg`,\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white text-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"font-bold text-sm sm:text-base md:text-lg\",\n                        children: actualPosition + 1\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 258,\n                        columnNumber: 61\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-xs hidden sm:block\",\n                        children: badges[index].title\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 259,\n                        columnNumber: 61\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 257,\n                      columnNumber: 57\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 256,\n                    columnNumber: 53\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-1 sm:mt-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-bold text-xs sm:text-sm truncate w-16 sm:w-18 md:w-20\",\n                      children: user.userName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 265,\n                      columnNumber: 57\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-yellow-600 font-semibold\",\n                      children: [user.totalPoints || 0, \" pts\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 266,\n                      columnNumber: 57\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 264,\n                    columnNumber: 53\n                  }, this)]\n                }, user.userId, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 49\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 sm:p-4 md:p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3 sm:space-y-4\",\n              children: (activeTab === \"overall\" ? rankingData : rankingData.filter(user => user.userClass === (userData === null || userData === void 0 ? void 0 : userData.class))).map((user, index) => {\n                const isCurrentUser = user.userId.includes(userData === null || userData === void 0 ? void 0 : userData._id);\n                const getRankBadge = position => {\n                  if (position === 0) return {\n                    icon: \"💎\",\n                    color: \"text-blue-600\",\n                    bg: \"bg-gradient-to-br from-blue-100 to-cyan-100\",\n                    border: \"border-blue-300\",\n                    title: \"Diamond\",\n                    glow: \"shadow-blue-200\"\n                  };\n                  if (position === 1) return {\n                    icon: \"🥈\",\n                    color: \"text-gray-600\",\n                    bg: \"bg-gradient-to-br from-gray-100 to-slate-100\",\n                    border: \"border-gray-300\",\n                    title: \"Silver\",\n                    glow: \"shadow-gray-200\"\n                  };\n                  if (position === 2) return {\n                    icon: \"🥉\",\n                    color: \"text-amber-600\",\n                    bg: \"bg-gradient-to-br from-amber-100 to-orange-100\",\n                    border: \"border-amber-300\",\n                    title: \"Bronze\",\n                    glow: \"shadow-amber-200\"\n                  };\n                  return {\n                    icon: position + 1,\n                    color: \"text-gray-600\",\n                    bg: \"bg-gray-50\",\n                    border: \"border-gray-200\",\n                    title: `Rank ${position + 1}`,\n                    glow: \"shadow-gray-100\"\n                  };\n                };\n                const rankInfo = getRankBadge(index);\n                return /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    x: -20\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0\n                  },\n                  transition: {\n                    delay: index * 0.1\n                  },\n                  className: `flex items-center space-x-2 sm:space-x-3 md:space-x-4 p-3 sm:p-4 rounded-xl transition-all duration-200 ${isCurrentUser ? 'bg-primary-50 border-2 border-primary-200 shadow-md' : 'bg-gray-50 hover:bg-gray-100'}`,\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `relative w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 rounded-full flex items-center justify-center ${rankInfo.bg} ${rankInfo.border} border-2 ${rankInfo.glow} shadow-lg transition-all duration-300 hover:scale-110 flex-shrink-0`,\n                    children: [index < 3 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-lg sm:text-xl md:text-2xl mb-0.5 sm:mb-1\",\n                        children: rankInfo.icon\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 335,\n                        columnNumber: 65\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `text-xs font-bold ${rankInfo.color} hidden sm:block`,\n                        children: rankInfo.title\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 336,\n                        columnNumber: 65\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 334,\n                      columnNumber: 61\n                    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-black text-sm sm:text-base md:text-lg text-gray-700\",\n                        children: rankInfo.icon\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 340,\n                        columnNumber: 65\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-xs font-semibold text-gray-500 hidden sm:block\",\n                        children: \"Rank\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 341,\n                        columnNumber: 65\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 339,\n                      columnNumber: 61\n                    }, this), index < 3 && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `absolute inset-0 rounded-full ${rankInfo.bg} opacity-50 blur-md -z-10 animate-pulse`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 347,\n                      columnNumber: 61\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 332,\n                    columnNumber: 53\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-12 h-12 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center\",\n                    children: user.userPhoto ? /*#__PURE__*/_jsxDEV(\"img\", {\n                      src: user.userPhoto,\n                      alt: \"profile\",\n                      className: \"w-full h-full object-cover\",\n                      onError: e => {\n                        e.target.src = image;\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 354,\n                      columnNumber: 61\n                    }, this) : /*#__PURE__*/_jsxDEV(TbUser, {\n                      className: \"w-6 h-6 text-gray-400\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 361,\n                      columnNumber: 61\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 352,\n                    columnNumber: 53\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-2 mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: `font-bold text-lg truncate ${isCurrentUser ? 'text-primary-900' : 'text-gray-900'}`,\n                        children: user.userName\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 368,\n                        columnNumber: 61\n                      }, this), isCurrentUser && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"bg-gradient-to-r from-blue-500 to-purple-500 text-white text-xs px-2 py-1 rounded-full font-bold\",\n                        children: \"You\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 374,\n                        columnNumber: 65\n                      }, this), index < 3 && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `text-xs px-2 py-1 rounded-full font-bold ${index === 0 ? 'bg-blue-100 text-blue-800' : index === 1 ? 'bg-gray-100 text-gray-800' : 'bg-amber-100 text-amber-800'}`,\n                        children: [rankInfo.title, \" Champion\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 377,\n                        columnNumber: 65\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 367,\n                      columnNumber: 57\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-4 mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-2 h-2 bg-yellow-400 rounded-full\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 390,\n                          columnNumber: 65\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"font-bold text-yellow-600\",\n                          children: [user.totalPoints || 0, \" pts\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 391,\n                          columnNumber: 65\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 389,\n                        columnNumber: 61\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-2 h-2 bg-green-400 rounded-full\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 394,\n                          columnNumber: 65\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"font-semibold text-green-600\",\n                          children: [user.passedExamsCount || 0, \" passed\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 395,\n                          columnNumber: 65\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 393,\n                        columnNumber: 61\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 398,\n                          columnNumber: 65\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"font-semibold text-blue-600\",\n                          children: [user.quizzesTaken || 0, \" quizzes\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 399,\n                          columnNumber: 65\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 397,\n                        columnNumber: 61\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 388,\n                      columnNumber: 57\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-4 text-sm text-gray-600\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-1\",\n                        children: [/*#__PURE__*/_jsxDEV(TbSchool, {\n                          className: \"w-4 h-4\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 405,\n                          columnNumber: 65\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"truncate\",\n                          children: user.userSchool || 'Not Enrolled'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 406,\n                          columnNumber: 65\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 404,\n                        columnNumber: 61\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-1\",\n                        children: [/*#__PURE__*/_jsxDEV(TbUsers, {\n                          className: \"w-4 h-4\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 409,\n                          columnNumber: 65\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: user.userClass || 'Not Enrolled'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 410,\n                          columnNumber: 65\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 408,\n                        columnNumber: 61\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 403,\n                      columnNumber: 57\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 366,\n                    columnNumber: 53\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-right\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `text-2xl font-bold ${isCurrentUser ? 'text-primary-600' : 'text-gray-900'}`,\n                      children: user.score\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 417,\n                      columnNumber: 57\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-500\",\n                      children: \"points\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 422,\n                      columnNumber: 57\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 416,\n                    columnNumber: 53\n                  }, this)]\n                }, user.userId, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 49\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 29\n        }, this) : /*#__PURE__*/_jsxDEV(Card, {\n          className: \"p-12 text-center\",\n          children: [/*#__PURE__*/_jsxDEV(TbChartBar, {\n            className: \"w-16 h-16 text-gray-400 mx-auto mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-semibold text-gray-900 mb-2\",\n            children: \"No Rankings Yet\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Complete some quizzes to see your ranking on the leaderboard!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 431,\n          columnNumber: 29\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 17\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 107,\n    columnNumber: 9\n  }, this);\n};\n_s(Ranking, \"go+TI1mL+JKEHb4M2IP7GD/Gias=\", false, function () {\n  return [useDispatch];\n});\n_c = Ranking;\nexport default Ranking;\nvar _c;\n$RefreshReg$(_c, \"Ranking\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "motion", "AnimatePresence", "getAllReportsForRanking", "getUserInfo", "message", "Page<PERSON><PERSON>le", "useDispatch", "HideLoading", "ShowLoading", "Card", "<PERSON><PERSON>", "Loading", "image", "IoPersonCircleOutline", "TbTrophy", "TbMedal", "TbCrown", "TbUsers", "TbSchool", "TbStar", "TbChartBar", "TbUser", "TbAward", "jsxDEV", "_jsxDEV", "Ranking", "_s", "rankingData", "setRankingData", "userRanking", "setUserRanking", "userData", "setUserData", "isAdmin", "setIsAdmin", "isMobile", "setIsMobile", "activeTab", "setActiveTab", "dispatch", "fetchReports", "response", "success", "data", "error", "getUserData", "window", "innerWidth", "localStorage", "getItem", "getUserStats", "map", "user", "index", "ranking", "filter", "item", "userId", "includes", "_id", "formatMobileUserId", "prefix", "slice", "suffix", "className", "children", "div", "initial", "opacity", "y", "animate", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "transition", "delay", "button", "whileHover", "scale", "whileTap", "onClick", "layoutId", "length", "style", "animationDelay", "class", "userClass", "positions", "actualPosition", "indexOf", "heights", "badges", "icon", "color", "title", "userPhoto", "src", "alt", "userName", "totalPoints", "isCurrentUser", "getRankBadge", "position", "bg", "border", "glow", "rankInfo", "x", "onError", "e", "target", "passedExamsCount", "quizzesTaken", "userSchool", "score", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Ranking/index.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport './index.css'\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { getAllReportsForRanking } from \"../../../apicalls/reports\";\r\nimport { getUserInfo } from \"../../../apicalls/users\";\r\nimport { message } from \"antd\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { Card, Button, Loading } from \"../../../components/modern\";\r\nimport image from '../../../assets/person.png';\r\nimport { IoPersonCircleOutline } from \"react-icons/io5\";\r\nimport {\r\n  TbTrophy,\r\n  TbMedal,\r\n  TbCrown,\r\n  TbUsers,\r\n  TbSchool,\r\n  TbStar,\r\n  TbChartBar,\r\n  TbUser,\r\n  TbAward\r\n} from \"react-icons/tb\";\r\n\r\nconst Ranking = () => {\r\n    const [rankingData, setRankingData] = useState('');\r\n    const [userRanking, setUserRanking] = useState('');\r\n    const [userData, setUserData] = useState('');\r\n    const [isAdmin, setIsAdmin] = useState(false);\r\n    const [isMobile, setIsMobile] = useState(false);\r\n    const [activeTab, setActiveTab] = useState(\"overall\"); // \"overall\" or \"class\"\r\n\r\n\r\n    const dispatch = useDispatch();\r\n\r\n    const fetchReports = async () => {\r\n        try {\r\n            const response = await getAllReportsForRanking();\r\n            if (response.success) {\r\n                setRankingData(response.data);\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n    }\r\n\r\n\r\n    const getUserData = async () => {\r\n        try {\r\n            const response = await getUserInfo();\r\n            if (response.success) {\r\n                if (response.data.isAdmin) {\r\n                    setIsAdmin(true);\r\n                } else {\r\n                    setIsAdmin(false);\r\n                    setUserData(response.data);\r\n                    await fetchReports();\r\n                    dispatch(HideLoading());\r\n                }\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (window.innerWidth < 700) {\r\n            setIsMobile(true);\r\n        }\r\n        else {\r\n            setIsMobile(false);\r\n        }\r\n        if (localStorage.getItem(\"token\")) {\r\n            dispatch(ShowLoading());\r\n            getUserData();\r\n        }\r\n    }, []);\r\n\r\n    const getUserStats = () => {\r\n        const Ranking = rankingData\r\n            .map((user, index) => ({\r\n                user,\r\n                ranking: index + 1,\r\n            }))\r\n            .filter((item) => item.user.userId.includes(userData._id));\r\n        setUserRanking(Ranking);\r\n    }\r\n\r\n    useEffect(() => {\r\n        if (rankingData) {\r\n            getUserStats();\r\n        }\r\n    }, [rankingData]);\r\n\r\n    // Helper function to format user ID for mobile devices\r\n    const formatMobileUserId = (userId) => {\r\n        const prefix = userId.slice(0, 4);\r\n        const suffix = userId.slice(-4);\r\n        return `${prefix}.....${suffix}`;\r\n    };\r\n\r\n    return (\r\n        <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50\">\r\n            {!isAdmin && (\r\n                <div className=\"container-modern py-8\">\r\n                    {/* Modern Header */}\r\n                    <motion.div\r\n                        initial={{ opacity: 0, y: -20 }}\r\n                        animate={{ opacity: 1, y: 0 }}\r\n                        className=\"text-center mb-8\"\r\n                    >\r\n                        <div className=\"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full mb-4\">\r\n                            <TbTrophy className=\"w-8 h-8 text-white\" />\r\n                        </div>\r\n                        <h1 className=\"heading-2 text-gradient mb-4\">Leaderboard</h1>\r\n                        <p className=\"text-xl text-gray-600\">\r\n                            See how you rank against other students\r\n                        </p>\r\n                    </motion.div>\r\n\r\n                    {/* Modern Tabs */}\r\n                    <motion.div\r\n                        initial={{ opacity: 0, y: 20 }}\r\n                        animate={{ opacity: 1, y: 0 }}\r\n                        transition={{ delay: 0.2 }}\r\n                        className=\"mb-8\"\r\n                    >\r\n                        <Card className=\"p-2\">\r\n                            <div className=\"flex gap-2\">\r\n                                <motion.button\r\n                                    whileHover={{ scale: 1.02 }}\r\n                                    whileTap={{ scale: 0.98 }}\r\n                                    className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 flex-1 justify-center ${\r\n                                        activeTab === \"overall\"\r\n                                            ? 'bg-primary-600 text-white shadow-md'\r\n                                            : 'text-gray-600 hover:bg-gray-100'\r\n                                    }`}\r\n                                    onClick={() => setActiveTab(\"overall\")}\r\n                                >\r\n                                    <TbUsers className=\"w-5 h-5\" />\r\n                                    <span>Overall Ranking</span>\r\n                                    {activeTab === \"overall\" && (\r\n                                        <motion.div\r\n                                            layoutId=\"activeRankingTab\"\r\n                                            className=\"w-2 h-2 bg-white rounded-full\"\r\n                                        />\r\n                                    )}\r\n                                </motion.button>\r\n                                <motion.button\r\n                                    whileHover={{ scale: 1.02 }}\r\n                                    whileTap={{ scale: 0.98 }}\r\n                                    className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 flex-1 justify-center ${\r\n                                        activeTab === \"class\"\r\n                                            ? 'bg-primary-600 text-white shadow-md'\r\n                                            : 'text-gray-600 hover:bg-gray-100'\r\n                                    }`}\r\n                                    onClick={() => setActiveTab(\"class\")}\r\n                                >\r\n                                    <TbSchool className=\"w-5 h-5\" />\r\n                                    <span>Class Ranking</span>\r\n                                    {activeTab === \"class\" && (\r\n                                        <motion.div\r\n                                            layoutId=\"activeRankingTab\"\r\n                                            className=\"w-2 h-2 bg-white rounded-full\"\r\n                                        />\r\n                                    )}\r\n                                </motion.button>\r\n                            </div>\r\n                        </Card>\r\n                    </motion.div>\r\n\r\n                    {/* Modern Leaderboard */}\r\n                    <motion.div\r\n                        initial={{ opacity: 0, y: 20 }}\r\n                        animate={{ opacity: 1, y: 0 }}\r\n                        transition={{ delay: 0.4 }}\r\n                    >\r\n                        {rankingData.length > 0 ? (\r\n                            <Card className=\"overflow-hidden\">\r\n                                {/* Enhanced Leaderboard Header */}\r\n                                <div className=\"bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 text-white p-6 relative overflow-hidden\">\r\n                                    {/* Background Pattern */}\r\n                                    <div className=\"absolute inset-0 opacity-10\">\r\n                                        <div className=\"absolute top-4 left-4 text-6xl\">🏆</div>\r\n                                        <div className=\"absolute top-8 right-8 text-4xl\">⭐</div>\r\n                                        <div className=\"absolute bottom-4 left-1/4 text-3xl\">🎯</div>\r\n                                        <div className=\"absolute bottom-6 right-1/3 text-5xl\">💎</div>\r\n                                    </div>\r\n\r\n                                    <div className=\"relative z-10\">\r\n                                        <div className=\"flex items-center justify-center space-x-3 mb-2\">\r\n                                            <TbTrophy className=\"w-10 h-10 text-yellow-300 animate-bounce\" />\r\n                                            <h2 className=\"text-3xl font-black bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent\">\r\n                                                {activeTab === \"overall\" ? \"Global Leaderboard\" : \"Class Champions\"}\r\n                                            </h2>\r\n                                            <TbTrophy className=\"w-10 h-10 text-yellow-300 animate-bounce\" style={{ animationDelay: '0.5s' }} />\r\n                                        </div>\r\n                                        <p className=\"text-center text-blue-100 text-lg font-semibold\">\r\n                                            {activeTab === \"overall\"\r\n                                                ? \"🌟 Elite performers from all classes competing for glory! 🌟\"\r\n                                                : `🎓 Class ${userData?.class || 'your class'} top achievers! 🎓`\r\n                                            }\r\n                                        </p>\r\n                                        <div className=\"text-center mt-3\">\r\n                                            <span className=\"bg-white/20 px-4 py-2 rounded-full text-sm font-bold\">\r\n                                                💪 Earn points by acing quizzes • 🏅 Climb the ranks • 👑 Become a champion!\r\n                                            </span>\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n\r\n                                {/* Top 3 Podium - Responsive */}\r\n                                <div className=\"bg-gradient-to-br from-blue-50 to-purple-50 p-3 sm:p-6 border-b\">\r\n                                    <h3 className=\"text-lg sm:text-xl font-bold text-center mb-4 sm:mb-6 text-gray-800\">🏆 Champions Podium 🏆</h3>\r\n                                    <div className=\"flex justify-center items-end space-x-2 sm:space-x-4 overflow-x-auto pb-2\">\r\n                                        {(activeTab === \"overall\"\r\n                                            ? rankingData.slice(0, 3)\r\n                                            : rankingData.filter(user => user.userClass === userData?.class).slice(0, 3)\r\n                                        ).map((user, index) => {\r\n                                            const positions = [1, 0, 2]; // Silver, Diamond, Bronze order for visual appeal\r\n                                            const actualPosition = positions.indexOf(index);\r\n                                            const heights = ['h-16 sm:h-20 md:h-24', 'h-20 sm:h-24 md:h-32', 'h-12 sm:h-16 md:h-20']; // Responsive heights for podium effect\r\n                                            const badges = [\r\n                                                { icon: \"🥈\", color: \"from-gray-400 to-gray-600\", title: \"Silver\" },\r\n                                                { icon: \"💎\", color: \"from-blue-400 to-cyan-600\", title: \"Diamond\" },\r\n                                                { icon: \"🥉\", color: \"from-amber-400 to-orange-600\", title: \"Bronze\" }\r\n                                            ];\r\n\r\n                                            return (\r\n                                                <motion.div\r\n                                                    key={user.userId}\r\n                                                    initial={{ opacity: 0, y: 50 }}\r\n                                                    animate={{ opacity: 1, y: 0 }}\r\n                                                    transition={{ delay: index * 0.2 }}\r\n                                                    className=\"text-center\"\r\n                                                >\r\n                                                    {/* User Avatar - Responsive */}\r\n                                                    <div className=\"mb-2 sm:mb-3\">\r\n                                                        <div className=\"w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 mx-auto rounded-full overflow-hidden border-2 sm:border-4 border-white shadow-lg\">\r\n                                                            {user.userPhoto ? (\r\n                                                                <img src={user.userPhoto} alt=\"profile\" className=\"w-full h-full object-cover\" />\r\n                                                            ) : (\r\n                                                                <div className=\"w-full h-full bg-gray-200 flex items-center justify-center\">\r\n                                                                    <TbUser className=\"w-4 h-4 sm:w-6 sm:h-6 md:w-8 md:h-8 text-gray-400\" />\r\n                                                                </div>\r\n                                                            )}\r\n                                                        </div>\r\n                                                        <div className=\"text-lg sm:text-xl md:text-2xl mt-1 sm:mt-2\">{badges[index].icon}</div>\r\n                                                    </div>\r\n\r\n                                                    {/* Podium - Responsive */}\r\n                                                    <div className={`${heights[index]} w-16 sm:w-18 md:w-20 bg-gradient-to-t ${badges[index].color} rounded-t-lg flex flex-col justify-end p-1 sm:p-2 shadow-lg`}>\r\n                                                        <div className=\"text-white text-center\">\r\n                                                            <div className=\"font-bold text-sm sm:text-base md:text-lg\">{actualPosition + 1}</div>\r\n                                                            <div className=\"text-xs hidden sm:block\">{badges[index].title}</div>\r\n                                                        </div>\r\n                                                    </div>\r\n\r\n                                                    {/* User Info - Responsive */}\r\n                                                    <div className=\"mt-1 sm:mt-2\">\r\n                                                        <div className=\"font-bold text-xs sm:text-sm truncate w-16 sm:w-18 md:w-20\">{user.userName}</div>\r\n                                                        <div className=\"text-xs text-yellow-600 font-semibold\">{user.totalPoints || 0} pts</div>\r\n                                                    </div>\r\n                                                </motion.div>\r\n                                            );\r\n                                        })}\r\n                                    </div>\r\n                                </div>\r\n\r\n                                {/* Leaderboard Content - Responsive */}\r\n                                <div className=\"p-3 sm:p-4 md:p-6\">\r\n                                    <div className=\"space-y-3 sm:space-y-4\">\r\n                                        {(activeTab === \"overall\"\r\n                                            ? rankingData\r\n                                            : rankingData.filter(user => user.userClass === userData?.class)\r\n                                        ).map((user, index) => {\r\n                                            const isCurrentUser = user.userId.includes(userData?._id);\r\n                                            const getRankBadge = (position) => {\r\n                                                if (position === 0) return {\r\n                                                    icon: \"💎\",\r\n                                                    color: \"text-blue-600\",\r\n                                                    bg: \"bg-gradient-to-br from-blue-100 to-cyan-100\",\r\n                                                    border: \"border-blue-300\",\r\n                                                    title: \"Diamond\",\r\n                                                    glow: \"shadow-blue-200\"\r\n                                                };\r\n                                                if (position === 1) return {\r\n                                                    icon: \"🥈\",\r\n                                                    color: \"text-gray-600\",\r\n                                                    bg: \"bg-gradient-to-br from-gray-100 to-slate-100\",\r\n                                                    border: \"border-gray-300\",\r\n                                                    title: \"Silver\",\r\n                                                    glow: \"shadow-gray-200\"\r\n                                                };\r\n                                                if (position === 2) return {\r\n                                                    icon: \"🥉\",\r\n                                                    color: \"text-amber-600\",\r\n                                                    bg: \"bg-gradient-to-br from-amber-100 to-orange-100\",\r\n                                                    border: \"border-amber-300\",\r\n                                                    title: \"Bronze\",\r\n                                                    glow: \"shadow-amber-200\"\r\n                                                };\r\n                                                return {\r\n                                                    icon: position + 1,\r\n                                                    color: \"text-gray-600\",\r\n                                                    bg: \"bg-gray-50\",\r\n                                                    border: \"border-gray-200\",\r\n                                                    title: `Rank ${position + 1}`,\r\n                                                    glow: \"shadow-gray-100\"\r\n                                                };\r\n                                            };\r\n\r\n                                            const rankInfo = getRankBadge(index);\r\n\r\n                                            return (\r\n                                                <motion.div\r\n                                                    key={user.userId}\r\n                                                    initial={{ opacity: 0, x: -20 }}\r\n                                                    animate={{ opacity: 1, x: 0 }}\r\n                                                    transition={{ delay: index * 0.1 }}\r\n                                                    className={`flex items-center space-x-2 sm:space-x-3 md:space-x-4 p-3 sm:p-4 rounded-xl transition-all duration-200 ${\r\n                                                        isCurrentUser\r\n                                                            ? 'bg-primary-50 border-2 border-primary-200 shadow-md'\r\n                                                            : 'bg-gray-50 hover:bg-gray-100'\r\n                                                    }`}\r\n                                                >\r\n                                                    {/* Enhanced Rank Badge - Responsive */}\r\n                                                    <div className={`relative w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 rounded-full flex items-center justify-center ${rankInfo.bg} ${rankInfo.border} border-2 ${rankInfo.glow} shadow-lg transition-all duration-300 hover:scale-110 flex-shrink-0`}>\r\n                                                        {index < 3 ? (\r\n                                                            <div className=\"text-center\">\r\n                                                                <div className=\"text-lg sm:text-xl md:text-2xl mb-0.5 sm:mb-1\">{rankInfo.icon}</div>\r\n                                                                <div className={`text-xs font-bold ${rankInfo.color} hidden sm:block`}>{rankInfo.title}</div>\r\n                                                            </div>\r\n                                                        ) : (\r\n                                                            <div className=\"text-center\">\r\n                                                                <span className=\"font-black text-sm sm:text-base md:text-lg text-gray-700\">{rankInfo.icon}</span>\r\n                                                                <div className=\"text-xs font-semibold text-gray-500 hidden sm:block\">Rank</div>\r\n                                                            </div>\r\n                                                        )}\r\n\r\n                                                        {/* Glow effect for top 3 */}\r\n                                                        {index < 3 && (\r\n                                                            <div className={`absolute inset-0 rounded-full ${rankInfo.bg} opacity-50 blur-md -z-10 animate-pulse`}></div>\r\n                                                        )}\r\n                                                    </div>\r\n\r\n                                                    {/* Profile Picture */}\r\n                                                    <div className=\"w-12 h-12 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center\">\r\n                                                        {user.userPhoto ? (\r\n                                                            <img\r\n                                                                src={user.userPhoto}\r\n                                                                alt=\"profile\"\r\n                                                                className=\"w-full h-full object-cover\"\r\n                                                                onError={(e) => { e.target.src = image }}\r\n                                                            />\r\n                                                        ) : (\r\n                                                            <TbUser className=\"w-6 h-6 text-gray-400\" />\r\n                                                        )}\r\n                                                    </div>\r\n\r\n                                                    {/* Enhanced User Info */}\r\n                                                    <div className=\"flex-1 min-w-0\">\r\n                                                        <div className=\"flex items-center space-x-2 mb-2\">\r\n                                                            <h3 className={`font-bold text-lg truncate ${\r\n                                                                isCurrentUser ? 'text-primary-900' : 'text-gray-900'\r\n                                                            }`}>\r\n                                                                {user.userName}\r\n                                                            </h3>\r\n                                                            {isCurrentUser && (\r\n                                                                <span className=\"bg-gradient-to-r from-blue-500 to-purple-500 text-white text-xs px-2 py-1 rounded-full font-bold\">You</span>\r\n                                                            )}\r\n                                                            {index < 3 && (\r\n                                                                <span className={`text-xs px-2 py-1 rounded-full font-bold ${\r\n                                                                    index === 0 ? 'bg-blue-100 text-blue-800' :\r\n                                                                    index === 1 ? 'bg-gray-100 text-gray-800' :\r\n                                                                    'bg-amber-100 text-amber-800'\r\n                                                                }`}>\r\n                                                                    {rankInfo.title} Champion\r\n                                                                </span>\r\n                                                            )}\r\n                                                        </div>\r\n\r\n                                                        {/* Points and Stats */}\r\n                                                        <div className=\"flex items-center space-x-4 mb-2\">\r\n                                                            <div className=\"flex items-center space-x-1\">\r\n                                                                <div className=\"w-2 h-2 bg-yellow-400 rounded-full\"></div>\r\n                                                                <span className=\"font-bold text-yellow-600\">{user.totalPoints || 0} pts</span>\r\n                                                            </div>\r\n                                                            <div className=\"flex items-center space-x-1\">\r\n                                                                <div className=\"w-2 h-2 bg-green-400 rounded-full\"></div>\r\n                                                                <span className=\"font-semibold text-green-600\">{user.passedExamsCount || 0} passed</span>\r\n                                                            </div>\r\n                                                            <div className=\"flex items-center space-x-1\">\r\n                                                                <div className=\"w-2 h-2 bg-blue-400 rounded-full\"></div>\r\n                                                                <span className=\"font-semibold text-blue-600\">{user.quizzesTaken || 0} quizzes</span>\r\n                                                            </div>\r\n                                                        </div>\r\n\r\n                                                        <div className=\"flex items-center space-x-4 text-sm text-gray-600\">\r\n                                                            <div className=\"flex items-center space-x-1\">\r\n                                                                <TbSchool className=\"w-4 h-4\" />\r\n                                                                <span className=\"truncate\">{user.userSchool || 'Not Enrolled'}</span>\r\n                                                            </div>\r\n                                                            <div className=\"flex items-center space-x-1\">\r\n                                                                <TbUsers className=\"w-4 h-4\" />\r\n                                                                <span>{user.userClass || 'Not Enrolled'}</span>\r\n                                                            </div>\r\n                                                        </div>\r\n                                                    </div>\r\n\r\n                                                    {/* Score */}\r\n                                                    <div className=\"text-right\">\r\n                                                        <div className={`text-2xl font-bold ${\r\n                                                            isCurrentUser ? 'text-primary-600' : 'text-gray-900'\r\n                                                        }`}>\r\n                                                            {user.score}\r\n                                                        </div>\r\n                                                        <div className=\"text-xs text-gray-500\">points</div>\r\n                                                    </div>\r\n                                                </motion.div>\r\n                                            );\r\n                                        })}\r\n                                    </div>\r\n                                </div>\r\n                            </Card>\r\n                        ) : (\r\n                            <Card className=\"p-12 text-center\">\r\n                                <TbChartBar className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\r\n                                <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">No Rankings Yet</h3>\r\n                                <p className=\"text-gray-600\">\r\n                                    Complete some quizzes to see your ranking on the leaderboard!\r\n                                </p>\r\n                            </Card>\r\n                        )}\r\n                    </motion.div>\r\n\r\n                </div>\r\n            )}\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default Ranking;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAO,aAAa;AACpB,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,uBAAuB,QAAQ,2BAA2B;AACnE,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,OAAO,QAAQ,MAAM;AAC9B,OAAOC,SAAS,MAAM,+BAA+B;AACrD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,IAAI,EAAEC,MAAM,EAAEC,OAAO,QAAQ,4BAA4B;AAClE,OAAOC,KAAK,MAAM,4BAA4B;AAC9C,SAASC,qBAAqB,QAAQ,iBAAiB;AACvD,SACEC,QAAQ,EACRC,OAAO,EACPC,OAAO,EACPC,OAAO,EACPC,QAAQ,EACRC,MAAM,EACNC,UAAU,EACVC,MAAM,EACNC,OAAO,QACF,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACgC,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoC,QAAQ,EAAEC,WAAW,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACsC,SAAS,EAAEC,YAAY,CAAC,GAAGvC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;;EAGvD,MAAMwC,QAAQ,GAAGjC,WAAW,CAAC,CAAC;EAE9B,MAAMkC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMvC,uBAAuB,CAAC,CAAC;MAChD,IAAIuC,QAAQ,CAACC,OAAO,EAAE;QAClBd,cAAc,CAACa,QAAQ,CAACE,IAAI,CAAC;MACjC,CAAC,MAAM;QACHvC,OAAO,CAACwC,KAAK,CAACH,QAAQ,CAACrC,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAOwC,KAAK,EAAE;MACZxC,OAAO,CAACwC,KAAK,CAACA,KAAK,CAACxC,OAAO,CAAC;IAChC;EACJ,CAAC;EAGD,MAAMyC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACA,MAAMJ,QAAQ,GAAG,MAAMtC,WAAW,CAAC,CAAC;MACpC,IAAIsC,QAAQ,CAACC,OAAO,EAAE;QAClB,IAAID,QAAQ,CAACE,IAAI,CAACV,OAAO,EAAE;UACvBC,UAAU,CAAC,IAAI,CAAC;QACpB,CAAC,MAAM;UACHA,UAAU,CAAC,KAAK,CAAC;UACjBF,WAAW,CAACS,QAAQ,CAACE,IAAI,CAAC;UAC1B,MAAMH,YAAY,CAAC,CAAC;UACpBD,QAAQ,CAAChC,WAAW,CAAC,CAAC,CAAC;QAC3B;MACJ,CAAC,MAAM;QACHH,OAAO,CAACwC,KAAK,CAACH,QAAQ,CAACrC,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAOwC,KAAK,EAAE;MACZxC,OAAO,CAACwC,KAAK,CAACA,KAAK,CAACxC,OAAO,CAAC;IAChC;EACJ,CAAC;EAEDN,SAAS,CAAC,MAAM;IACZ,IAAIgD,MAAM,CAACC,UAAU,GAAG,GAAG,EAAE;MACzBX,WAAW,CAAC,IAAI,CAAC;IACrB,CAAC,MACI;MACDA,WAAW,CAAC,KAAK,CAAC;IACtB;IACA,IAAIY,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE;MAC/BV,QAAQ,CAAC/B,WAAW,CAAC,CAAC,CAAC;MACvBqC,WAAW,CAAC,CAAC;IACjB;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,YAAY,GAAGA,CAAA,KAAM;IACvB,MAAMzB,OAAO,GAAGE,WAAW,CACtBwB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,MAAM;MACnBD,IAAI;MACJE,OAAO,EAAED,KAAK,GAAG;IACrB,CAAC,CAAC,CAAC,CACFE,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACJ,IAAI,CAACK,MAAM,CAACC,QAAQ,CAAC3B,QAAQ,CAAC4B,GAAG,CAAC,CAAC;IAC9D7B,cAAc,CAACL,OAAO,CAAC;EAC3B,CAAC;EAED3B,SAAS,CAAC,MAAM;IACZ,IAAI6B,WAAW,EAAE;MACbuB,YAAY,CAAC,CAAC;IAClB;EACJ,CAAC,EAAE,CAACvB,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAMiC,kBAAkB,GAAIH,MAAM,IAAK;IACnC,MAAMI,MAAM,GAAGJ,MAAM,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IACjC,MAAMC,MAAM,GAAGN,MAAM,CAACK,KAAK,CAAC,CAAC,CAAC,CAAC;IAC/B,OAAQ,GAAED,MAAO,QAAOE,MAAO,EAAC;EACpC,CAAC;EAED,oBACIvC,OAAA;IAAKwC,SAAS,EAAC,wDAAwD;IAAAC,QAAA,EAClE,CAAChC,OAAO,iBACLT,OAAA;MAAKwC,SAAS,EAAC,uBAAuB;MAAAC,QAAA,gBAElCzC,OAAA,CAACxB,MAAM,CAACkE,GAAG;QACPC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BL,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAE5BzC,OAAA;UAAKwC,SAAS,EAAC,oHAAoH;UAAAC,QAAA,eAC/HzC,OAAA,CAACV,QAAQ;YAACkD,SAAS,EAAC;UAAoB;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACNlD,OAAA;UAAIwC,SAAS,EAAC,8BAA8B;UAAAC,QAAA,EAAC;QAAW;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7DlD,OAAA;UAAGwC,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAErC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGblD,OAAA,CAACxB,MAAM,CAACkE,GAAG;QACPC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BM,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3BZ,SAAS,EAAC,MAAM;QAAAC,QAAA,eAEhBzC,OAAA,CAACf,IAAI;UAACuD,SAAS,EAAC,KAAK;UAAAC,QAAA,eACjBzC,OAAA;YAAKwC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACvBzC,OAAA,CAACxB,MAAM,CAAC6E,MAAM;cACVC,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAC1Bf,SAAS,EAAG,kHACR3B,SAAS,KAAK,SAAS,GACjB,qCAAqC,GACrC,iCACT,EAAE;cACH4C,OAAO,EAAEA,CAAA,KAAM3C,YAAY,CAAC,SAAS,CAAE;cAAA2B,QAAA,gBAEvCzC,OAAA,CAACP,OAAO;gBAAC+C,SAAS,EAAC;cAAS;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/BlD,OAAA;gBAAAyC,QAAA,EAAM;cAAe;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAC3BrC,SAAS,KAAK,SAAS,iBACpBb,OAAA,CAACxB,MAAM,CAACkE,GAAG;gBACPgB,QAAQ,EAAC,kBAAkB;gBAC3BlB,SAAS,EAAC;cAA+B;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU,CAAC,eAChBlD,OAAA,CAACxB,MAAM,CAAC6E,MAAM;cACVC,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAC1Bf,SAAS,EAAG,kHACR3B,SAAS,KAAK,OAAO,GACf,qCAAqC,GACrC,iCACT,EAAE;cACH4C,OAAO,EAAEA,CAAA,KAAM3C,YAAY,CAAC,OAAO,CAAE;cAAA2B,QAAA,gBAErCzC,OAAA,CAACN,QAAQ;gBAAC8C,SAAS,EAAC;cAAS;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChClD,OAAA;gBAAAyC,QAAA,EAAM;cAAa;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EACzBrC,SAAS,KAAK,OAAO,iBAClBb,OAAA,CAACxB,MAAM,CAACkE,GAAG;gBACPgB,QAAQ,EAAC,kBAAkB;gBAC3BlB,SAAS,EAAC;cAA+B;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGblD,OAAA,CAACxB,MAAM,CAACkE,GAAG;QACPC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BM,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAAX,QAAA,EAE1BtC,WAAW,CAACwD,MAAM,GAAG,CAAC,gBACnB3D,OAAA,CAACf,IAAI;UAACuD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAE7BzC,OAAA;YAAKwC,SAAS,EAAC,qGAAqG;YAAAC,QAAA,gBAEhHzC,OAAA;cAAKwC,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBACxCzC,OAAA;gBAAKwC,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAAC;cAAE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxDlD,OAAA;gBAAKwC,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAAC;cAAC;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxDlD,OAAA;gBAAKwC,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAC;cAAE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7DlD,OAAA;gBAAKwC,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAAE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC,eAENlD,OAAA;cAAKwC,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC1BzC,OAAA;gBAAKwC,SAAS,EAAC,iDAAiD;gBAAAC,QAAA,gBAC5DzC,OAAA,CAACV,QAAQ;kBAACkD,SAAS,EAAC;gBAA0C;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjElD,OAAA;kBAAIwC,SAAS,EAAC,kGAAkG;kBAAAC,QAAA,EAC3G5B,SAAS,KAAK,SAAS,GAAG,oBAAoB,GAAG;gBAAiB;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CAAC,eACLlD,OAAA,CAACV,QAAQ;kBAACkD,SAAS,EAAC,0CAA0C;kBAACoB,KAAK,EAAE;oBAAEC,cAAc,EAAE;kBAAO;gBAAE;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnG,CAAC,eACNlD,OAAA;gBAAGwC,SAAS,EAAC,iDAAiD;gBAAAC,QAAA,EACzD5B,SAAS,KAAK,SAAS,GAClB,8DAA8D,GAC7D,YAAW,CAAAN,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEuD,KAAK,KAAI,YAAa;cAAmB;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEtE,CAAC,eACJlD,OAAA;gBAAKwC,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,eAC7BzC,OAAA;kBAAMwC,SAAS,EAAC,sDAAsD;kBAAAC,QAAA,EAAC;gBAEvE;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGNlD,OAAA;YAAKwC,SAAS,EAAC,iEAAiE;YAAAC,QAAA,gBAC5EzC,OAAA;cAAIwC,SAAS,EAAC,qEAAqE;cAAAC,QAAA,EAAC;YAAsB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/GlD,OAAA;cAAKwC,SAAS,EAAC,2EAA2E;cAAAC,QAAA,EACrF,CAAC5B,SAAS,KAAK,SAAS,GACnBV,WAAW,CAACmC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GACvBnC,WAAW,CAAC4B,MAAM,CAACH,IAAI,IAAIA,IAAI,CAACmC,SAAS,MAAKxD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEuD,KAAK,EAAC,CAACxB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAC9EX,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;gBACnB,MAAMmC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBAC7B,MAAMC,cAAc,GAAGD,SAAS,CAACE,OAAO,CAACrC,KAAK,CAAC;gBAC/C,MAAMsC,OAAO,GAAG,CAAC,sBAAsB,EAAE,sBAAsB,EAAE,sBAAsB,CAAC,CAAC,CAAC;gBAC1F,MAAMC,MAAM,GAAG,CACX;kBAAEC,IAAI,EAAE,IAAI;kBAAEC,KAAK,EAAE,2BAA2B;kBAAEC,KAAK,EAAE;gBAAS,CAAC,EACnE;kBAAEF,IAAI,EAAE,IAAI;kBAAEC,KAAK,EAAE,2BAA2B;kBAAEC,KAAK,EAAE;gBAAU,CAAC,EACpE;kBAAEF,IAAI,EAAE,IAAI;kBAAEC,KAAK,EAAE,8BAA8B;kBAAEC,KAAK,EAAE;gBAAS,CAAC,CACzE;gBAED,oBACIvE,OAAA,CAACxB,MAAM,CAACkE,GAAG;kBAEPC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAG,CAAE;kBAC/BC,OAAO,EAAE;oBAAEF,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAE,CAAE;kBAC9BM,UAAU,EAAE;oBAAEC,KAAK,EAAEvB,KAAK,GAAG;kBAAI,CAAE;kBACnCW,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAGvBzC,OAAA;oBAAKwC,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBACzBzC,OAAA;sBAAKwC,SAAS,EAAC,4HAA4H;sBAAAC,QAAA,EACtIb,IAAI,CAAC4C,SAAS,gBACXxE,OAAA;wBAAKyE,GAAG,EAAE7C,IAAI,CAAC4C,SAAU;wBAACE,GAAG,EAAC,SAAS;wBAAClC,SAAS,EAAC;sBAA4B;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,gBAEjFlD,OAAA;wBAAKwC,SAAS,EAAC,4DAA4D;wBAAAC,QAAA,eACvEzC,OAAA,CAACH,MAAM;0BAAC2C,SAAS,EAAC;wBAAmD;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvE;oBACR;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC,eACNlD,OAAA;sBAAKwC,SAAS,EAAC,6CAA6C;sBAAAC,QAAA,EAAE2B,MAAM,CAACvC,KAAK,CAAC,CAACwC;oBAAI;sBAAAtB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtF,CAAC,eAGNlD,OAAA;oBAAKwC,SAAS,EAAG,GAAE2B,OAAO,CAACtC,KAAK,CAAE,0CAAyCuC,MAAM,CAACvC,KAAK,CAAC,CAACyC,KAAM,8DAA8D;oBAAA7B,QAAA,eACzJzC,OAAA;sBAAKwC,SAAS,EAAC,wBAAwB;sBAAAC,QAAA,gBACnCzC,OAAA;wBAAKwC,SAAS,EAAC,2CAA2C;wBAAAC,QAAA,EAAEwB,cAAc,GAAG;sBAAC;wBAAAlB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACrFlD,OAAA;wBAAKwC,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EAAE2B,MAAM,CAACvC,KAAK,CAAC,CAAC0C;sBAAK;wBAAAxB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eAGNlD,OAAA;oBAAKwC,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBACzBzC,OAAA;sBAAKwC,SAAS,EAAC,4DAA4D;sBAAAC,QAAA,EAAEb,IAAI,CAAC+C;oBAAQ;sBAAA5B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjGlD,OAAA;sBAAKwC,SAAS,EAAC,uCAAuC;sBAAAC,QAAA,GAAEb,IAAI,CAACgD,WAAW,IAAI,CAAC,EAAC,MAAI;oBAAA;sBAAA7B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvF,CAAC;gBAAA,GAhCDtB,IAAI,CAACK,MAAM;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAiCR,CAAC;cAErB,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGNlD,OAAA;YAAKwC,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAC9BzC,OAAA;cAAKwC,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAClC,CAAC5B,SAAS,KAAK,SAAS,GACnBV,WAAW,GACXA,WAAW,CAAC4B,MAAM,CAACH,IAAI,IAAIA,IAAI,CAACmC,SAAS,MAAKxD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEuD,KAAK,EAAC,EAClEnC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;gBACnB,MAAMgD,aAAa,GAAGjD,IAAI,CAACK,MAAM,CAACC,QAAQ,CAAC3B,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE4B,GAAG,CAAC;gBACzD,MAAM2C,YAAY,GAAIC,QAAQ,IAAK;kBAC/B,IAAIA,QAAQ,KAAK,CAAC,EAAE,OAAO;oBACvBV,IAAI,EAAE,IAAI;oBACVC,KAAK,EAAE,eAAe;oBACtBU,EAAE,EAAE,6CAA6C;oBACjDC,MAAM,EAAE,iBAAiB;oBACzBV,KAAK,EAAE,SAAS;oBAChBW,IAAI,EAAE;kBACV,CAAC;kBACD,IAAIH,QAAQ,KAAK,CAAC,EAAE,OAAO;oBACvBV,IAAI,EAAE,IAAI;oBACVC,KAAK,EAAE,eAAe;oBACtBU,EAAE,EAAE,8CAA8C;oBAClDC,MAAM,EAAE,iBAAiB;oBACzBV,KAAK,EAAE,QAAQ;oBACfW,IAAI,EAAE;kBACV,CAAC;kBACD,IAAIH,QAAQ,KAAK,CAAC,EAAE,OAAO;oBACvBV,IAAI,EAAE,IAAI;oBACVC,KAAK,EAAE,gBAAgB;oBACvBU,EAAE,EAAE,gDAAgD;oBACpDC,MAAM,EAAE,kBAAkB;oBAC1BV,KAAK,EAAE,QAAQ;oBACfW,IAAI,EAAE;kBACV,CAAC;kBACD,OAAO;oBACHb,IAAI,EAAEU,QAAQ,GAAG,CAAC;oBAClBT,KAAK,EAAE,eAAe;oBACtBU,EAAE,EAAE,YAAY;oBAChBC,MAAM,EAAE,iBAAiB;oBACzBV,KAAK,EAAG,QAAOQ,QAAQ,GAAG,CAAE,EAAC;oBAC7BG,IAAI,EAAE;kBACV,CAAC;gBACL,CAAC;gBAED,MAAMC,QAAQ,GAAGL,YAAY,CAACjD,KAAK,CAAC;gBAEpC,oBACI7B,OAAA,CAACxB,MAAM,CAACkE,GAAG;kBAEPC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEwC,CAAC,EAAE,CAAC;kBAAG,CAAE;kBAChCtC,OAAO,EAAE;oBAAEF,OAAO,EAAE,CAAC;oBAAEwC,CAAC,EAAE;kBAAE,CAAE;kBAC9BjC,UAAU,EAAE;oBAAEC,KAAK,EAAEvB,KAAK,GAAG;kBAAI,CAAE;kBACnCW,SAAS,EAAG,2GACRqC,aAAa,GACP,qDAAqD,GACrD,8BACT,EAAE;kBAAApC,QAAA,gBAGHzC,OAAA;oBAAKwC,SAAS,EAAG,oGAAmG2C,QAAQ,CAACH,EAAG,IAAGG,QAAQ,CAACF,MAAO,aAAYE,QAAQ,CAACD,IAAK,sEAAsE;oBAAAzC,QAAA,GAC9OZ,KAAK,GAAG,CAAC,gBACN7B,OAAA;sBAAKwC,SAAS,EAAC,aAAa;sBAAAC,QAAA,gBACxBzC,OAAA;wBAAKwC,SAAS,EAAC,+CAA+C;wBAAAC,QAAA,EAAE0C,QAAQ,CAACd;sBAAI;wBAAAtB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACpFlD,OAAA;wBAAKwC,SAAS,EAAG,qBAAoB2C,QAAQ,CAACb,KAAM,kBAAkB;wBAAA7B,QAAA,EAAE0C,QAAQ,CAACZ;sBAAK;wBAAAxB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5F,CAAC,gBAENlD,OAAA;sBAAKwC,SAAS,EAAC,aAAa;sBAAAC,QAAA,gBACxBzC,OAAA;wBAAMwC,SAAS,EAAC,0DAA0D;wBAAAC,QAAA,EAAE0C,QAAQ,CAACd;sBAAI;wBAAAtB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACjGlD,OAAA;wBAAKwC,SAAS,EAAC,qDAAqD;wBAAAC,QAAA,EAAC;sBAAI;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9E,CACR,EAGArB,KAAK,GAAG,CAAC,iBACN7B,OAAA;sBAAKwC,SAAS,EAAG,iCAAgC2C,QAAQ,CAACH,EAAG;oBAAyC;sBAAAjC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAC/G;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eAGNlD,OAAA;oBAAKwC,SAAS,EAAC,qFAAqF;oBAAAC,QAAA,EAC/Fb,IAAI,CAAC4C,SAAS,gBACXxE,OAAA;sBACIyE,GAAG,EAAE7C,IAAI,CAAC4C,SAAU;sBACpBE,GAAG,EAAC,SAAS;sBACblC,SAAS,EAAC,4BAA4B;sBACtC6C,OAAO,EAAGC,CAAC,IAAK;wBAAEA,CAAC,CAACC,MAAM,CAACd,GAAG,GAAGrF,KAAK;sBAAC;oBAAE;sBAAA2D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC,gBAEFlD,OAAA,CAACH,MAAM;sBAAC2C,SAAS,EAAC;oBAAuB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAC9C;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eAGNlD,OAAA;oBAAKwC,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBAC3BzC,OAAA;sBAAKwC,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,gBAC7CzC,OAAA;wBAAIwC,SAAS,EAAG,8BACZqC,aAAa,GAAG,kBAAkB,GAAG,eACxC,EAAE;wBAAApC,QAAA,EACEb,IAAI,CAAC+C;sBAAQ;wBAAA5B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACd,CAAC,EACJ2B,aAAa,iBACV7E,OAAA;wBAAMwC,SAAS,EAAC,kGAAkG;wBAAAC,QAAA,EAAC;sBAAG;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAC/H,EACArB,KAAK,GAAG,CAAC,iBACN7B,OAAA;wBAAMwC,SAAS,EAAG,4CACdX,KAAK,KAAK,CAAC,GAAG,2BAA2B,GACzCA,KAAK,KAAK,CAAC,GAAG,2BAA2B,GACzC,6BACH,EAAE;wBAAAY,QAAA,GACE0C,QAAQ,CAACZ,KAAK,EAAC,WACpB;sBAAA;wBAAAxB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CACT;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC,eAGNlD,OAAA;sBAAKwC,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,gBAC7CzC,OAAA;wBAAKwC,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,gBACxCzC,OAAA;0BAAKwC,SAAS,EAAC;wBAAoC;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eAC1DlD,OAAA;0BAAMwC,SAAS,EAAC,2BAA2B;0BAAAC,QAAA,GAAEb,IAAI,CAACgD,WAAW,IAAI,CAAC,EAAC,MAAI;wBAAA;0BAAA7B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7E,CAAC,eACNlD,OAAA;wBAAKwC,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,gBACxCzC,OAAA;0BAAKwC,SAAS,EAAC;wBAAmC;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACzDlD,OAAA;0BAAMwC,SAAS,EAAC,8BAA8B;0BAAAC,QAAA,GAAEb,IAAI,CAAC4D,gBAAgB,IAAI,CAAC,EAAC,SAAO;wBAAA;0BAAAzC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxF,CAAC,eACNlD,OAAA;wBAAKwC,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,gBACxCzC,OAAA;0BAAKwC,SAAS,EAAC;wBAAkC;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACxDlD,OAAA;0BAAMwC,SAAS,EAAC,6BAA6B;0BAAAC,QAAA,GAAEb,IAAI,CAAC6D,YAAY,IAAI,CAAC,EAAC,UAAQ;wBAAA;0BAAA1C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eAENlD,OAAA;sBAAKwC,SAAS,EAAC,mDAAmD;sBAAAC,QAAA,gBAC9DzC,OAAA;wBAAKwC,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,gBACxCzC,OAAA,CAACN,QAAQ;0BAAC8C,SAAS,EAAC;wBAAS;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAChClD,OAAA;0BAAMwC,SAAS,EAAC,UAAU;0BAAAC,QAAA,EAAEb,IAAI,CAAC8D,UAAU,IAAI;wBAAc;0BAAA3C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpE,CAAC,eACNlD,OAAA;wBAAKwC,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,gBACxCzC,OAAA,CAACP,OAAO;0BAAC+C,SAAS,EAAC;wBAAS;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC/BlD,OAAA;0BAAAyC,QAAA,EAAOb,IAAI,CAACmC,SAAS,IAAI;wBAAc;0BAAAhB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9C,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eAGNlD,OAAA;oBAAKwC,SAAS,EAAC,YAAY;oBAAAC,QAAA,gBACvBzC,OAAA;sBAAKwC,SAAS,EAAG,sBACbqC,aAAa,GAAG,kBAAkB,GAAG,eACxC,EAAE;sBAAApC,QAAA,EACEb,IAAI,CAAC+D;oBAAK;sBAAA5C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACNlD,OAAA;sBAAKwC,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAM;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC;gBAAA,GAtGDtB,IAAI,CAACK,MAAM;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAuGR,CAAC;cAErB,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,gBAEPlD,OAAA,CAACf,IAAI;UAACuD,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC9BzC,OAAA,CAACJ,UAAU;YAAC4C,SAAS,EAAC;UAAsC;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/DlD,OAAA;YAAIwC,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAe;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7ElD,OAAA;YAAGwC,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAE7B;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEZ;EACR;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAAhD,EAAA,CApaKD,OAAO;EAAA,QASQnB,WAAW;AAAA;AAAA8G,EAAA,GAT1B3F,OAAO;AAsab,eAAeA,OAAO;AAAC,IAAA2F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}