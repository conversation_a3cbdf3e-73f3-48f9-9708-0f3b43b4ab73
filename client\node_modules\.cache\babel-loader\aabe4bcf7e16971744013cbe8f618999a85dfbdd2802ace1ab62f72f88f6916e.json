{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\QuizResult.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate, useLocation } from 'react-router-dom';\nimport { useDispatch } from 'react-redux';\nimport { message } from 'antd';\nimport Confetti from 'react-confetti';\nimport useWindowSize from 'react-use/lib/useWindowSize';\nimport { TbArrowLeft, TbCheck, TbX, TbTrophy, TbBrain, TbTarget, TbClock, TbRefresh, TbEye, TbBulb } from 'react-icons/tb';\nimport { getExamById } from '../../../apicalls/exams';\nimport { chatWithChatGPTToExplainAns } from '../../../apicalls/chat';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport ContentRenderer from '../../../components/ContentRenderer';\nimport Pass from '../../../assets/pass.gif';\nimport Fail from '../../../assets/fail.gif';\nimport PassSound from '../../../assets/pass.mp3';\nimport FailSound from '../../../assets/fail.mp3';\nimport './responsive.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuizResult = () => {\n  _s();\n  var _location$state, _result$correctAnswer2, _result$wrongAnswers, _result$correctAnswer3, _result$correctAnswer4;\n  const [examData, setExamData] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [explanations, setExplanations] = useState({});\n  const [showReview, setShowReview] = useState(false);\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  const {\n    width,\n    height\n  } = useWindowSize();\n  const result = (_location$state = location.state) === null || _location$state === void 0 ? void 0 : _location$state.result;\n  useEffect(() => {\n    const fetchExamData = async () => {\n      try {\n        dispatch(ShowLoading());\n        const response = await getExamById({\n          examId: id\n        });\n        dispatch(HideLoading());\n        if (response.success) {\n          var _response$data;\n          setExamData(response.data);\n          setQuestions(((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.questions) || []);\n        } else {\n          message.error(response.message);\n          navigate('/user/quiz');\n        }\n      } catch (error) {\n        dispatch(HideLoading());\n        message.error(error.message);\n        navigate('/user/quiz');\n      }\n    };\n    if (id) {\n      fetchExamData();\n    }\n  }, [id, dispatch, navigate]);\n  useEffect(() => {\n    if (result) {\n      // Play sound based on result\n      new Audio(result.verdict === \"Pass\" ? PassSound : FailSound).play();\n    }\n  }, [result]);\n\n  // Add quiz-fullscreen class for fullscreen experience\n  useEffect(() => {\n    document.body.classList.add('quiz-fullscreen');\n    return () => {\n      document.body.classList.remove('quiz-fullscreen');\n    };\n  }, []);\n  const fetchExplanation = async (question, expectedAnswer, userAnswer, imageUrl) => {\n    try {\n      dispatch(ShowLoading());\n      const response = await chatWithChatGPTToExplainAns({\n        question,\n        expectedAnswer,\n        userAnswer,\n        imageUrl\n      });\n      dispatch(HideLoading());\n      if (response.success) {\n        setExplanations(prev => ({\n          ...prev,\n          [question]: response.explanation\n        }));\n      } else {\n        message.error(response.error || \"Failed to fetch explanation.\");\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n  if (!result || !examData) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-4 text-gray-600\",\n          children: \"Loading results...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this);\n  }\n  if (showReview) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 py-4 sm:py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-6 sm:mb-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg p-4 sm:p-6 shadow-sm border border-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl sm:text-2xl font-bold text-gray-900 mb-2\",\n              children: \"Review Your Answers\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-sm sm:text-base\",\n              children: \"Detailed breakdown of your quiz performance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4 sm:space-y-6 mb-6 sm:mb-8\",\n          children: questions.map((question, index) => {\n            var _result$correctAnswer, _result$wrongAnswers$, _question$options, _question$options2;\n            const userAnswer = ((_result$correctAnswer = result.correctAnswers.find(q => q._id === question._id)) === null || _result$correctAnswer === void 0 ? void 0 : _result$correctAnswer.userAnswer) || ((_result$wrongAnswers$ = result.wrongAnswers.find(q => q._id === question._id)) === null || _result$wrongAnswers$ === void 0 ? void 0 : _result$wrongAnswers$.userAnswer) || \"\";\n            const isCorrect = result.correctAnswers.some(q => q._id === question._id);\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `px-4 sm:px-6 py-3 sm:py-4 border-b ${isCorrect ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-base sm:text-lg font-semibold text-gray-900\",\n                    children: [\"Question \", index + 1]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 141,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium ${isCorrect ? 'bg-green-100 text-green-700 border border-green-200' : 'bg-red-100 text-red-700 border border-red-200'}`,\n                    children: isCorrect ? '✓ Correct' : '✗ Incorrect'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 144,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 140,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-4 sm:p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-base sm:text-lg font-medium text-gray-900 mb-3 sm:mb-4\",\n                  children: question.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 21\n                }, this), (question.image || question.imageUrl) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-3 sm:mb-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: question.image || question.imageUrl,\n                    alt: \"Question\",\n                    className: \"max-w-full h-auto rounded-lg border border-gray-200 mx-auto\",\n                    style: {\n                      maxHeight: '300px'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 160,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4 mb-3 sm:mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `p-3 sm:p-4 rounded-lg border-2 ${isCorrect ? 'border-green-300 bg-green-50' : 'border-red-300 bg-red-50'}`,\n                    children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                      className: \"font-semibold text-gray-700 mb-2 text-sm sm:text-base\",\n                      children: \"Your Answer\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 176,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: `text-sm sm:text-base font-medium ${isCorrect ? 'text-green-800' : 'text-red-800'}`,\n                      children: question.type === \"mcq\" || question.answerType === \"Options\" ? ((_question$options = question.options) === null || _question$options === void 0 ? void 0 : _question$options[userAnswer]) || \"Not answered\" : userAnswer || \"Not answered\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 177,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 171,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 sm:p-4 rounded-lg border-2 border-green-300 bg-green-50\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                      className: \"font-semibold text-gray-700 mb-2 text-sm sm:text-base\",\n                      children: \"Correct Answer\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 187,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-green-800 text-sm sm:text-base font-medium\",\n                      children: question.type === \"mcq\" || question.answerType === \"Options\" ? (_question$options2 = question.options) === null || _question$options2 === void 0 ? void 0 : _question$options2[question.correctOption || question.correctAnswer] : question.correctAnswer || question.correctOption\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 188,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 186,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 170,\n                  columnNumber: 21\n                }, this), !isCorrect && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-3 sm:mt-4 p-3 sm:p-4 bg-blue-50 rounded-lg border border-blue-200\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"px-3 sm:px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors duration-200 text-sm sm:text-base\",\n                    onClick: () => {\n                      var _question$options3, _question$options4;\n                      return fetchExplanation(question.name, question.type === \"mcq\" || question.answerType === \"Options\" ? (_question$options3 = question.options) === null || _question$options3 === void 0 ? void 0 : _question$options3[question.correctOption || question.correctAnswer] : question.correctAnswer || question.correctOption, question.type === \"mcq\" || question.answerType === \"Options\" ? ((_question$options4 = question.options) === null || _question$options4 === void 0 ? void 0 : _question$options4[userAnswer]) || \"Not answered\" : userAnswer || \"Not answered\", question.image || question.imageUrl);\n                    },\n                    children: \"Get AI Explanation\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 199,\n                    columnNumber: 25\n                  }, this), explanations[question.name] && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-3 sm:mt-4 p-3 sm:p-4 bg-white rounded-lg border border-blue-200\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                      className: \"font-semibold text-blue-800 mb-2 text-sm sm:text-base\",\n                      children: \"AI Explanation\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 217,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm sm:text-base\",\n                      children: /*#__PURE__*/_jsxDEV(ContentRenderer, {\n                        text: explanations[question.name]\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 219,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 218,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 216,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center space-y-3 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"w-full sm:w-auto px-4 sm:px-6 py-3 bg-gray-600 text-white rounded-lg font-medium hover:bg-gray-700 transition-colors duration-200 text-sm sm:text-base\",\n            onClick: () => setShowReview(false),\n            children: \"Back to Results\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"w-full sm:w-auto px-4 sm:px-6 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors duration-200 text-sm sm:text-base\",\n            onClick: () => navigate('/user/hub'),\n            children: \"Hub\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-4 sm:py-8\",\n    children: [result.verdict === \"Pass\" && /*#__PURE__*/_jsxDEV(Confetti, {\n      width: width,\n      height: height\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 37\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute top-4 left-4 z-10\",\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => navigate(\"/user/hub\"),\n        className: \"group flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-xl font-bold transition-all duration-300 transform hover:scale-105 hover:-translate-y-0.5 shadow-lg hover:shadow-2xl hover:shadow-blue-500/25 border border-blue-400/20 relative overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"ri-apps-2-line text-sm group-hover:rotate-12 transition-transform duration-300\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm tracking-wide\",\n          children: \"Hub\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 256,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto px-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-xl overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `px-4 sm:px-6 py-8 sm:py-12 text-center ${result.verdict === \"Pass\" ? \"bg-green-50\" : \"bg-red-50\"}`,\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: result.verdict === \"Pass\" ? Pass : Fail,\n            alt: result.verdict,\n            className: \"mx-auto mb-4 sm:mb-6 w-20 h-20 sm:w-28 sm:h-28 md:w-32 md:h-32\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: `text-2xl sm:text-3xl md:text-4xl font-bold mb-3 sm:mb-4 ${result.verdict === \"Pass\" ? \"text-green-700\" : \"text-red-700\"}`,\n            children: result.verdict === \"Pass\" ? \"Congratulations!\" : \"Better Luck Next Time!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg sm:text-xl text-gray-600 px-2\",\n            children: result.verdict === \"Pass\" ? \"You've successfully passed the quiz!\" : \"Keep practicing and try again!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4 sm:p-6 md:p-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 md:gap-6 mb-6 sm:mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center p-4 sm:p-6 bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl border-2 border-green-200 shadow-lg hover:shadow-xl transition-all duration-300\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"inline-flex items-center justify-center w-12 h-12 bg-green-100 rounded-full mb-3\",\n                children: /*#__PURE__*/_jsxDEV(TbCheck, {\n                  className: \"w-6 h-6 text-green-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl sm:text-4xl font-black text-green-700 mb-1\",\n                children: ((_result$correctAnswer2 = result.correctAnswers) === null || _result$correctAnswer2 === void 0 ? void 0 : _result$correctAnswer2.length) || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm font-bold text-green-600\",\n                children: \"Correct Answers\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center p-4 sm:p-6 bg-gradient-to-br from-red-50 to-pink-50 rounded-2xl border-2 border-red-200 shadow-lg hover:shadow-xl transition-all duration-300\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"inline-flex items-center justify-center w-12 h-12 bg-red-100 rounded-full mb-3\",\n                children: /*#__PURE__*/_jsxDEV(TbX, {\n                  className: \"w-6 h-6 text-red-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl sm:text-4xl font-black text-red-700 mb-1\",\n                children: ((_result$wrongAnswers = result.wrongAnswers) === null || _result$wrongAnswers === void 0 ? void 0 : _result$wrongAnswers.length) || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm font-bold text-red-600\",\n                children: \"Wrong Answers\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center p-4 sm:p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl border-2 border-blue-200 shadow-lg hover:shadow-xl transition-all duration-300\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-full mb-3\",\n                children: /*#__PURE__*/_jsxDEV(TbBrain, {\n                  className: \"w-6 h-6 text-blue-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl sm:text-4xl font-black text-blue-700 mb-1\",\n                children: questions.length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm font-bold text-blue-600\",\n                children: \"Total Questions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center p-4 sm:p-6 bg-gradient-to-br from-purple-50 to-violet-50 rounded-2xl border-2 border-purple-200 shadow-lg hover:shadow-xl transition-all duration-300\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"inline-flex items-center justify-center w-12 h-12 bg-purple-100 rounded-full mb-3\",\n                children: /*#__PURE__*/_jsxDEV(TbTarget, {\n                  className: \"w-6 h-6 text-purple-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 325,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl sm:text-4xl font-black text-purple-700 mb-1\",\n                children: [examData.passingMarks, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm font-bold text-purple-600\",\n                children: \"Pass Mark\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6 sm:mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center mb-3 sm:mb-4\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xl sm:text-2xl font-bold text-gray-700\",\n                children: \"Your Score\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full bg-gray-200 rounded-full h-4 sm:h-6 overflow-hidden\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `h-full rounded-full transition-all duration-1000 ${result.verdict === \"Pass\" ? \"bg-green-600\" : \"bg-red-600\"}`,\n                style: {\n                  width: `${(((_result$correctAnswer3 = result.correctAnswers) === null || _result$correctAnswer3 === void 0 ? void 0 : _result$correctAnswer3.length) || 0) / questions.length * 100}%`\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center mt-2 sm:mt-3\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-2xl sm:text-3xl font-bold text-gray-700\",\n                children: [Math.round((((_result$correctAnswer4 = result.correctAnswers) === null || _result$correctAnswer4 === void 0 ? void 0 : _result$correctAnswer4.length) || 0) / questions.length * 100), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"px-6 sm:px-8 py-3 sm:py-4 bg-blue-600 text-white rounded-xl font-semibold hover:bg-blue-700 transition-colors duration-200 text-sm sm:text-base\",\n              onClick: () => setShowReview(true),\n              children: \"Review Answers\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"px-6 sm:px-8 py-3 sm:py-4 bg-green-600 text-white rounded-xl font-semibold hover:bg-green-700 transition-colors duration-200 text-sm sm:text-base\",\n              onClick: () => navigate(`/quiz/${id}/start`),\n              children: \"Retake Quiz\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"px-6 sm:px-8 py-3 sm:py-4 bg-gray-600 text-white rounded-xl font-semibold hover:bg-gray-700 transition-colors duration-200 text-sm sm:text-base\",\n              onClick: () => navigate('/user/hub'),\n              children: \"Hub\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed bottom-4 left-4 z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => navigate('/user/hub'),\n        className: \"flex items-center justify-center w-12 h-12 bg-gray-600 hover:bg-gray-700 text-white rounded-full shadow-lg transition-all duration-200 hover:scale-105\",\n        title: \"Back to Hub\",\n        children: /*#__PURE__*/_jsxDEV(TbArrowLeft, {\n          className: \"w-6 h-6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 383,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 382,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 252,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizResult, \"D4V/cGZGP63efKMIrBtSCOFOvoE=\", false, function () {\n  return [useParams, useNavigate, useLocation, useDispatch, useWindowSize];\n});\n_c = QuizResult;\nexport default QuizResult;\nvar _c;\n$RefreshReg$(_c, \"QuizResult\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "useLocation", "useDispatch", "message", "Confetti", "useWindowSize", "TbArrowLeft", "TbCheck", "TbX", "TbTrophy", "TbBrain", "TbTarget", "TbClock", "TbRefresh", "TbEye", "TbBulb", "getExamById", "chatWithChatGPTToExplainAns", "HideLoading", "ShowLoading", "Content<PERSON><PERSON><PERSON>", "Pass", "Fail", "PassSound", "FailSound", "jsxDEV", "_jsxDEV", "QuizResult", "_s", "_location$state", "_result$correctAnswer2", "_result$wrongAnswers", "_result$correctAnswer3", "_result$correctAnswer4", "examData", "setExamData", "questions", "setQuestions", "explanations", "setExplanations", "showReview", "setShowReview", "id", "navigate", "location", "dispatch", "width", "height", "result", "state", "fetchExamData", "response", "examId", "success", "_response$data", "data", "error", "Audio", "verdict", "play", "document", "body", "classList", "add", "remove", "fetchExplanation", "question", "expectedAnswer", "userAnswer", "imageUrl", "prev", "explanation", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "index", "_result$correctAnswer", "_result$wrongAnswers$", "_question$options", "_question$options2", "correctAnswers", "find", "q", "_id", "wrongAnswers", "isCorrect", "some", "name", "image", "src", "alt", "style", "maxHeight", "type", "answerType", "options", "correctOption", "<PERSON><PERSON><PERSON><PERSON>", "onClick", "_question$options3", "_question$options4", "text", "length", "passingMarks", "Math", "round", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/QuizResult.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate, useLocation } from 'react-router-dom';\nimport { useDispatch } from 'react-redux';\nimport { message } from 'antd';\nimport Confetti from 'react-confetti';\nimport useWindowSize from 'react-use/lib/useWindowSize';\nimport {\n  TbArrowLeft,\n  TbCheck,\n  TbX,\n  TbTrophy,\n  TbBrain,\n  TbTarget,\n  TbClock,\n  TbRefresh,\n  TbEye,\n  TbBulb\n} from 'react-icons/tb';\nimport { getExamById } from '../../../apicalls/exams';\nimport { chatWithChatGPTToExplainAns } from '../../../apicalls/chat';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport ContentRenderer from '../../../components/ContentRenderer';\nimport Pass from '../../../assets/pass.gif';\nimport Fail from '../../../assets/fail.gif';\nimport PassSound from '../../../assets/pass.mp3';\nimport FailSound from '../../../assets/fail.mp3';\nimport './responsive.css';\n\nconst QuizResult = () => {\n  const [examData, setExamData] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [explanations, setExplanations] = useState({});\n  const [showReview, setShowReview] = useState(false);\n  \n  const { id } = useParams();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  const { width, height } = useWindowSize();\n  \n  const result = location.state?.result;\n\n  useEffect(() => {\n    const fetchExamData = async () => {\n      try {\n        dispatch(ShowLoading());\n        const response = await getExamById({ examId: id });\n        dispatch(HideLoading());\n        \n        if (response.success) {\n          setExamData(response.data);\n          setQuestions(response.data?.questions || []);\n        } else {\n          message.error(response.message);\n          navigate('/user/quiz');\n        }\n      } catch (error) {\n        dispatch(HideLoading());\n        message.error(error.message);\n        navigate('/user/quiz');\n      }\n    };\n\n    if (id) {\n      fetchExamData();\n    }\n  }, [id, dispatch, navigate]);\n\n  useEffect(() => {\n    if (result) {\n      // Play sound based on result\n      new Audio(result.verdict === \"Pass\" ? PassSound : FailSound).play();\n    }\n  }, [result]);\n\n  // Add quiz-fullscreen class for fullscreen experience\n  useEffect(() => {\n    document.body.classList.add('quiz-fullscreen');\n\n    return () => {\n      document.body.classList.remove('quiz-fullscreen');\n    };\n  }, []);\n\n  const fetchExplanation = async (question, expectedAnswer, userAnswer, imageUrl) => {\n    try {\n      dispatch(ShowLoading());\n      const response = await chatWithChatGPTToExplainAns({ question, expectedAnswer, userAnswer, imageUrl });\n      dispatch(HideLoading());\n\n      if (response.success) {\n        setExplanations((prev) => ({ ...prev, [question]: response.explanation }));\n      } else {\n        message.error(response.error || \"Failed to fetch explanation.\");\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n\n  if (!result || !examData) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">Loading results...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (showReview) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 py-4 sm:py-8\">\n        <div className=\"max-w-4xl mx-auto px-4\">\n          {/* Header */}\n          <div className=\"text-center mb-6 sm:mb-8\">\n            <div className=\"bg-white rounded-lg p-4 sm:p-6 shadow-sm border border-gray-200\">\n              <h2 className=\"text-xl sm:text-2xl font-bold text-gray-900 mb-2\">Review Your Answers</h2>\n              <p className=\"text-gray-600 text-sm sm:text-base\">Detailed breakdown of your quiz performance</p>\n            </div>\n          </div>\n\n          {/* Questions Review */}\n          <div className=\"space-y-4 sm:space-y-6 mb-6 sm:mb-8\">\n            {questions.map((question, index) => {\n              const userAnswer = result.correctAnswers.find(q => q._id === question._id)?.userAnswer ||\n                                result.wrongAnswers.find(q => q._id === question._id)?.userAnswer || \"\";\n              const isCorrect = result.correctAnswers.some(q => q._id === question._id);\n\n              return (\n                <div key={index} className=\"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\">\n                  {/* Question Header */}\n                  <div className={`px-4 sm:px-6 py-3 sm:py-4 border-b ${\n                    isCorrect\n                      ? 'bg-green-50 border-green-200'\n                      : 'bg-red-50 border-red-200'\n                  }`}>\n                    <div className=\"flex items-center justify-between\">\n                      <h3 className=\"text-base sm:text-lg font-semibold text-gray-900\">\n                        Question {index + 1}\n                      </h3>\n                      <span className={`px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium ${\n                        isCorrect\n                          ? 'bg-green-100 text-green-700 border border-green-200'\n                          : 'bg-red-100 text-red-700 border border-red-200'\n                      }`}>\n                        {isCorrect ? '✓ Correct' : '✗ Incorrect'}\n                      </span>\n                    </div>\n                  </div>\n\n                  {/* Question Content */}\n                  <div className=\"p-4 sm:p-6\">\n                    <h4 className=\"text-base sm:text-lg font-medium text-gray-900 mb-3 sm:mb-4\">{question.name}</h4>\n\n                    {(question.image || question.imageUrl) && (\n                      <div className=\"mb-3 sm:mb-4\">\n                        <img\n                          src={question.image || question.imageUrl}\n                          alt=\"Question\"\n                          className=\"max-w-full h-auto rounded-lg border border-gray-200 mx-auto\"\n                          style={{ maxHeight: '300px' }}\n                        />\n                      </div>\n                    )}\n\n                    {/* Answer Comparison */}\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4 mb-3 sm:mb-4\">\n                      <div className={`p-3 sm:p-4 rounded-lg border-2 ${\n                        isCorrect\n                          ? 'border-green-300 bg-green-50'\n                          : 'border-red-300 bg-red-50'\n                      }`}>\n                        <h5 className=\"font-semibold text-gray-700 mb-2 text-sm sm:text-base\">Your Answer</h5>\n                        <p className={`text-sm sm:text-base font-medium ${\n                          isCorrect ? 'text-green-800' : 'text-red-800'\n                        }`}>\n                          {question.type === \"mcq\" || question.answerType === \"Options\"\n                            ? question.options?.[userAnswer] || \"Not answered\"\n                            : userAnswer || \"Not answered\"}\n                        </p>\n                      </div>\n\n                      <div className=\"p-3 sm:p-4 rounded-lg border-2 border-green-300 bg-green-50\">\n                        <h5 className=\"font-semibold text-gray-700 mb-2 text-sm sm:text-base\">Correct Answer</h5>\n                        <p className=\"text-green-800 text-sm sm:text-base font-medium\">\n                          {question.type === \"mcq\" || question.answerType === \"Options\"\n                            ? question.options?.[question.correctOption || question.correctAnswer]\n                            : (question.correctAnswer || question.correctOption)}\n                        </p>\n                      </div>\n                    </div>\n\n                    {/* Explanation Section */}\n                    {!isCorrect && (\n                      <div className=\"mt-3 sm:mt-4 p-3 sm:p-4 bg-blue-50 rounded-lg border border-blue-200\">\n                        <button\n                          className=\"px-3 sm:px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors duration-200 text-sm sm:text-base\"\n                          onClick={() => fetchExplanation(\n                            question.name,\n                            question.type === \"mcq\" || question.answerType === \"Options\"\n                              ? question.options?.[question.correctOption || question.correctAnswer]\n                              : (question.correctAnswer || question.correctOption),\n                            question.type === \"mcq\" || question.answerType === \"Options\"\n                              ? question.options?.[userAnswer] || \"Not answered\"\n                              : userAnswer || \"Not answered\",\n                            question.image || question.imageUrl\n                          )}\n                        >\n                          Get AI Explanation\n                        </button>\n\n                        {explanations[question.name] && (\n                          <div className=\"mt-3 sm:mt-4 p-3 sm:p-4 bg-white rounded-lg border border-blue-200\">\n                            <h6 className=\"font-semibold text-blue-800 mb-2 text-sm sm:text-base\">AI Explanation</h6>\n                            <div className=\"text-sm sm:text-base\">\n                              <ContentRenderer text={explanations[question.name]} />\n                            </div>\n                          </div>\n                        )}\n                      </div>\n                    )}\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n\n          {/* Back Buttons */}\n          <div className=\"text-center space-y-3 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center\">\n            <button\n              className=\"w-full sm:w-auto px-4 sm:px-6 py-3 bg-gray-600 text-white rounded-lg font-medium hover:bg-gray-700 transition-colors duration-200 text-sm sm:text-base\"\n              onClick={() => setShowReview(false)}\n            >\n              Back to Results\n            </button>\n            <button\n              className=\"w-full sm:w-auto px-4 sm:px-6 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors duration-200 text-sm sm:text-base\"\n              onClick={() => navigate('/user/hub')}\n            >\n              Hub\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-4 sm:py-8\">\n      {result.verdict === \"Pass\" && <Confetti width={width} height={height} />}\n\n      {/* Hub Tab - Top Left */}\n      <div className=\"absolute top-4 left-4 z-10\">\n        <button\n          onClick={() => navigate(\"/user/hub\")}\n          className=\"group flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-xl font-bold transition-all duration-300 transform hover:scale-105 hover:-translate-y-0.5 shadow-lg hover:shadow-2xl hover:shadow-blue-500/25 border border-blue-400/20 relative overflow-hidden\"\n        >\n          <i className=\"ri-apps-2-line text-sm group-hover:rotate-12 transition-transform duration-300\"></i>\n          <span className=\"text-sm tracking-wide\">Hub</span>\n          <div className=\"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl\"></div>\n        </button>\n      </div>\n\n      <div className=\"max-w-4xl mx-auto px-4\">\n        <div className=\"bg-white rounded-2xl shadow-xl overflow-hidden\">\n          {/* Header */}\n          <div className={`px-4 sm:px-6 py-8 sm:py-12 text-center ${\n            result.verdict === \"Pass\" ? \"bg-green-50\" : \"bg-red-50\"\n          }`}>\n            <img\n              src={result.verdict === \"Pass\" ? Pass : Fail}\n              alt={result.verdict}\n              className=\"mx-auto mb-4 sm:mb-6 w-20 h-20 sm:w-28 sm:h-28 md:w-32 md:h-32\"\n            />\n            <h1 className={`text-2xl sm:text-3xl md:text-4xl font-bold mb-3 sm:mb-4 ${\n              result.verdict === \"Pass\" ? \"text-green-700\" : \"text-red-700\"\n            }`}>\n              {result.verdict === \"Pass\" ? \"Congratulations!\" : \"Better Luck Next Time!\"}\n            </h1>\n            <p className=\"text-lg sm:text-xl text-gray-600 px-2\">\n              {result.verdict === \"Pass\"\n                ? \"You've successfully passed the quiz!\"\n                : \"Keep practicing and try again!\"}\n            </p>\n          </div>\n\n          {/* Statistics */}\n          <div className=\"p-4 sm:p-6 md:p-8\">\n            <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 md:gap-6 mb-6 sm:mb-8\">\n              <div className=\"text-center p-4 sm:p-6 bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl border-2 border-green-200 shadow-lg hover:shadow-xl transition-all duration-300\">\n                <div className=\"inline-flex items-center justify-center w-12 h-12 bg-green-100 rounded-full mb-3\">\n                  <TbCheck className=\"w-6 h-6 text-green-600\" />\n                </div>\n                <div className=\"text-3xl sm:text-4xl font-black text-green-700 mb-1\">\n                  {result.correctAnswers?.length || 0}\n                </div>\n                <div className=\"text-sm font-bold text-green-600\">Correct Answers</div>\n              </div>\n\n              <div className=\"text-center p-4 sm:p-6 bg-gradient-to-br from-red-50 to-pink-50 rounded-2xl border-2 border-red-200 shadow-lg hover:shadow-xl transition-all duration-300\">\n                <div className=\"inline-flex items-center justify-center w-12 h-12 bg-red-100 rounded-full mb-3\">\n                  <TbX className=\"w-6 h-6 text-red-600\" />\n                </div>\n                <div className=\"text-3xl sm:text-4xl font-black text-red-700 mb-1\">\n                  {result.wrongAnswers?.length || 0}\n                </div>\n                <div className=\"text-sm font-bold text-red-600\">Wrong Answers</div>\n              </div>\n\n              <div className=\"text-center p-4 sm:p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl border-2 border-blue-200 shadow-lg hover:shadow-xl transition-all duration-300\">\n                <div className=\"inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-full mb-3\">\n                  <TbBrain className=\"w-6 h-6 text-blue-600\" />\n                </div>\n                <div className=\"text-3xl sm:text-4xl font-black text-blue-700 mb-1\">\n                  {questions.length}\n                </div>\n                <div className=\"text-sm font-bold text-blue-600\">Total Questions</div>\n              </div>\n\n              <div className=\"text-center p-4 sm:p-6 bg-gradient-to-br from-purple-50 to-violet-50 rounded-2xl border-2 border-purple-200 shadow-lg hover:shadow-xl transition-all duration-300\">\n                <div className=\"inline-flex items-center justify-center w-12 h-12 bg-purple-100 rounded-full mb-3\">\n                  <TbTarget className=\"w-6 h-6 text-purple-600\" />\n                </div>\n                <div className=\"text-3xl sm:text-4xl font-black text-purple-700 mb-1\">\n                  {examData.passingMarks}%\n                </div>\n                <div className=\"text-sm font-bold text-purple-600\">Pass Mark</div>\n              </div>\n            </div>\n\n            {/* Score Percentage */}\n            <div className=\"mb-6 sm:mb-8\">\n              <div className=\"text-center mb-3 sm:mb-4\">\n                <span className=\"text-xl sm:text-2xl font-bold text-gray-700\">Your Score</span>\n              </div>\n              <div className=\"w-full bg-gray-200 rounded-full h-4 sm:h-6 overflow-hidden\">\n                <div\n                  className={`h-full rounded-full transition-all duration-1000 ${\n                    result.verdict === \"Pass\" ? \"bg-green-600\" : \"bg-red-600\"\n                  }`}\n                  style={{ width: `${((result.correctAnswers?.length || 0) / questions.length) * 100}%` }}\n                ></div>\n              </div>\n              <div className=\"text-center mt-2 sm:mt-3\">\n                <span className=\"text-2xl sm:text-3xl font-bold text-gray-700\">\n                  {Math.round(((result.correctAnswers?.length || 0) / questions.length) * 100)}%\n                </span>\n              </div>\n            </div>\n\n            {/* Action Buttons */}\n            <div className=\"flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center\">\n              <button\n                className=\"px-6 sm:px-8 py-3 sm:py-4 bg-blue-600 text-white rounded-xl font-semibold hover:bg-blue-700 transition-colors duration-200 text-sm sm:text-base\"\n                onClick={() => setShowReview(true)}\n              >\n                Review Answers\n              </button>\n\n              <button\n                className=\"px-6 sm:px-8 py-3 sm:py-4 bg-green-600 text-white rounded-xl font-semibold hover:bg-green-700 transition-colors duration-200 text-sm sm:text-base\"\n                onClick={() => navigate(`/quiz/${id}/start`)}\n              >\n                Retake Quiz\n              </button>\n\n              <button\n                className=\"px-6 sm:px-8 py-3 sm:py-4 bg-gray-600 text-white rounded-xl font-semibold hover:bg-gray-700 transition-colors duration-200 text-sm sm:text-base\"\n                onClick={() => navigate('/user/hub')}\n              >\n                Hub\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Back Navigation Arrow */}\n      <div className=\"fixed bottom-4 left-4 z-50\">\n        <button\n          onClick={() => navigate('/user/hub')}\n          className=\"flex items-center justify-center w-12 h-12 bg-gray-600 hover:bg-gray-700 text-white rounded-full shadow-lg transition-all duration-200 hover:scale-105\"\n          title=\"Back to Hub\"\n        >\n          <TbArrowLeft className=\"w-6 h-6\" />\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizResult;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACtE,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,OAAO,QAAQ,MAAM;AAC9B,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,aAAa,MAAM,6BAA6B;AACvD,SACEC,WAAW,EACXC,OAAO,EACPC,GAAG,EACHC,QAAQ,EACRC,OAAO,EACPC,QAAQ,EACRC,OAAO,EACPC,SAAS,EACTC,KAAK,EACLC,MAAM,QACD,gBAAgB;AACvB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,2BAA2B,QAAQ,wBAAwB;AACpE,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,OAAOC,eAAe,MAAM,qCAAqC;AACjE,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,OAAOC,SAAS,MAAM,0BAA0B;AAChD,OAAOC,SAAS,MAAM,0BAA0B;AAChD,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,sBAAA,EAAAC,oBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACvB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACuC,SAAS,EAAEC,YAAY,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACyC,YAAY,EAAEC,eAAe,CAAC,GAAG1C,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAAC2C,UAAU,EAAEC,aAAa,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EAEnD,MAAM;IAAE6C;EAAG,CAAC,GAAG3C,SAAS,CAAC,CAAC;EAC1B,MAAM4C,QAAQ,GAAG3C,WAAW,CAAC,CAAC;EAC9B,MAAM4C,QAAQ,GAAG3C,WAAW,CAAC,CAAC;EAC9B,MAAM4C,QAAQ,GAAG3C,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE4C,KAAK;IAAEC;EAAO,CAAC,GAAG1C,aAAa,CAAC,CAAC;EAEzC,MAAM2C,MAAM,IAAAnB,eAAA,GAAGe,QAAQ,CAACK,KAAK,cAAApB,eAAA,uBAAdA,eAAA,CAAgBmB,MAAM;EAErClD,SAAS,CAAC,MAAM;IACd,MAAMoD,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACFL,QAAQ,CAAC1B,WAAW,CAAC,CAAC,CAAC;QACvB,MAAMgC,QAAQ,GAAG,MAAMnC,WAAW,CAAC;UAAEoC,MAAM,EAAEV;QAAG,CAAC,CAAC;QAClDG,QAAQ,CAAC3B,WAAW,CAAC,CAAC,CAAC;QAEvB,IAAIiC,QAAQ,CAACE,OAAO,EAAE;UAAA,IAAAC,cAAA;UACpBnB,WAAW,CAACgB,QAAQ,CAACI,IAAI,CAAC;UAC1BlB,YAAY,CAAC,EAAAiB,cAAA,GAAAH,QAAQ,CAACI,IAAI,cAAAD,cAAA,uBAAbA,cAAA,CAAelB,SAAS,KAAI,EAAE,CAAC;QAC9C,CAAC,MAAM;UACLjC,OAAO,CAACqD,KAAK,CAACL,QAAQ,CAAChD,OAAO,CAAC;UAC/BwC,QAAQ,CAAC,YAAY,CAAC;QACxB;MACF,CAAC,CAAC,OAAOa,KAAK,EAAE;QACdX,QAAQ,CAAC3B,WAAW,CAAC,CAAC,CAAC;QACvBf,OAAO,CAACqD,KAAK,CAACA,KAAK,CAACrD,OAAO,CAAC;QAC5BwC,QAAQ,CAAC,YAAY,CAAC;MACxB;IACF,CAAC;IAED,IAAID,EAAE,EAAE;MACNQ,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAACR,EAAE,EAAEG,QAAQ,EAAEF,QAAQ,CAAC,CAAC;EAE5B7C,SAAS,CAAC,MAAM;IACd,IAAIkD,MAAM,EAAE;MACV;MACA,IAAIS,KAAK,CAACT,MAAM,CAACU,OAAO,KAAK,MAAM,GAAGnC,SAAS,GAAGC,SAAS,CAAC,CAACmC,IAAI,CAAC,CAAC;IACrE;EACF,CAAC,EAAE,CAACX,MAAM,CAAC,CAAC;;EAEZ;EACAlD,SAAS,CAAC,MAAM;IACd8D,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAE9C,OAAO,MAAM;MACXH,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,iBAAiB,CAAC;IACnD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,gBAAgB,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,cAAc,EAAEC,UAAU,EAAEC,QAAQ,KAAK;IACjF,IAAI;MACFxB,QAAQ,CAAC1B,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMgC,QAAQ,GAAG,MAAMlC,2BAA2B,CAAC;QAAEiD,QAAQ;QAAEC,cAAc;QAAEC,UAAU;QAAEC;MAAS,CAAC,CAAC;MACtGxB,QAAQ,CAAC3B,WAAW,CAAC,CAAC,CAAC;MAEvB,IAAIiC,QAAQ,CAACE,OAAO,EAAE;QACpBd,eAAe,CAAE+B,IAAI,KAAM;UAAE,GAAGA,IAAI;UAAE,CAACJ,QAAQ,GAAGf,QAAQ,CAACoB;QAAY,CAAC,CAAC,CAAC;MAC5E,CAAC,MAAM;QACLpE,OAAO,CAACqD,KAAK,CAACL,QAAQ,CAACK,KAAK,IAAI,8BAA8B,CAAC;MACjE;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdX,QAAQ,CAAC3B,WAAW,CAAC,CAAC,CAAC;MACvBf,OAAO,CAACqD,KAAK,CAACA,KAAK,CAACrD,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,IAAI,CAAC6C,MAAM,IAAI,CAACd,QAAQ,EAAE;IACxB,oBACER,OAAA;MAAK8C,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzG/C,OAAA;QAAK8C,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B/C,OAAA;UAAK8C,SAAS,EAAC;QAAwE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC9FnD,OAAA;UAAG8C,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIrC,UAAU,EAAE;IACd,oBACEd,OAAA;MAAK8C,SAAS,EAAC,sCAAsC;MAAAC,QAAA,eACnD/C,OAAA;QAAK8C,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBAErC/C,OAAA;UAAK8C,SAAS,EAAC,0BAA0B;UAAAC,QAAA,eACvC/C,OAAA;YAAK8C,SAAS,EAAC,iEAAiE;YAAAC,QAAA,gBAC9E/C,OAAA;cAAI8C,SAAS,EAAC,kDAAkD;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzFnD,OAAA;cAAG8C,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAAC;YAA2C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnD,OAAA;UAAK8C,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EACjDrC,SAAS,CAAC0C,GAAG,CAAC,CAACZ,QAAQ,EAAEa,KAAK,KAAK;YAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,kBAAA;YAClC,MAAMf,UAAU,GAAG,EAAAY,qBAAA,GAAAhC,MAAM,CAACoC,cAAc,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKrB,QAAQ,CAACqB,GAAG,CAAC,cAAAP,qBAAA,uBAAvDA,qBAAA,CAAyDZ,UAAU,OAAAa,qBAAA,GACpEjC,MAAM,CAACwC,YAAY,CAACH,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKrB,QAAQ,CAACqB,GAAG,CAAC,cAAAN,qBAAA,uBAArDA,qBAAA,CAAuDb,UAAU,KAAI,EAAE;YACzF,MAAMqB,SAAS,GAAGzC,MAAM,CAACoC,cAAc,CAACM,IAAI,CAACJ,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKrB,QAAQ,CAACqB,GAAG,CAAC;YAEzE,oBACE7D,OAAA;cAAiB8C,SAAS,EAAC,sEAAsE;cAAAC,QAAA,gBAE/F/C,OAAA;gBAAK8C,SAAS,EAAG,sCACfiB,SAAS,GACL,8BAA8B,GAC9B,0BACL,EAAE;gBAAAhB,QAAA,eACD/C,OAAA;kBAAK8C,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChD/C,OAAA;oBAAI8C,SAAS,EAAC,kDAAkD;oBAAAC,QAAA,GAAC,WACtD,EAACM,KAAK,GAAG,CAAC;kBAAA;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC,eACLnD,OAAA;oBAAM8C,SAAS,EAAG,iEAChBiB,SAAS,GACL,qDAAqD,GACrD,+CACL,EAAE;oBAAAhB,QAAA,EACAgB,SAAS,GAAG,WAAW,GAAG;kBAAa;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNnD,OAAA;gBAAK8C,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB/C,OAAA;kBAAI8C,SAAS,EAAC,6DAA6D;kBAAAC,QAAA,EAAEP,QAAQ,CAACyB;gBAAI;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,EAE/F,CAACX,QAAQ,CAAC0B,KAAK,IAAI1B,QAAQ,CAACG,QAAQ,kBACnC3C,OAAA;kBAAK8C,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAC3B/C,OAAA;oBACEmE,GAAG,EAAE3B,QAAQ,CAAC0B,KAAK,IAAI1B,QAAQ,CAACG,QAAS;oBACzCyB,GAAG,EAAC,UAAU;oBACdtB,SAAS,EAAC,6DAA6D;oBACvEuB,KAAK,EAAE;sBAAEC,SAAS,EAAE;oBAAQ;kBAAE;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACN,eAGDnD,OAAA;kBAAK8C,SAAS,EAAC,6DAA6D;kBAAAC,QAAA,gBAC1E/C,OAAA;oBAAK8C,SAAS,EAAG,kCACfiB,SAAS,GACL,8BAA8B,GAC9B,0BACL,EAAE;oBAAAhB,QAAA,gBACD/C,OAAA;sBAAI8C,SAAS,EAAC,uDAAuD;sBAAAC,QAAA,EAAC;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACtFnD,OAAA;sBAAG8C,SAAS,EAAG,oCACbiB,SAAS,GAAG,gBAAgB,GAAG,cAChC,EAAE;sBAAAhB,QAAA,EACAP,QAAQ,CAAC+B,IAAI,KAAK,KAAK,IAAI/B,QAAQ,CAACgC,UAAU,KAAK,SAAS,GACzD,EAAAhB,iBAAA,GAAAhB,QAAQ,CAACiC,OAAO,cAAAjB,iBAAA,uBAAhBA,iBAAA,CAAmBd,UAAU,CAAC,KAAI,cAAc,GAChDA,UAAU,IAAI;oBAAc;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eAENnD,OAAA;oBAAK8C,SAAS,EAAC,6DAA6D;oBAAAC,QAAA,gBAC1E/C,OAAA;sBAAI8C,SAAS,EAAC,uDAAuD;sBAAAC,QAAA,EAAC;oBAAc;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACzFnD,OAAA;sBAAG8C,SAAS,EAAC,iDAAiD;sBAAAC,QAAA,EAC3DP,QAAQ,CAAC+B,IAAI,KAAK,KAAK,IAAI/B,QAAQ,CAACgC,UAAU,KAAK,SAAS,IAAAf,kBAAA,GACzDjB,QAAQ,CAACiC,OAAO,cAAAhB,kBAAA,uBAAhBA,kBAAA,CAAmBjB,QAAQ,CAACkC,aAAa,IAAIlC,QAAQ,CAACmC,aAAa,CAAC,GACnEnC,QAAQ,CAACmC,aAAa,IAAInC,QAAQ,CAACkC;oBAAc;sBAAA1B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGL,CAACY,SAAS,iBACT/D,OAAA;kBAAK8C,SAAS,EAAC,sEAAsE;kBAAAC,QAAA,gBACnF/C,OAAA;oBACE8C,SAAS,EAAC,uIAAuI;oBACjJ8B,OAAO,EAAEA,CAAA;sBAAA,IAAAC,kBAAA,EAAAC,kBAAA;sBAAA,OAAMvC,gBAAgB,CAC7BC,QAAQ,CAACyB,IAAI,EACbzB,QAAQ,CAAC+B,IAAI,KAAK,KAAK,IAAI/B,QAAQ,CAACgC,UAAU,KAAK,SAAS,IAAAK,kBAAA,GACxDrC,QAAQ,CAACiC,OAAO,cAAAI,kBAAA,uBAAhBA,kBAAA,CAAmBrC,QAAQ,CAACkC,aAAa,IAAIlC,QAAQ,CAACmC,aAAa,CAAC,GACnEnC,QAAQ,CAACmC,aAAa,IAAInC,QAAQ,CAACkC,aAAc,EACtDlC,QAAQ,CAAC+B,IAAI,KAAK,KAAK,IAAI/B,QAAQ,CAACgC,UAAU,KAAK,SAAS,GACxD,EAAAM,kBAAA,GAAAtC,QAAQ,CAACiC,OAAO,cAAAK,kBAAA,uBAAhBA,kBAAA,CAAmBpC,UAAU,CAAC,KAAI,cAAc,GAChDA,UAAU,IAAI,cAAc,EAChCF,QAAQ,CAAC0B,KAAK,IAAI1B,QAAQ,CAACG,QAC7B,CAAC;oBAAA,CAAC;oBAAAI,QAAA,EACH;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAERvC,YAAY,CAAC4B,QAAQ,CAACyB,IAAI,CAAC,iBAC1BjE,OAAA;oBAAK8C,SAAS,EAAC,oEAAoE;oBAAAC,QAAA,gBACjF/C,OAAA;sBAAI8C,SAAS,EAAC,uDAAuD;sBAAAC,QAAA,EAAC;oBAAc;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACzFnD,OAAA;sBAAK8C,SAAS,EAAC,sBAAsB;sBAAAC,QAAA,eACnC/C,OAAA,CAACN,eAAe;wBAACqF,IAAI,EAAEnE,YAAY,CAAC4B,QAAQ,CAACyB,IAAI;sBAAE;wBAAAjB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA,GA5FEE,KAAK;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA6FV,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNnD,OAAA;UAAK8C,SAAS,EAAC,2EAA2E;UAAAC,QAAA,gBACxF/C,OAAA;YACE8C,SAAS,EAAC,wJAAwJ;YAClK8B,OAAO,EAAEA,CAAA,KAAM7D,aAAa,CAAC,KAAK,CAAE;YAAAgC,QAAA,EACrC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTnD,OAAA;YACE8C,SAAS,EAAC,wJAAwJ;YAClK8B,OAAO,EAAEA,CAAA,KAAM3D,QAAQ,CAAC,WAAW,CAAE;YAAA8B,QAAA,EACtC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEnD,OAAA;IAAK8C,SAAS,EAAC,wEAAwE;IAAAC,QAAA,GACpFzB,MAAM,CAACU,OAAO,KAAK,MAAM,iBAAIhC,OAAA,CAACtB,QAAQ;MAAC0C,KAAK,EAAEA,KAAM;MAACC,MAAM,EAAEA;IAAO;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGxEnD,OAAA;MAAK8C,SAAS,EAAC,4BAA4B;MAAAC,QAAA,eACzC/C,OAAA;QACE4E,OAAO,EAAEA,CAAA,KAAM3D,QAAQ,CAAC,WAAW,CAAE;QACrC6B,SAAS,EAAC,kVAAkV;QAAAC,QAAA,gBAE5V/C,OAAA;UAAG8C,SAAS,EAAC;QAAgF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClGnD,OAAA;UAAM8C,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClDnD,OAAA;UAAK8C,SAAS,EAAC;QAA6I;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7J;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENnD,OAAA;MAAK8C,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACrC/C,OAAA;QAAK8C,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAE7D/C,OAAA;UAAK8C,SAAS,EAAG,0CACfxB,MAAM,CAACU,OAAO,KAAK,MAAM,GAAG,aAAa,GAAG,WAC7C,EAAE;UAAAe,QAAA,gBACD/C,OAAA;YACEmE,GAAG,EAAE7C,MAAM,CAACU,OAAO,KAAK,MAAM,GAAGrC,IAAI,GAAGC,IAAK;YAC7CwE,GAAG,EAAE9C,MAAM,CAACU,OAAQ;YACpBc,SAAS,EAAC;UAAgE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC,eACFnD,OAAA;YAAI8C,SAAS,EAAG,2DACdxB,MAAM,CAACU,OAAO,KAAK,MAAM,GAAG,gBAAgB,GAAG,cAChD,EAAE;YAAAe,QAAA,EACAzB,MAAM,CAACU,OAAO,KAAK,MAAM,GAAG,kBAAkB,GAAG;UAAwB;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CAAC,eACLnD,OAAA;YAAG8C,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EACjDzB,MAAM,CAACU,OAAO,KAAK,MAAM,GACtB,sCAAsC,GACtC;UAAgC;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGNnD,OAAA;UAAK8C,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC/C,OAAA;YAAK8C,SAAS,EAAC,sEAAsE;YAAAC,QAAA,gBACnF/C,OAAA;cAAK8C,SAAS,EAAC,kKAAkK;cAAAC,QAAA,gBAC/K/C,OAAA;gBAAK8C,SAAS,EAAC,kFAAkF;gBAAAC,QAAA,eAC/F/C,OAAA,CAACnB,OAAO;kBAACiE,SAAS,EAAC;gBAAwB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eACNnD,OAAA;gBAAK8C,SAAS,EAAC,qDAAqD;gBAAAC,QAAA,EACjE,EAAA3C,sBAAA,GAAAkB,MAAM,CAACoC,cAAc,cAAAtD,sBAAA,uBAArBA,sBAAA,CAAuB4E,MAAM,KAAI;cAAC;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eACNnD,OAAA;gBAAK8C,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC,eAENnD,OAAA;cAAK8C,SAAS,EAAC,2JAA2J;cAAAC,QAAA,gBACxK/C,OAAA;gBAAK8C,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,eAC7F/C,OAAA,CAAClB,GAAG;kBAACgE,SAAS,EAAC;gBAAsB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC,eACNnD,OAAA;gBAAK8C,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAC/D,EAAA1C,oBAAA,GAAAiB,MAAM,CAACwC,YAAY,cAAAzD,oBAAA,uBAAnBA,oBAAA,CAAqB2E,MAAM,KAAI;cAAC;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eACNnD,OAAA;gBAAK8C,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC,eAENnD,OAAA;cAAK8C,SAAS,EAAC,+JAA+J;cAAAC,QAAA,gBAC5K/C,OAAA;gBAAK8C,SAAS,EAAC,iFAAiF;gBAAAC,QAAA,eAC9F/C,OAAA,CAAChB,OAAO;kBAAC8D,SAAS,EAAC;gBAAuB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eACNnD,OAAA;gBAAK8C,SAAS,EAAC,oDAAoD;gBAAAC,QAAA,EAChErC,SAAS,CAACsE;cAAM;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC,eACNnD,OAAA;gBAAK8C,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC,eAENnD,OAAA;cAAK8C,SAAS,EAAC,mKAAmK;cAAAC,QAAA,gBAChL/C,OAAA;gBAAK8C,SAAS,EAAC,mFAAmF;gBAAAC,QAAA,eAChG/C,OAAA,CAACf,QAAQ;kBAAC6D,SAAS,EAAC;gBAAyB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eACNnD,OAAA;gBAAK8C,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,GAClEvC,QAAQ,CAACyE,YAAY,EAAC,GACzB;cAAA;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNnD,OAAA;gBAAK8C,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNnD,OAAA;YAAK8C,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B/C,OAAA;cAAK8C,SAAS,EAAC,0BAA0B;cAAAC,QAAA,eACvC/C,OAAA;gBAAM8C,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5E,CAAC,eACNnD,OAAA;cAAK8C,SAAS,EAAC,4DAA4D;cAAAC,QAAA,eACzE/C,OAAA;gBACE8C,SAAS,EAAG,oDACVxB,MAAM,CAACU,OAAO,KAAK,MAAM,GAAG,cAAc,GAAG,YAC9C,EAAE;gBACHqC,KAAK,EAAE;kBAAEjD,KAAK,EAAG,GAAG,CAAC,EAAAd,sBAAA,GAAAgB,MAAM,CAACoC,cAAc,cAAApD,sBAAA,uBAArBA,sBAAA,CAAuB0E,MAAM,KAAI,CAAC,IAAItE,SAAS,CAACsE,MAAM,GAAI,GAAI;gBAAG;cAAE;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNnD,OAAA;cAAK8C,SAAS,EAAC,0BAA0B;cAAAC,QAAA,eACvC/C,OAAA;gBAAM8C,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAC3DmC,IAAI,CAACC,KAAK,CAAE,CAAC,EAAA5E,sBAAA,GAAAe,MAAM,CAACoC,cAAc,cAAAnD,sBAAA,uBAArBA,sBAAA,CAAuByE,MAAM,KAAI,CAAC,IAAItE,SAAS,CAACsE,MAAM,GAAI,GAAG,CAAC,EAAC,GAC/E;cAAA;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNnD,OAAA;YAAK8C,SAAS,EAAC,yDAAyD;YAAAC,QAAA,gBACtE/C,OAAA;cACE8C,SAAS,EAAC,iJAAiJ;cAC3J8B,OAAO,EAAEA,CAAA,KAAM7D,aAAa,CAAC,IAAI,CAAE;cAAAgC,QAAA,EACpC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAETnD,OAAA;cACE8C,SAAS,EAAC,mJAAmJ;cAC7J8B,OAAO,EAAEA,CAAA,KAAM3D,QAAQ,CAAE,SAAQD,EAAG,QAAO,CAAE;cAAA+B,QAAA,EAC9C;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAETnD,OAAA;cACE8C,SAAS,EAAC,iJAAiJ;cAC3J8B,OAAO,EAAEA,CAAA,KAAM3D,QAAQ,CAAC,WAAW,CAAE;cAAA8B,QAAA,EACtC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnD,OAAA;MAAK8C,SAAS,EAAC,4BAA4B;MAAAC,QAAA,eACzC/C,OAAA;QACE4E,OAAO,EAAEA,CAAA,KAAM3D,QAAQ,CAAC,WAAW,CAAE;QACrC6B,SAAS,EAAC,wJAAwJ;QAClKsC,KAAK,EAAC,aAAa;QAAArC,QAAA,eAEnB/C,OAAA,CAACpB,WAAW;UAACkE,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjD,EAAA,CA5WID,UAAU;EAAA,QAMC5B,SAAS,EACPC,WAAW,EACXC,WAAW,EACXC,WAAW,EACFG,aAAa;AAAA;AAAA0G,EAAA,GAVnCpF,UAAU;AA8WhB,eAAeA,UAAU;AAAC,IAAAoF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}