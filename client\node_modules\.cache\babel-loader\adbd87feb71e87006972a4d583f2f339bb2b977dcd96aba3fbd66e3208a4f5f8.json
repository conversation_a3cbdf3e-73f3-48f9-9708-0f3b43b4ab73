{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\QuizRenderer.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport '../pages/user/Quiz/responsive.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst QuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  onAnswerChange,\n  timeLeft,\n  onNext,\n  onPrevious,\n  examTitle = \"Quiz\",\n  isTimeWarning = false\n}) => {\n  _s();\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n  const handleAnswerSelect = answer => {\n    setCurrentAnswer(answer);\n    setIsAnswered(true);\n    onAnswerChange(answer);\n  };\n  const formatTime = seconds => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n  const progressPercentage = (questionIndex + 1) / totalQuestions * 100;\n\n  // Early return for invalid question\n  if (!question || !question.name) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"quiz-container quiz-responsive\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"quiz-content quiz-content-responsive\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"quiz-question-container\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center text-red-600 p-6 bg-red-50 rounded-lg border border-red-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"ri-error-warning-line text-3xl mb-3 block\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold mb-2\",\n              children: \"Question Not Available\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm\",\n              children: \"This question could not be loaded. Please try refreshing the page.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this);\n  }\n  const renderMCQ = () => {\n    if (!question || !question.options || Object.keys(question.options).length === 0) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"quiz-question-container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center text-red-600 p-4 bg-red-50 rounded-lg border border-red-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"ri-error-warning-line text-2xl mb-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"No options available for this question.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this);\n    }\n    const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"quiz-options space-y-4\",\n      children: Object.entries(question.options).map(([key, value], index) => {\n        const optionKey = String(key).trim();\n        const optionValue = String(value || '').trim();\n        const label = optionLabels[index] || optionKey;\n        const isSelected = currentAnswer === optionKey;\n\n        // Skip empty options\n        if (!optionValue) return null;\n        return /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handleAnswerSelect(optionKey),\n          className: `quiz-option w-full text-left transition-all duration-300 transform hover:scale-[1.02] ${isSelected ? 'selected bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg border-blue-500' : 'bg-white hover:bg-blue-50 border-gray-200 hover:border-blue-300 text-gray-800'} border-2 rounded-xl p-4 sm:p-5 group relative overflow-hidden`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `quiz-option-letter flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm transition-all duration-300 ${isSelected ? 'bg-white text-blue-600 shadow-md' : 'bg-gradient-to-br from-blue-100 to-indigo-100 text-blue-700 group-hover:from-blue-200 group-hover:to-indigo-200'}`,\n              children: label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `quiz-option-text flex-1 font-medium transition-all duration-300 ${isSelected ? 'text-white' : 'text-gray-800 group-hover:text-blue-800'}`,\n              children: optionValue\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 17\n            }, this), isSelected && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0 w-6 h-6 bg-white rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-4 h-4 text-blue-600\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 20 20\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 106,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 105,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 15\n          }, this), !isSelected && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 bg-gradient-to-r from-blue-500/0 to-indigo-500/0 group-hover:from-blue-500/5 group-hover:to-indigo-500/5 transition-all duration-300 rounded-xl\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 17\n          }, this)]\n        }, optionKey, true, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this);\n  };\n  const renderFillBlank = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"quiz-question-container space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"block text-sm font-medium text-gray-700 mb-2\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-5 h-5 text-blue-500\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Your Answer:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: currentAnswer,\n          onChange: e => handleAnswerSelect(e.target.value),\n          placeholder: \"Type your answer here...\",\n          className: \"quiz-fill-input w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:ring-4 focus:ring-blue-100 transition-all duration-300 text-lg font-medium bg-white shadow-sm\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute right-4 top-1/2 transform -translate-y-1/2\",\n          children: currentAnswer ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-6 h-6 bg-green-500 rounded-full flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-4 h-4 text-white\",\n              fill: \"currentColor\",\n              viewBox: \"0 0 20 20\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                fillRule: \"evenodd\",\n                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                clipRule: \"evenodd\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-4 h-4 text-gray-400\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 7\n    }, this), currentAnswer && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-emerald-50 to-green-50 rounded-xl p-4 sm:p-6 border border-emerald-200 shadow-sm\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-3 sm:space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-8 h-8 bg-gradient-to-br from-emerald-500 to-green-500 rounded-full flex items-center justify-center shadow-sm\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-5 h-5 text-white\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-emerald-800 font-semibold text-sm\",\n            children: \"Your Answer:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-emerald-900 font-bold text-lg\",\n            children: currentAnswer\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 122,\n    columnNumber: 5\n  }, this);\n  const renderImageQuestion = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-8\",\n    children: [question.imageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"inline-block p-2 bg-white rounded-xl shadow-lg border border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: question.imageUrl,\n          alt: \"Question diagram\",\n          className: \"max-w-full max-h-96 rounded-lg mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 9\n    }, this), question.options ? renderMCQ() : renderFillBlank()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 177,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"quiz-container quiz-responsive min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"quiz-progress-bar relative\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-gray-200 rounded-full\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"quiz-progress-fill relative z-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full transition-all duration-500 ease-out\",\n        style: {\n          width: `${progressPercentage}%`\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute right-0 top-1/2 transform -translate-y-1/2 w-3 h-3 bg-white rounded-full shadow-md\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute right-2 top-1/2 transform -translate-y-1/2 text-xs font-medium text-gray-600\",\n        children: [Math.round(progressPercentage), \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"quiz-progress-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"quiz-header-content-improved bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-4 sm:p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"quiz-title-section\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white text-sm font-bold\",\n                children: \"Q\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"quiz-subtitle text-sm sm:text-base font-semibold text-gray-800\",\n              children: examTitle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"quiz-timer-center\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `quiz-timer ${isTimeWarning ? 'warning animate-pulse' : ''} bg-gradient-to-r ${isTimeWarning ? 'from-red-600 to-red-700 border-red-300' : 'from-blue-600 to-indigo-700 border-blue-300'} text-white rounded-xl px-6 py-3 font-mono font-bold shadow-2xl border-2`,\n            style: {\n              textShadow: '1px 1px 2px rgba(0,0,0,0.5)'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-semibold block mb-1\",\n              style: {\n                textShadow: '1px 1px 2px rgba(0,0,0,0.5)'\n              },\n              children: \"TIME LEFT\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xl font-black\",\n              style: {\n                textShadow: '2px 2px 4px rgba(0,0,0,0.7)'\n              },\n              children: formatTime(timeLeft)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"quiz-question-counter-right\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-r from-gray-100 to-gray-200 rounded-xl px-4 py-2 text-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-bold text-gray-800\",\n              children: [questionIndex + 1, \" of \", totalQuestions]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 211,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"quiz-content pb-20 quiz-content-responsive px-4 sm:px-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"quiz-question-container max-w-4xl mx-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6 sm:p-8 mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"quiz-question-number text-sm sm:text-base mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"inline-flex items-center space-x-2 bg-gradient-to-r from-blue-100 to-indigo-100 rounded-full px-4 py-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-2 h-2 bg-blue-500 rounded-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-semibold text-blue-800\",\n                children: [questionIndex + 1, \" of \", totalQuestions]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"quiz-question-text text-lg sm:text-xl lg:text-2xl font-medium text-gray-800 leading-relaxed mb-6\",\n            children: question.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this), question.image && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"quiz-image-container mb-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative rounded-xl overflow-hidden shadow-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: question.image,\n                alt: \"Question\",\n                className: \"quiz-image w-full h-auto max-h-96 object-contain bg-gray-50\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-t from-black/10 to-transparent pointer-events-none\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"quiz-options-container\",\n            children: [question.answerType === \"Options\" && renderMCQ(), (question.answerType === \"Free Text\" || question.answerType === \"Fill in the Blank\") && renderFillBlank(), question.answerType === \"Image\" && question.imageUrl && renderImageQuestion()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 242,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"quiz-navigation fixed bottom-0 left-0 right-0 bg-white/95 backdrop-blur-sm border-t border-gray-200 p-4 sm:p-6 z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onPrevious,\n          disabled: questionIndex === 0,\n          className: `quiz-nav-btn flex items-center space-x-2 px-6 py-3 rounded-xl font-medium transition-all duration-300 ${questionIndex === 0 ? 'disabled bg-gray-100 text-gray-400 cursor-not-allowed' : 'secondary bg-gray-200 hover:bg-gray-300 text-gray-700 hover:text-gray-900 transform hover:scale-105'}`,\n          title: questionIndex === 0 ? 'This is the first question' : 'Go to previous question',\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M15 19l-7-7 7-7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Previous\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3 text-sm text-gray-600\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden sm:flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-2 h-2 bg-blue-500 rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Question \", questionIndex + 1, \" of \", totalQuestions]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 13\n          }, this), !isAnswered && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 text-amber-600 bg-amber-50 px-3 py-1 rounded-full\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-4 h-4\",\n              fill: \"currentColor\",\n              viewBox: \"0 0 20 20\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                fillRule: \"evenodd\",\n                d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                clipRule: \"evenodd\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs font-medium\",\n              children: \"Select an answer\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onNext,\n          disabled: !isAnswered,\n          className: `quiz-nav-btn flex items-center space-x-2 px-6 py-3 rounded-xl font-medium transition-all duration-300 ${!isAnswered ? 'disabled bg-gray-100 text-gray-400 cursor-not-allowed' : questionIndex === totalQuestions - 1 ? 'primary bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white shadow-lg transform hover:scale-105' : 'primary bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white shadow-lg transform hover:scale-105'}`,\n          title: !isAnswered ? 'Please select an answer first' : questionIndex === totalQuestions - 1 ? 'Submit your quiz' : 'Go to next question',\n          children: questionIndex === totalQuestions - 1 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-5 h-5\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M5 13l4 4L19 7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Submit Quiz\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Next\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-5 h-5\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M9 5l7 7-7 7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 279,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 195,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizRenderer, \"GLXCrRLAt2Wgb0CPI+PSeCbLXgs=\");\n_c = QuizRenderer;\nexport default QuizRenderer;\nvar _c;\n$RefreshReg$(_c, \"QuizRenderer\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Quiz<PERSON><PERSON><PERSON>", "question", "questionIndex", "totalQuestions", "<PERSON><PERSON><PERSON><PERSON>", "onAnswerChange", "timeLeft", "onNext", "onPrevious", "examTitle", "isTimeWarning", "_s", "currentAnswer", "setCurrentAnswer", "isAnswered", "setIsAnswered", "handleAnswerSelect", "answer", "formatTime", "seconds", "minutes", "Math", "floor", "remainingSeconds", "toString", "padStart", "progressPercentage", "name", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "renderMCQ", "options", "Object", "keys", "length", "optionLabels", "entries", "map", "key", "value", "index", "optionKey", "String", "trim", "optionValue", "label", "isSelected", "onClick", "fill", "viewBox", "fillRule", "d", "clipRule", "renderFillBlank", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "type", "onChange", "e", "target", "placeholder", "renderImageQuestion", "imageUrl", "src", "alt", "style", "width", "round", "textShadow", "image", "answerType", "disabled", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/QuizRenderer.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport '../pages/user/Quiz/responsive.css';\n\nconst QuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  onAnswerChange,\n  timeLeft,\n  onNext,\n  onPrevious,\n  examTitle = \"Quiz\",\n  isTimeWarning = false\n}) => {\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n\n  const handleAnswerSelect = (answer) => {\n    setCurrentAnswer(answer);\n    setIsAnswered(true);\n    onAnswerChange(answer);\n  };\n\n  const formatTime = (seconds) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  const progressPercentage = ((questionIndex + 1) / totalQuestions) * 100;\n\n  // Early return for invalid question\n  if (!question || !question.name) {\n    return (\n      <div className=\"quiz-container quiz-responsive\">\n        <div className=\"quiz-content quiz-content-responsive\">\n          <div className=\"quiz-question-container\">\n            <div className=\"text-center text-red-600 p-6 bg-red-50 rounded-lg border border-red-200\">\n              <i className=\"ri-error-warning-line text-3xl mb-3 block\"></i>\n              <h3 className=\"text-lg font-semibold mb-2\">Question Not Available</h3>\n              <p className=\"text-sm\">This question could not be loaded. Please try refreshing the page.</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  const renderMCQ = () => {\n    if (!question || !question.options || Object.keys(question.options).length === 0) {\n      return (\n        <div className=\"quiz-question-container\">\n          <div className=\"text-center text-red-600 p-4 bg-red-50 rounded-lg border border-red-200\">\n            <i className=\"ri-error-warning-line text-2xl mb-2\"></i>\n            <p>No options available for this question.</p>\n          </div>\n        </div>\n      );\n    }\n\n    const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];\n\n    return (\n      <div className=\"quiz-options space-y-4\">\n        {Object.entries(question.options).map(([key, value], index) => {\n          const optionKey = String(key).trim();\n          const optionValue = String(value || '').trim();\n          const label = optionLabels[index] || optionKey;\n          const isSelected = currentAnswer === optionKey;\n\n          // Skip empty options\n          if (!optionValue) return null;\n\n          return (\n            <button\n              key={optionKey}\n              onClick={() => handleAnswerSelect(optionKey)}\n              className={`quiz-option w-full text-left transition-all duration-300 transform hover:scale-[1.02] ${\n                isSelected\n                  ? 'selected bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg border-blue-500'\n                  : 'bg-white hover:bg-blue-50 border-gray-200 hover:border-blue-300 text-gray-800'\n              } border-2 rounded-xl p-4 sm:p-5 group relative overflow-hidden`}\n            >\n              <div className=\"flex items-center space-x-4\">\n                <div className={`quiz-option-letter flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm transition-all duration-300 ${\n                  isSelected\n                    ? 'bg-white text-blue-600 shadow-md'\n                    : 'bg-gradient-to-br from-blue-100 to-indigo-100 text-blue-700 group-hover:from-blue-200 group-hover:to-indigo-200'\n                }`}>\n                  {label}\n                </div>\n                <span className={`quiz-option-text flex-1 font-medium transition-all duration-300 ${\n                  isSelected ? 'text-white' : 'text-gray-800 group-hover:text-blue-800'\n                }`}>\n                  {optionValue}\n                </span>\n                {isSelected && (\n                  <div className=\"flex-shrink-0 w-6 h-6 bg-white rounded-full flex items-center justify-center\">\n                    <svg className=\"w-4 h-4 text-blue-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                    </svg>\n                  </div>\n                )}\n              </div>\n              {!isSelected && (\n                <div className=\"absolute inset-0 bg-gradient-to-r from-blue-500/0 to-indigo-500/0 group-hover:from-blue-500/5 group-hover:to-indigo-500/5 transition-all duration-300 rounded-xl\"></div>\n              )}\n            </button>\n          );\n        })}\n      </div>\n    );\n  };\n\n  const renderFillBlank = () => (\n    <div className=\"quiz-question-container space-y-6\">\n      <div className=\"space-y-3\">\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          <div className=\"flex items-center space-x-2\">\n            <svg className=\"w-5 h-5 text-blue-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z\" />\n            </svg>\n            <span>Your Answer:</span>\n          </div>\n        </label>\n        <div className=\"relative\">\n          <input\n            type=\"text\"\n            value={currentAnswer}\n            onChange={(e) => handleAnswerSelect(e.target.value)}\n            placeholder=\"Type your answer here...\"\n            className=\"quiz-fill-input w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:ring-4 focus:ring-blue-100 transition-all duration-300 text-lg font-medium bg-white shadow-sm\"\n          />\n          <div className=\"absolute right-4 top-1/2 transform -translate-y-1/2\">\n            {currentAnswer ? (\n              <div className=\"w-6 h-6 bg-green-500 rounded-full flex items-center justify-center\">\n                <svg className=\"w-4 h-4 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                </svg>\n              </div>\n            ) : (\n              <div className=\"w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center\">\n                <svg className=\"w-4 h-4 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z\" />\n                </svg>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {currentAnswer && (\n        <div className=\"bg-gradient-to-r from-emerald-50 to-green-50 rounded-xl p-4 sm:p-6 border border-emerald-200 shadow-sm\">\n          <div className=\"flex items-center space-x-3 sm:space-x-4\">\n            <div className=\"w-8 h-8 bg-gradient-to-br from-emerald-500 to-green-500 rounded-full flex items-center justify-center shadow-sm\">\n              <svg className=\"w-5 h-5 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n              </svg>\n            </div>\n            <div>\n              <p className=\"text-emerald-800 font-semibold text-sm\">Your Answer:</p>\n              <p className=\"text-emerald-900 font-bold text-lg\">{currentAnswer}</p>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n\n  const renderImageQuestion = () => (\n    <div className=\"space-y-8\">\n      {question.imageUrl && (\n        <div className=\"text-center\">\n          <div className=\"inline-block p-2 bg-white rounded-xl shadow-lg border border-gray-200\">\n            <img\n              src={question.imageUrl}\n              alt=\"Question diagram\"\n              className=\"max-w-full max-h-96 rounded-lg mx-auto\"\n            />\n          </div>\n        </div>\n      )}\n\n      {question.options ? renderMCQ() : renderFillBlank()}\n    </div>\n  );\n\n  return (\n    <div className=\"quiz-container quiz-responsive min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50\">\n      {/* Enhanced Progress Bar */}\n      <div className=\"quiz-progress-bar relative\">\n        <div className=\"absolute inset-0 bg-gray-200 rounded-full\"></div>\n        <div\n          className=\"quiz-progress-fill relative z-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full transition-all duration-500 ease-out\"\n          style={{ width: `${progressPercentage}%` }}\n        >\n          <div className=\"absolute right-0 top-1/2 transform -translate-y-1/2 w-3 h-3 bg-white rounded-full shadow-md\"></div>\n        </div>\n        <div className=\"absolute right-2 top-1/2 transform -translate-y-1/2 text-xs font-medium text-gray-600\">\n          {Math.round(progressPercentage)}%\n        </div>\n      </div>\n\n      {/* Enhanced Header */}\n      <div className=\"quiz-progress-container\">\n        <div className=\"quiz-header-content-improved bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-4 sm:p-6\">\n          <div className=\"quiz-title-section\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white text-sm font-bold\">Q</span>\n              </div>\n              <p className=\"quiz-subtitle text-sm sm:text-base font-semibold text-gray-800\">\n                {examTitle}\n              </p>\n            </div>\n          </div>\n\n          <div className=\"quiz-timer-center\">\n            <div className={`quiz-timer ${isTimeWarning ? 'warning animate-pulse' : ''} bg-gradient-to-r ${isTimeWarning ? 'from-red-600 to-red-700 border-red-300' : 'from-blue-600 to-indigo-700 border-blue-300'} text-white rounded-xl px-6 py-3 font-mono font-bold shadow-2xl border-2`} style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }}>\n              <span className=\"text-sm font-semibold block mb-1\" style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }}>TIME LEFT</span>\n              <div className=\"text-xl font-black\" style={{ textShadow: '2px 2px 4px rgba(0,0,0,0.7)' }}>\n                {formatTime(timeLeft)}\n              </div>\n            </div>\n          </div>\n\n          <div className=\"quiz-question-counter-right\">\n            <div className=\"bg-gradient-to-r from-gray-100 to-gray-200 rounded-xl px-4 py-2 text-center\">\n              <span className=\"font-bold text-gray-800\">{questionIndex + 1} of {totalQuestions}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Enhanced Question Content */}\n      <div className=\"quiz-content pb-20 quiz-content-responsive px-4 sm:px-6\">\n        <div className=\"quiz-question-container max-w-4xl mx-auto\">\n          <div className=\"bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6 sm:p-8 mb-6\">\n            <div className=\"quiz-question-number text-sm sm:text-base mb-4\">\n              <div className=\"inline-flex items-center space-x-2 bg-gradient-to-r from-blue-100 to-indigo-100 rounded-full px-4 py-2\">\n                <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                <span className=\"font-semibold text-blue-800\">{questionIndex + 1} of {totalQuestions}</span>\n              </div>\n            </div>\n\n            <div className=\"quiz-question-text text-lg sm:text-xl lg:text-2xl font-medium text-gray-800 leading-relaxed mb-6\">\n              {question.name}\n            </div>\n\n            {question.image && (\n              <div className=\"quiz-image-container mb-6\">\n                <div className=\"relative rounded-xl overflow-hidden shadow-lg\">\n                  <img\n                    src={question.image}\n                    alt=\"Question\"\n                    className=\"quiz-image w-full h-auto max-h-96 object-contain bg-gray-50\"\n                  />\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-black/10 to-transparent pointer-events-none\"></div>\n                </div>\n              </div>\n            )}\n\n            <div className=\"quiz-options-container\">\n              {question.answerType === \"Options\" && renderMCQ()}\n              {(question.answerType === \"Free Text\" || question.answerType === \"Fill in the Blank\") && renderFillBlank()}\n              {question.answerType === \"Image\" && question.imageUrl && renderImageQuestion()}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Enhanced Navigation */}\n      <div className=\"quiz-navigation fixed bottom-0 left-0 right-0 bg-white/95 backdrop-blur-sm border-t border-gray-200 p-4 sm:p-6 z-50\">\n        <div className=\"max-w-4xl mx-auto flex items-center justify-between\">\n          <button\n            onClick={onPrevious}\n            disabled={questionIndex === 0}\n            className={`quiz-nav-btn flex items-center space-x-2 px-6 py-3 rounded-xl font-medium transition-all duration-300 ${\n              questionIndex === 0\n                ? 'disabled bg-gray-100 text-gray-400 cursor-not-allowed'\n                : 'secondary bg-gray-200 hover:bg-gray-300 text-gray-700 hover:text-gray-900 transform hover:scale-105'\n            }`}\n            title={questionIndex === 0 ? 'This is the first question' : 'Go to previous question'}\n          >\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n            </svg>\n            <span>Previous</span>\n          </button>\n\n          <div className=\"flex items-center space-x-3 text-sm text-gray-600\">\n            <div className=\"hidden sm:flex items-center space-x-2\">\n              <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n              <span>Question {questionIndex + 1} of {totalQuestions}</span>\n            </div>\n            {!isAnswered && (\n              <div className=\"flex items-center space-x-2 text-amber-600 bg-amber-50 px-3 py-1 rounded-full\">\n                <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                </svg>\n                <span className=\"text-xs font-medium\">Select an answer</span>\n              </div>\n            )}\n          </div>\n\n          <button\n            onClick={onNext}\n            disabled={!isAnswered}\n            className={`quiz-nav-btn flex items-center space-x-2 px-6 py-3 rounded-xl font-medium transition-all duration-300 ${\n              !isAnswered\n                ? 'disabled bg-gray-100 text-gray-400 cursor-not-allowed'\n                : questionIndex === totalQuestions - 1\n                  ? 'primary bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white shadow-lg transform hover:scale-105'\n                  : 'primary bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white shadow-lg transform hover:scale-105'\n            }`}\n            title={!isAnswered ? 'Please select an answer first' :\n                   questionIndex === totalQuestions - 1 ? 'Submit your quiz' : 'Go to next question'}\n          >\n            {questionIndex === totalQuestions - 1 ? (\n              <>\n                <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                </svg>\n                <span>Submit Quiz</span>\n              </>\n            ) : (\n              <>\n                <span>Next</span>\n                <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                </svg>\n              </>\n            )}\n          </button>\n        </div>\n      </div>\n\n\n    </div>\n  );\n};\n\nexport default QuizRenderer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAE3C,MAAMC,YAAY,GAAGA,CAAC;EACpBC,QAAQ;EACRC,aAAa;EACbC,cAAc;EACdC,cAAc;EACdC,cAAc;EACdC,QAAQ;EACRC,MAAM;EACNC,UAAU;EACVC,SAAS,GAAG,MAAM;EAClBC,aAAa,GAAG;AAClB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGnB,QAAQ,CAACU,cAAc,IAAI,EAAE,CAAC;EACxE,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAEnDC,SAAS,CAAC,MAAM;IACdkB,gBAAgB,CAACT,cAAc,IAAI,EAAE,CAAC;IACtCW,aAAa,CAAC,CAAC,CAACX,cAAc,CAAC;EACjC,CAAC,EAAE,CAACA,cAAc,EAAEF,aAAa,CAAC,CAAC;EAEnC,MAAMc,kBAAkB,GAAIC,MAAM,IAAK;IACrCJ,gBAAgB,CAACI,MAAM,CAAC;IACxBF,aAAa,CAAC,IAAI,CAAC;IACnBV,cAAc,CAACY,MAAM,CAAC;EACxB,CAAC;EAED,MAAMC,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;IACrC,OAAQ,GAAEC,OAAQ,IAAGG,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,EAAC;EACrE,CAAC;EAED,MAAMC,kBAAkB,GAAI,CAACxB,aAAa,GAAG,CAAC,IAAIC,cAAc,GAAI,GAAG;;EAEvE;EACA,IAAI,CAACF,QAAQ,IAAI,CAACA,QAAQ,CAAC0B,IAAI,EAAE;IAC/B,oBACE9B,OAAA;MAAK+B,SAAS,EAAC,gCAAgC;MAAAC,QAAA,eAC7ChC,OAAA;QAAK+B,SAAS,EAAC,sCAAsC;QAAAC,QAAA,eACnDhC,OAAA;UAAK+B,SAAS,EAAC,yBAAyB;UAAAC,QAAA,eACtChC,OAAA;YAAK+B,SAAS,EAAC,yEAAyE;YAAAC,QAAA,gBACtFhC,OAAA;cAAG+B,SAAS,EAAC;YAA2C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7DpC,OAAA;cAAI+B,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtEpC,OAAA;cAAG+B,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAkE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAMC,SAAS,GAAGA,CAAA,KAAM;IACtB,IAAI,CAACjC,QAAQ,IAAI,CAACA,QAAQ,CAACkC,OAAO,IAAIC,MAAM,CAACC,IAAI,CAACpC,QAAQ,CAACkC,OAAO,CAAC,CAACG,MAAM,KAAK,CAAC,EAAE;MAChF,oBACEzC,OAAA;QAAK+B,SAAS,EAAC,yBAAyB;QAAAC,QAAA,eACtChC,OAAA;UAAK+B,SAAS,EAAC,yEAAyE;UAAAC,QAAA,gBACtFhC,OAAA;YAAG+B,SAAS,EAAC;UAAqC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvDpC,OAAA;YAAAgC,QAAA,EAAG;UAAuC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEV;IAEA,MAAMM,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAEnD,oBACE1C,OAAA;MAAK+B,SAAS,EAAC,wBAAwB;MAAAC,QAAA,EACpCO,MAAM,CAACI,OAAO,CAACvC,QAAQ,CAACkC,OAAO,CAAC,CAACM,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,EAAEC,KAAK,KAAK;QAC7D,MAAMC,SAAS,GAAGC,MAAM,CAACJ,GAAG,CAAC,CAACK,IAAI,CAAC,CAAC;QACpC,MAAMC,WAAW,GAAGF,MAAM,CAACH,KAAK,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC;QAC9C,MAAME,KAAK,GAAGV,YAAY,CAACK,KAAK,CAAC,IAAIC,SAAS;QAC9C,MAAMK,UAAU,GAAGtC,aAAa,KAAKiC,SAAS;;QAE9C;QACA,IAAI,CAACG,WAAW,EAAE,OAAO,IAAI;QAE7B,oBACEnD,OAAA;UAEEsD,OAAO,EAAEA,CAAA,KAAMnC,kBAAkB,CAAC6B,SAAS,CAAE;UAC7CjB,SAAS,EAAG,yFACVsB,UAAU,GACN,4FAA4F,GAC5F,+EACL,gEAAgE;UAAArB,QAAA,gBAEjEhC,OAAA;YAAK+B,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1ChC,OAAA;cAAK+B,SAAS,EAAG,0IACfsB,UAAU,GACN,kCAAkC,GAClC,iHACL,EAAE;cAAArB,QAAA,EACAoB;YAAK;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNpC,OAAA;cAAM+B,SAAS,EAAG,mEAChBsB,UAAU,GAAG,YAAY,GAAG,yCAC7B,EAAE;cAAArB,QAAA,EACAmB;YAAW;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,EACNiB,UAAU,iBACTrD,OAAA;cAAK+B,SAAS,EAAC,8EAA8E;cAAAC,QAAA,eAC3FhC,OAAA;gBAAK+B,SAAS,EAAC,uBAAuB;gBAACwB,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAxB,QAAA,eAC5EhC,OAAA;kBAAMyD,QAAQ,EAAC,SAAS;kBAACC,CAAC,EAAC,oHAAoH;kBAACC,QAAQ,EAAC;gBAAS;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EACL,CAACiB,UAAU,iBACVrD,OAAA;YAAK+B,SAAS,EAAC;UAAkK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACxL;QAAA,GA/BIY,SAAS;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgCR,CAAC;MAEb,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV,CAAC;EAED,MAAMwB,eAAe,GAAGA,CAAA,kBACtB5D,OAAA;IAAK+B,SAAS,EAAC,mCAAmC;IAAAC,QAAA,gBAChDhC,OAAA;MAAK+B,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBhC,OAAA;QAAO+B,SAAS,EAAC,8CAA8C;QAAAC,QAAA,eAC7DhC,OAAA;UAAK+B,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1ChC,OAAA;YAAK+B,SAAS,EAAC,uBAAuB;YAACwB,IAAI,EAAC,MAAM;YAACM,MAAM,EAAC,cAAc;YAACL,OAAO,EAAC,WAAW;YAAAxB,QAAA,eAC1FhC,OAAA;cAAM8D,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACN,CAAC,EAAC;YAAkG;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvK,CAAC,eACNpC,OAAA;YAAAgC,QAAA,EAAM;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACRpC,OAAA;QAAK+B,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACvBhC,OAAA;UACEiE,IAAI,EAAC,MAAM;UACXnB,KAAK,EAAE/B,aAAc;UACrBmD,QAAQ,EAAGC,CAAC,IAAKhD,kBAAkB,CAACgD,CAAC,CAACC,MAAM,CAACtB,KAAK,CAAE;UACpDuB,WAAW,EAAC,0BAA0B;UACtCtC,SAAS,EAAC;QAAgM;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3M,CAAC,eACFpC,OAAA;UAAK+B,SAAS,EAAC,qDAAqD;UAAAC,QAAA,EACjEjB,aAAa,gBACZf,OAAA;YAAK+B,SAAS,EAAC,oEAAoE;YAAAC,QAAA,eACjFhC,OAAA;cAAK+B,SAAS,EAAC,oBAAoB;cAACwB,IAAI,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAxB,QAAA,eACzEhC,OAAA;gBAAMyD,QAAQ,EAAC,SAAS;gBAACC,CAAC,EAAC,oHAAoH;gBAACC,QAAQ,EAAC;cAAS;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAENpC,OAAA;YAAK+B,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAChFhC,OAAA;cAAK+B,SAAS,EAAC,uBAAuB;cAACwB,IAAI,EAAC,MAAM;cAACM,MAAM,EAAC,cAAc;cAACL,OAAO,EAAC,WAAW;cAAAxB,QAAA,eAC1FhC,OAAA;gBAAM8D,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACN,CAAC,EAAC;cAAkG;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELrB,aAAa,iBACZf,OAAA;MAAK+B,SAAS,EAAC,wGAAwG;MAAAC,QAAA,eACrHhC,OAAA;QAAK+B,SAAS,EAAC,0CAA0C;QAAAC,QAAA,gBACvDhC,OAAA;UAAK+B,SAAS,EAAC,iHAAiH;UAAAC,QAAA,eAC9HhC,OAAA;YAAK+B,SAAS,EAAC,oBAAoB;YAACwB,IAAI,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAxB,QAAA,eACzEhC,OAAA;cAAMyD,QAAQ,EAAC,SAAS;cAACC,CAAC,EAAC,oHAAoH;cAACC,QAAQ,EAAC;YAAS;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNpC,OAAA;UAAAgC,QAAA,gBACEhC,OAAA;YAAG+B,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACtEpC,OAAA;YAAG+B,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAAEjB;UAAa;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;EAED,MAAMkC,mBAAmB,GAAGA,CAAA,kBAC1BtE,OAAA;IAAK+B,SAAS,EAAC,WAAW;IAAAC,QAAA,GACvB5B,QAAQ,CAACmE,QAAQ,iBAChBvE,OAAA;MAAK+B,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1BhC,OAAA;QAAK+B,SAAS,EAAC,uEAAuE;QAAAC,QAAA,eACpFhC,OAAA;UACEwE,GAAG,EAAEpE,QAAQ,CAACmE,QAAS;UACvBE,GAAG,EAAC,kBAAkB;UACtB1C,SAAS,EAAC;QAAwC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEAhC,QAAQ,CAACkC,OAAO,GAAGD,SAAS,CAAC,CAAC,GAAGuB,eAAe,CAAC,CAAC;EAAA;IAAA3B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChD,CACN;EAED,oBACEpC,OAAA;IAAK+B,SAAS,EAAC,uGAAuG;IAAAC,QAAA,gBAEpHhC,OAAA;MAAK+B,SAAS,EAAC,4BAA4B;MAAAC,QAAA,gBACzChC,OAAA;QAAK+B,SAAS,EAAC;MAA2C;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACjEpC,OAAA;QACE+B,SAAS,EAAC,iIAAiI;QAC3I2C,KAAK,EAAE;UAAEC,KAAK,EAAG,GAAE9C,kBAAmB;QAAG,CAAE;QAAAG,QAAA,eAE3ChC,OAAA;UAAK+B,SAAS,EAAC;QAA6F;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChH,CAAC,eACNpC,OAAA;QAAK+B,SAAS,EAAC,uFAAuF;QAAAC,QAAA,GACnGR,IAAI,CAACoD,KAAK,CAAC/C,kBAAkB,CAAC,EAAC,GAClC;MAAA;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpC,OAAA;MAAK+B,SAAS,EAAC,yBAAyB;MAAAC,QAAA,eACtChC,OAAA;QAAK+B,SAAS,EAAC,mHAAmH;QAAAC,QAAA,gBAChIhC,OAAA;UAAK+B,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjChC,OAAA;YAAK+B,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1ChC,OAAA;cAAK+B,SAAS,EAAC,mGAAmG;cAAAC,QAAA,eAChHhC,OAAA;gBAAM+B,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACNpC,OAAA;cAAG+B,SAAS,EAAC,gEAAgE;cAAAC,QAAA,EAC1EpB;YAAS;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENpC,OAAA;UAAK+B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChChC,OAAA;YAAK+B,SAAS,EAAG,cAAalB,aAAa,GAAG,uBAAuB,GAAG,EAAG,qBAAoBA,aAAa,GAAG,wCAAwC,GAAG,6CAA8C,0EAA0E;YAAC6D,KAAK,EAAE;cAAEG,UAAU,EAAE;YAA8B,CAAE;YAAA7C,QAAA,gBACtUhC,OAAA;cAAM+B,SAAS,EAAC,kCAAkC;cAAC2C,KAAK,EAAE;gBAAEG,UAAU,EAAE;cAA8B,CAAE;cAAA7C,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzHpC,OAAA;cAAK+B,SAAS,EAAC,oBAAoB;cAAC2C,KAAK,EAAE;gBAAEG,UAAU,EAAE;cAA8B,CAAE;cAAA7C,QAAA,EACtFX,UAAU,CAACZ,QAAQ;YAAC;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENpC,OAAA;UAAK+B,SAAS,EAAC,6BAA6B;UAAAC,QAAA,eAC1ChC,OAAA;YAAK+B,SAAS,EAAC,6EAA6E;YAAAC,QAAA,eAC1FhC,OAAA;cAAM+B,SAAS,EAAC,yBAAyB;cAAAC,QAAA,GAAE3B,aAAa,GAAG,CAAC,EAAC,MAAI,EAACC,cAAc;YAAA;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpC,OAAA;MAAK+B,SAAS,EAAC,yDAAyD;MAAAC,QAAA,eACtEhC,OAAA;QAAK+B,SAAS,EAAC,2CAA2C;QAAAC,QAAA,eACxDhC,OAAA;UAAK+B,SAAS,EAAC,2FAA2F;UAAAC,QAAA,gBACxGhC,OAAA;YAAK+B,SAAS,EAAC,gDAAgD;YAAAC,QAAA,eAC7DhC,OAAA;cAAK+B,SAAS,EAAC,wGAAwG;cAAAC,QAAA,gBACrHhC,OAAA;gBAAK+B,SAAS,EAAC;cAAkC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxDpC,OAAA;gBAAM+B,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,GAAE3B,aAAa,GAAG,CAAC,EAAC,MAAI,EAACC,cAAc;cAAA;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENpC,OAAA;YAAK+B,SAAS,EAAC,kGAAkG;YAAAC,QAAA,EAC9G5B,QAAQ,CAAC0B;UAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,EAELhC,QAAQ,CAAC0E,KAAK,iBACb9E,OAAA;YAAK+B,SAAS,EAAC,2BAA2B;YAAAC,QAAA,eACxChC,OAAA;cAAK+B,SAAS,EAAC,+CAA+C;cAAAC,QAAA,gBAC5DhC,OAAA;gBACEwE,GAAG,EAAEpE,QAAQ,CAAC0E,KAAM;gBACpBL,GAAG,EAAC,UAAU;gBACd1C,SAAS,EAAC;cAA6D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE,CAAC,eACFpC,OAAA;gBAAK+B,SAAS,EAAC;cAAoF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAEDpC,OAAA;YAAK+B,SAAS,EAAC,wBAAwB;YAAAC,QAAA,GACpC5B,QAAQ,CAAC2E,UAAU,KAAK,SAAS,IAAI1C,SAAS,CAAC,CAAC,EAChD,CAACjC,QAAQ,CAAC2E,UAAU,KAAK,WAAW,IAAI3E,QAAQ,CAAC2E,UAAU,KAAK,mBAAmB,KAAKnB,eAAe,CAAC,CAAC,EACzGxD,QAAQ,CAAC2E,UAAU,KAAK,OAAO,IAAI3E,QAAQ,CAACmE,QAAQ,IAAID,mBAAmB,CAAC,CAAC;UAAA;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpC,OAAA;MAAK+B,SAAS,EAAC,qHAAqH;MAAAC,QAAA,eAClIhC,OAAA;QAAK+B,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAClEhC,OAAA;UACEsD,OAAO,EAAE3C,UAAW;UACpBqE,QAAQ,EAAE3E,aAAa,KAAK,CAAE;UAC9B0B,SAAS,EAAG,yGACV1B,aAAa,KAAK,CAAC,GACf,uDAAuD,GACvD,qGACL,EAAE;UACH4E,KAAK,EAAE5E,aAAa,KAAK,CAAC,GAAG,4BAA4B,GAAG,yBAA0B;UAAA2B,QAAA,gBAEtFhC,OAAA;YAAK+B,SAAS,EAAC,SAAS;YAACwB,IAAI,EAAC,MAAM;YAACM,MAAM,EAAC,cAAc;YAACL,OAAO,EAAC,WAAW;YAAAxB,QAAA,eAC5EhC,OAAA;cAAM8D,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACN,CAAC,EAAC;YAAiB;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtF,CAAC,eACNpC,OAAA;YAAAgC,QAAA,EAAM;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eAETpC,OAAA;UAAK+B,SAAS,EAAC,mDAAmD;UAAAC,QAAA,gBAChEhC,OAAA;YAAK+B,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDhC,OAAA;cAAK+B,SAAS,EAAC;YAAkC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxDpC,OAAA;cAAAgC,QAAA,GAAM,WAAS,EAAC3B,aAAa,GAAG,CAAC,EAAC,MAAI,EAACC,cAAc;YAAA;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC,EACL,CAACnB,UAAU,iBACVjB,OAAA;YAAK+B,SAAS,EAAC,+EAA+E;YAAAC,QAAA,gBAC5FhC,OAAA;cAAK+B,SAAS,EAAC,SAAS;cAACwB,IAAI,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAxB,QAAA,eAC9DhC,OAAA;gBAAMyD,QAAQ,EAAC,SAAS;gBAACC,CAAC,EAAC,mNAAmN;gBAACC,QAAQ,EAAC;cAAS;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjQ,CAAC,eACNpC,OAAA;cAAM+B,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENpC,OAAA;UACEsD,OAAO,EAAE5C,MAAO;UAChBsE,QAAQ,EAAE,CAAC/D,UAAW;UACtBc,SAAS,EAAG,yGACV,CAACd,UAAU,GACP,uDAAuD,GACvDZ,aAAa,KAAKC,cAAc,GAAG,CAAC,GAClC,iJAAiJ,GACjJ,6IACP,EAAE;UACH2E,KAAK,EAAE,CAAChE,UAAU,GAAG,+BAA+B,GAC7CZ,aAAa,KAAKC,cAAc,GAAG,CAAC,GAAG,kBAAkB,GAAG,qBAAsB;UAAA0B,QAAA,EAExF3B,aAAa,KAAKC,cAAc,GAAG,CAAC,gBACnCN,OAAA,CAAAE,SAAA;YAAA8B,QAAA,gBACEhC,OAAA;cAAK+B,SAAS,EAAC,SAAS;cAACwB,IAAI,EAAC,MAAM;cAACM,MAAM,EAAC,cAAc;cAACL,OAAO,EAAC,WAAW;cAAAxB,QAAA,eAC5EhC,OAAA;gBAAM8D,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACN,CAAC,EAAC;cAAgB;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrF,CAAC,eACNpC,OAAA;cAAAgC,QAAA,EAAM;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,eACxB,CAAC,gBAEHpC,OAAA,CAAAE,SAAA;YAAA8B,QAAA,gBACEhC,OAAA;cAAAgC,QAAA,EAAM;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjBpC,OAAA;cAAK+B,SAAS,EAAC,SAAS;cAACwB,IAAI,EAAC,MAAM;cAACM,MAAM,EAAC,cAAc;cAACL,OAAO,EAAC,WAAW;cAAAxB,QAAA,eAC5EhC,OAAA;gBAAM8D,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACN,CAAC,EAAC;cAAc;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnF,CAAC;UAAA,eACN;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGH,CAAC;AAEV,CAAC;AAACtB,EAAA,CAvVIX,YAAY;AAAA+E,EAAA,GAAZ/E,YAAY;AAyVlB,eAAeA,YAAY;AAAC,IAAA+E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}