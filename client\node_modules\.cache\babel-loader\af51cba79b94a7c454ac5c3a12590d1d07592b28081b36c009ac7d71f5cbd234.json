{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\modern\\\\QuizCard.js\";\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { Tb<PERSON>lock, TbQuestionMark, TbUsers, TbTrophy, TbPlayerPlay, TbStar, TbTarget, TbBrain, TbChevronRight } from 'react-icons/tb';\nimport { Card, Button } from './index';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuizCard = ({\n  quiz,\n  onStart,\n  onView,\n  showResults = false,\n  userResult = null,\n  className = '',\n  ...props\n}) => {\n  var _quiz$questions, _quiz$questions2;\n  const getDifficultyColor = difficulty => {\n    switch (difficulty === null || difficulty === void 0 ? void 0 : difficulty.toLowerCase()) {\n      case 'easy':\n        return 'bg-green-100 text-green-800';\n      case 'medium':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'hard':\n        return 'bg-red-100 text-red-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n  const getScoreColor = percentage => {\n    if (percentage >= 80) return 'text-green-600';\n    if (percentage >= 60) return 'text-yellow-600';\n    return 'text-red-600';\n  };\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    initial: {\n      opacity: 0,\n      y: 20\n    },\n    animate: {\n      opacity: 1,\n      y: 0\n    },\n    whileHover: {\n      y: -8,\n      scale: 1.02\n    },\n    transition: {\n      duration: 0.3\n    },\n    className: `quiz-card-modern ${className}`,\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      interactive: true,\n      variant: \"default\",\n      className: \"quiz-card overflow-hidden h-full border-0 shadow-lg hover:shadow-xl transition-all duration-300\",\n      ...props,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative overflow-hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-br from-blue-600 via-indigo-700 to-purple-800 p-6 text-white relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 opacity-20\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute top-0 right-0 w-40 h-40 bg-white rounded-full -translate-y-20 translate-x-20 animate-pulse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute bottom-0 left-0 w-32 h-32 bg-white rounded-full translate-y-16 -translate-x-16 animate-pulse delay-700\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute top-1/2 left-1/2 w-20 h-20 bg-white rounded-full -translate-x-10 -translate-y-10 animate-pulse delay-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute top-4 right-8 w-2 h-2 bg-white rounded-full animate-bounce delay-100\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute top-12 right-16 w-1 h-1 bg-white rounded-full animate-bounce delay-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute bottom-8 left-12 w-1.5 h-1.5 bg-white rounded-full animate-bounce delay-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 animate-shimmer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative z-10\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start justify-between mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3 mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-14 h-14 bg-white/20 backdrop-blur-md rounded-2xl flex items-center justify-center border border-white/30 shadow-xl group-hover:scale-110 transition-transform duration-300\",\n                    children: /*#__PURE__*/_jsxDEV(TbBrain, {\n                      className: \"w-7 h-7 text-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 73,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 72,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-2 mb-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-blue-100 text-xs font-bold uppercase tracking-wider bg-white/20 px-2 py-1 rounded-full\",\n                        children: [\"Class \", quiz.class || 'N/A']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 77,\n                        columnNumber: 25\n                      }, this), quiz.subject && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-purple-100 text-xs font-bold uppercase tracking-wider bg-purple-500/30 px-2 py-1 rounded-full\",\n                        children: quiz.subject\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 81,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 76,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white text-sm font-bold flex items-center space-x-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"flex items-center space-x-1\",\n                        children: [/*#__PURE__*/_jsxDEV(TbQuestionMark, {\n                          className: \"w-4 h-4\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 88,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: ((_quiz$questions = quiz.questions) === null || _quiz$questions === void 0 ? void 0 : _quiz$questions.length) || 0\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 89,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 87,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"flex items-center space-x-1\",\n                        children: [/*#__PURE__*/_jsxDEV(TbClock, {\n                          className: \"w-4 h-4\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 92,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: [quiz.duration || 30, \"m\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 93,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 91,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"flex items-center space-x-1\",\n                        children: [/*#__PURE__*/_jsxDEV(TbTarget, {\n                          className: \"w-4 h-4\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 96,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: [quiz.passingMarks || 70, \"%\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 97,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 95,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 86,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 75,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 71,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-xl font-bold mb-3 line-clamp-2 text-white drop-shadow-lg leading-tight\",\n                  children: quiz.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 102,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-blue-100 text-sm line-clamp-2 opacity-90 font-medium leading-relaxed\",\n                  children: quiz.description || 'Test your knowledge with this comprehensive quiz and track your progress'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 105,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 70,\n                columnNumber: 17\n              }, this), showResults && userResult && /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  scale: 0\n                },\n                animate: {\n                  scale: 1\n                },\n                className: `px-3 py-2 rounded-xl text-xs font-bold shadow-xl border backdrop-blur-sm ml-4 ${userResult.verdict === 'Pass' ? 'bg-emerald-500/90 text-white border-emerald-400/50' : 'bg-red-500/90 text-white border-red-400/50'}`,\n                children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n                  className: \"w-4 h-4 inline mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 119,\n                  columnNumber: 21\n                }, this), userResult.verdict]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6 pb-4 bg-gradient-to-br from-gray-50/50 to-white relative\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 opacity-5\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-0 left-0 w-full h-full bg-gradient-to-br from-blue-500 to-purple-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative z-10\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-3 gap-3 mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              whileHover: {\n                scale: 1.05,\n                y: -2\n              },\n              className: \"bg-gradient-to-br from-blue-50 via-blue-100/80 to-blue-200/60 rounded-2xl p-4 text-center border border-blue-200/70 hover:border-blue-400/70 transition-all duration-300 hover:shadow-xl group relative overflow-hidden\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-blue-500/5 to-blue-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative z-10\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300 shadow-lg\",\n                  children: /*#__PURE__*/_jsxDEV(TbQuestionMark, {\n                    className: \"w-5 h-5 text-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 145,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-2xl font-bold text-blue-700 mb-1\",\n                  children: ((_quiz$questions2 = quiz.questions) === null || _quiz$questions2 === void 0 ? void 0 : _quiz$questions2.length) || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-blue-600 font-semibold uppercase tracking-wide\",\n                  children: \"Questions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              whileHover: {\n                scale: 1.05,\n                y: -2\n              },\n              className: \"bg-gradient-to-br from-emerald-50 via-emerald-100/80 to-emerald-200/60 rounded-2xl p-4 text-center border border-emerald-200/70 hover:border-emerald-400/70 transition-all duration-300 hover:shadow-xl group relative overflow-hidden\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-emerald-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative z-10\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300 shadow-lg\",\n                  children: /*#__PURE__*/_jsxDEV(TbClock, {\n                    className: \"w-5 h-5 text-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 159,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 158,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-2xl font-bold text-emerald-700 mb-1\",\n                  children: quiz.duration || 30\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 161,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-emerald-600 font-semibold uppercase tracking-wide\",\n                  children: \"Minutes\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 162,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              whileHover: {\n                scale: 1.05,\n                y: -2\n              },\n              className: \"bg-gradient-to-br from-purple-50 via-purple-100/80 to-purple-200/60 rounded-2xl p-4 text-center border border-purple-200/70 hover:border-purple-400/70 transition-all duration-300 hover:shadow-xl group relative overflow-hidden\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-purple-500/5 to-purple-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative z-10\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300 shadow-lg\",\n                  children: /*#__PURE__*/_jsxDEV(TbStar, {\n                    className: \"w-5 h-5 text-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 173,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-2xl font-bold text-purple-700 mb-1\",\n                  children: quiz.passingMarks || 70\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-purple-600 font-semibold uppercase tracking-wide\",\n                  children: \"Pass %\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 176,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [quiz.subject && /*#__PURE__*/_jsxDEV(motion.span, {\n              whileHover: {\n                scale: 1.05\n              },\n              className: \"inline-flex items-center px-4 py-2 rounded-full text-sm font-bold bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-lg border border-indigo-300/50 backdrop-blur-sm\",\n              children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                className: \"w-4 h-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 19\n              }, this), quiz.subject]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 17\n            }, this), quiz.difficulty && /*#__PURE__*/_jsxDEV(motion.span, {\n              whileHover: {\n                scale: 1.05\n              },\n              className: `inline-flex items-center px-3 py-1 rounded-full text-xs font-bold shadow-md ${getDifficultyColor(quiz.difficulty)}`,\n              children: quiz.difficulty\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this), quiz.attempts > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(TbUsers, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium\",\n              children: [quiz.attempts, \" attempts\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), showResults && userResult && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 10\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          className: \"bg-gradient-to-br from-emerald-50 via-green-50 to-emerald-100/50 border-2 border-emerald-200/70 rounded-2xl p-5 mb-6 relative overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 opacity-10\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute top-0 right-0 w-20 h-20 bg-emerald-500 rounded-full -translate-y-10 translate-x-10\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute bottom-0 left-0 w-16 h-16 bg-green-500 rounded-full translate-y-8 -translate-x-8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative z-10\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center shadow-lg\",\n                  children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n                    className: \"w-5 h-5 text-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 229,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 228,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-bold text-emerald-800\",\n                    children: \"Your Best Score\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 232,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-emerald-600 font-medium\",\n                    children: [userResult.correctAnswers, \"/\", userResult.totalQuestions, \" correct answers\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 233,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `text-3xl font-black ${getScoreColor(userResult.percentage)} drop-shadow-sm`,\n                children: [userResult.percentage, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full bg-emerald-200/50 rounded-full h-3 mb-3 overflow-hidden\",\n              children: /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  width: 0\n                },\n                animate: {\n                  width: `${userResult.percentage}%`\n                },\n                transition: {\n                  duration: 1,\n                  ease: \"easeOut\"\n                },\n                className: \"h-full bg-gradient-to-r from-emerald-500 to-green-500 rounded-full shadow-sm\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between text-xs text-emerald-700\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-semibold\",\n                children: [\"Completed \", new Date(userResult.completedAt).toLocaleDateString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `px-2 py-1 rounded-full font-bold ${userResult.verdict === 'Pass' ? 'bg-emerald-200 text-emerald-800' : 'bg-red-200 text-red-800'}`,\n                children: userResult.verdict\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-6 pb-6 bg-gradient-to-br from-gray-50 to-gray-100/50 border-t border-gray-200/50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex space-x-3 pt-6\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"primary\",\n            size: \"md\",\n            className: \"flex-1 bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-700 hover:from-blue-700 hover:via-blue-800 hover:to-indigo-800 border-0 shadow-lg hover:shadow-xl transform hover:scale-105 hover:-translate-y-1 transition-all duration-300 font-semibold text-white relative overflow-hidden group\",\n            onClick: onStart,\n            icon: /*#__PURE__*/_jsxDEV(TbPlayerPlay, {\n              className: \"group-hover:scale-110 transition-transform duration-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 21\n            }, this),\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"relative z-10\",\n              children: showResults && userResult ? 'Retake Quiz' : 'Start Quiz'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 13\n          }, this), showResults && onView && /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"secondary\",\n            size: \"md\",\n            className: \"bg-white border-2 border-blue-200 text-blue-600 hover:bg-blue-50 hover:border-blue-400 hover:text-blue-700 transform hover:scale-105 hover:-translate-y-1 transition-all duration-300 shadow-sm hover:shadow-md\",\n            onClick: onView,\n            icon: /*#__PURE__*/_jsxDEV(TbTrophy, {\n              className: \"hover:scale-110 transition-transform duration-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 23\n            }, this),\n            children: \"View Results\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 9\n      }, this), quiz.progress && quiz.progress > 0 && quiz.progress < 100 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-6 pb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between text-xs text-gray-600 mb-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Progress\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [quiz.progress, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"progress-bar\",\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              width: 0\n            },\n            animate: {\n              width: `${quiz.progress}%`\n            },\n            transition: {\n              duration: 0.5\n            },\n            className: \"progress-fill\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"absolute inset-0 bg-gradient-to-r from-primary-600/5 to-blue-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none\",\n        whileHover: {\n          opacity: 1\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 5\n  }, this);\n};\n_c = QuizCard;\nexport const QuizGrid = ({\n  quizzes,\n  onQuizStart,\n  onQuizView,\n  showResults = false,\n  userResults = {},\n  className = ''\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `quiz-grid-container ${className}`,\n    children: quizzes.map((quiz, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        duration: 0.3,\n        delay: Math.min(index * 0.1, 0.8)\n      },\n      className: \"h-full\",\n      children: /*#__PURE__*/_jsxDEV(QuizCard, {\n        quiz: quiz,\n        onStart: () => onQuizStart(quiz),\n        onView: onQuizView ? () => onQuizView(quiz) : undefined,\n        showResults: showResults,\n        userResult: userResults[quiz._id],\n        className: \"h-full\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 336,\n        columnNumber: 11\n      }, this)\n    }, quiz._id || index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 329,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 327,\n    columnNumber: 5\n  }, this);\n};\n_c2 = QuizGrid;\nexport default QuizCard;\nvar _c, _c2;\n$RefreshReg$(_c, \"QuizCard\");\n$RefreshReg$(_c2, \"QuizGrid\");", "map": {"version": 3, "names": ["React", "motion", "TbClock", "TbQuestionMark", "TbUsers", "TbTrophy", "TbPlayerPlay", "TbStar", "TbTarget", "TbBrain", "TbChevronRight", "Card", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "QuizCard", "quiz", "onStart", "onView", "showResults", "userResult", "className", "props", "_quiz$questions", "_quiz$questions2", "getDifficultyColor", "difficulty", "toLowerCase", "getScoreColor", "percentage", "div", "initial", "opacity", "y", "animate", "whileHover", "scale", "transition", "duration", "children", "interactive", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "class", "subject", "questions", "length", "passingMarks", "name", "description", "verdict", "span", "attempts", "correctAnswers", "totalQuestions", "width", "ease", "Date", "completedAt", "toLocaleDateString", "size", "onClick", "icon", "progress", "_c", "QuizGrid", "quizzes", "onQuizStart", "onQuizView", "userResults", "map", "index", "delay", "Math", "min", "undefined", "_id", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/modern/QuizCard.js"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport { Tb<PERSON><PERSON>, TbQuestionMark, TbUsers, TbTrophy, TbPlayerPlay, TbStar, TbTarget, TbBrain, TbChevronRight } from 'react-icons/tb';\nimport { <PERSON>, Button } from './index';\n\nconst QuizCard = ({\n  quiz,\n  onStart,\n  onView,\n  showResults = false,\n  userResult = null,\n  className = '',\n  ...props\n}) => {\n  const getDifficultyColor = (difficulty) => {\n    switch (difficulty?.toLowerCase()) {\n      case 'easy':\n        return 'bg-green-100 text-green-800';\n      case 'medium':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'hard':\n        return 'bg-red-100 text-red-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getScoreColor = (percentage) => {\n    if (percentage >= 80) return 'text-green-600';\n    if (percentage >= 60) return 'text-yellow-600';\n    return 'text-red-600';\n  };\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      whileHover={{ y: -8, scale: 1.02 }}\n      transition={{ duration: 0.3 }}\n      className={`quiz-card-modern ${className}`}\n    >\n      <Card\n        interactive\n        variant=\"default\"\n        className=\"quiz-card overflow-hidden h-full border-0 shadow-lg hover:shadow-xl transition-all duration-300\"\n        {...props}\n      >\n        {/* Enhanced Header with Dynamic Gradient */}\n        <div className=\"relative overflow-hidden\">\n          <div className=\"bg-gradient-to-br from-blue-600 via-indigo-700 to-purple-800 p-6 text-white relative\">\n            {/* Animated Background Elements */}\n            <div className=\"absolute inset-0 opacity-20\">\n              <div className=\"absolute top-0 right-0 w-40 h-40 bg-white rounded-full -translate-y-20 translate-x-20 animate-pulse\"></div>\n              <div className=\"absolute bottom-0 left-0 w-32 h-32 bg-white rounded-full translate-y-16 -translate-x-16 animate-pulse delay-700\"></div>\n              <div className=\"absolute top-1/2 left-1/2 w-20 h-20 bg-white rounded-full -translate-x-10 -translate-y-10 animate-pulse delay-300\"></div>\n            </div>\n\n            {/* Floating Particles */}\n            <div className=\"absolute inset-0\">\n              <div className=\"absolute top-4 right-8 w-2 h-2 bg-white rounded-full animate-bounce delay-100\"></div>\n              <div className=\"absolute top-12 right-16 w-1 h-1 bg-white rounded-full animate-bounce delay-500\"></div>\n              <div className=\"absolute bottom-8 left-12 w-1.5 h-1.5 bg-white rounded-full animate-bounce delay-300\"></div>\n            </div>\n\n            {/* Shimmer Effect */}\n            <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 animate-shimmer\"></div>\n\n            <div className=\"relative z-10\">\n              <div className=\"flex items-start justify-between mb-4\">\n                <div className=\"flex-1\">\n                  <div className=\"flex items-center space-x-3 mb-4\">\n                    <div className=\"w-14 h-14 bg-white/20 backdrop-blur-md rounded-2xl flex items-center justify-center border border-white/30 shadow-xl group-hover:scale-110 transition-transform duration-300\">\n                      <TbBrain className=\"w-7 h-7 text-white\" />\n                    </div>\n                    <div>\n                      <div className=\"flex items-center space-x-2 mb-1\">\n                        <span className=\"text-blue-100 text-xs font-bold uppercase tracking-wider bg-white/20 px-2 py-1 rounded-full\">\n                          Class {quiz.class || 'N/A'}\n                        </span>\n                        {quiz.subject && (\n                          <span className=\"text-purple-100 text-xs font-bold uppercase tracking-wider bg-purple-500/30 px-2 py-1 rounded-full\">\n                            {quiz.subject}\n                          </span>\n                        )}\n                      </div>\n                      <div className=\"text-white text-sm font-bold flex items-center space-x-3\">\n                        <span className=\"flex items-center space-x-1\">\n                          <TbQuestionMark className=\"w-4 h-4\" />\n                          <span>{quiz.questions?.length || 0}</span>\n                        </span>\n                        <span className=\"flex items-center space-x-1\">\n                          <TbClock className=\"w-4 h-4\" />\n                          <span>{quiz.duration || 30}m</span>\n                        </span>\n                        <span className=\"flex items-center space-x-1\">\n                          <TbTarget className=\"w-4 h-4\" />\n                          <span>{quiz.passingMarks || 70}%</span>\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n                  <h3 className=\"text-xl font-bold mb-3 line-clamp-2 text-white drop-shadow-lg leading-tight\">\n                    {quiz.name}\n                  </h3>\n                  <p className=\"text-blue-100 text-sm line-clamp-2 opacity-90 font-medium leading-relaxed\">\n                    {quiz.description || 'Test your knowledge with this comprehensive quiz and track your progress'}\n                  </p>\n                </div>\n                {showResults && userResult && (\n                  <motion.div\n                    initial={{ scale: 0 }}\n                    animate={{ scale: 1 }}\n                    className={`px-3 py-2 rounded-xl text-xs font-bold shadow-xl border backdrop-blur-sm ml-4 ${\n                      userResult.verdict === 'Pass'\n                        ? 'bg-emerald-500/90 text-white border-emerald-400/50'\n                        : 'bg-red-500/90 text-white border-red-400/50'\n                    }`}\n                  >\n                    <TbTrophy className=\"w-4 h-4 inline mr-1\" />\n                    {userResult.verdict}\n                  </motion.div>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Enhanced Stats Section */}\n        <div className=\"p-6 pb-4 bg-gradient-to-br from-gray-50/50 to-white relative\">\n          {/* Background Pattern */}\n          <div className=\"absolute inset-0 opacity-5\">\n            <div className=\"absolute top-0 left-0 w-full h-full bg-gradient-to-br from-blue-500 to-purple-600\"></div>\n          </div>\n\n          <div className=\"relative z-10\">\n            {/* Quick Stats Row */}\n            <div className=\"grid grid-cols-3 gap-3 mb-6\">\n              <motion.div\n                whileHover={{ scale: 1.05, y: -2 }}\n                className=\"bg-gradient-to-br from-blue-50 via-blue-100/80 to-blue-200/60 rounded-2xl p-4 text-center border border-blue-200/70 hover:border-blue-400/70 transition-all duration-300 hover:shadow-xl group relative overflow-hidden\"\n              >\n                <div className=\"absolute inset-0 bg-gradient-to-br from-blue-500/5 to-blue-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n                <div className=\"relative z-10\">\n                  <div className=\"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300 shadow-lg\">\n                    <TbQuestionMark className=\"w-5 h-5 text-white\" />\n                  </div>\n                  <div className=\"text-2xl font-bold text-blue-700 mb-1\">{quiz.questions?.length || 0}</div>\n                  <div className=\"text-xs text-blue-600 font-semibold uppercase tracking-wide\">Questions</div>\n                </div>\n              </motion.div>\n\n              <motion.div\n                whileHover={{ scale: 1.05, y: -2 }}\n                className=\"bg-gradient-to-br from-emerald-50 via-emerald-100/80 to-emerald-200/60 rounded-2xl p-4 text-center border border-emerald-200/70 hover:border-emerald-400/70 transition-all duration-300 hover:shadow-xl group relative overflow-hidden\"\n              >\n                <div className=\"absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-emerald-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n                <div className=\"relative z-10\">\n                  <div className=\"w-10 h-10 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300 shadow-lg\">\n                    <TbClock className=\"w-5 h-5 text-white\" />\n                  </div>\n                  <div className=\"text-2xl font-bold text-emerald-700 mb-1\">{quiz.duration || 30}</div>\n                  <div className=\"text-xs text-emerald-600 font-semibold uppercase tracking-wide\">Minutes</div>\n                </div>\n              </motion.div>\n\n              <motion.div\n                whileHover={{ scale: 1.05, y: -2 }}\n                className=\"bg-gradient-to-br from-purple-50 via-purple-100/80 to-purple-200/60 rounded-2xl p-4 text-center border border-purple-200/70 hover:border-purple-400/70 transition-all duration-300 hover:shadow-xl group relative overflow-hidden\"\n              >\n                <div className=\"absolute inset-0 bg-gradient-to-br from-purple-500/5 to-purple-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n                <div className=\"relative z-10\">\n                  <div className=\"w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300 shadow-lg\">\n                    <TbStar className=\"w-5 h-5 text-white\" />\n                  </div>\n                  <div className=\"text-2xl font-bold text-purple-700 mb-1\">{quiz.passingMarks || 70}</div>\n                  <div className=\"text-xs text-purple-600 font-semibold uppercase tracking-wide\">Pass %</div>\n                </div>\n              </motion.div>\n            </div>\n          </div>\n\n          {/* Enhanced Subject & Difficulty Display */}\n          <div className=\"flex items-center justify-between mb-6\">\n            <div className=\"flex items-center space-x-3\">\n              {quiz.subject && (\n                <motion.span\n                  whileHover={{ scale: 1.05 }}\n                  className=\"inline-flex items-center px-4 py-2 rounded-full text-sm font-bold bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-lg border border-indigo-300/50 backdrop-blur-sm\"\n                >\n                  <TbBrain className=\"w-4 h-4 mr-2\" />\n                  {quiz.subject}\n                </motion.span>\n              )}\n              {quiz.difficulty && (\n                <motion.span\n                  whileHover={{ scale: 1.05 }}\n                  className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-bold shadow-md ${getDifficultyColor(quiz.difficulty)}`}\n                >\n                  {quiz.difficulty}\n                </motion.span>\n              )}\n            </div>\n\n            {quiz.attempts > 0 && (\n              <div className=\"flex items-center space-x-2 text-gray-500\">\n                <TbUsers className=\"w-4 h-4\" />\n                <span className=\"text-sm font-medium\">{quiz.attempts} attempts</span>\n              </div>\n            )}\n          </div>\n\n          {/* Enhanced User Results Section */}\n          {showResults && userResult && (\n            <motion.div\n              initial={{ opacity: 0, y: 10 }}\n              animate={{ opacity: 1, y: 0 }}\n              className=\"bg-gradient-to-br from-emerald-50 via-green-50 to-emerald-100/50 border-2 border-emerald-200/70 rounded-2xl p-5 mb-6 relative overflow-hidden\"\n            >\n              {/* Background Pattern */}\n              <div className=\"absolute inset-0 opacity-10\">\n                <div className=\"absolute top-0 right-0 w-20 h-20 bg-emerald-500 rounded-full -translate-y-10 translate-x-10\"></div>\n                <div className=\"absolute bottom-0 left-0 w-16 h-16 bg-green-500 rounded-full translate-y-8 -translate-x-8\"></div>\n              </div>\n\n              <div className=\"relative z-10\">\n                <div className=\"flex items-center justify-between mb-3\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-10 h-10 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center shadow-lg\">\n                      <TbTrophy className=\"w-5 h-5 text-white\" />\n                    </div>\n                    <div>\n                      <span className=\"text-sm font-bold text-emerald-800\">Your Best Score</span>\n                      <div className=\"text-xs text-emerald-600 font-medium\">\n                        {userResult.correctAnswers}/{userResult.totalQuestions} correct answers\n                      </div>\n                    </div>\n                  </div>\n                  <div className={`text-3xl font-black ${getScoreColor(userResult.percentage)} drop-shadow-sm`}>\n                    {userResult.percentage}%\n                  </div>\n                </div>\n\n                {/* Progress Bar */}\n                <div className=\"w-full bg-emerald-200/50 rounded-full h-3 mb-3 overflow-hidden\">\n                  <motion.div\n                    initial={{ width: 0 }}\n                    animate={{ width: `${userResult.percentage}%` }}\n                    transition={{ duration: 1, ease: \"easeOut\" }}\n                    className=\"h-full bg-gradient-to-r from-emerald-500 to-green-500 rounded-full shadow-sm\"\n                  />\n                </div>\n\n                <div className=\"flex items-center justify-between text-xs text-emerald-700\">\n                  <span className=\"font-semibold\">\n                    Completed {new Date(userResult.completedAt).toLocaleDateString()}\n                  </span>\n                  <span className={`px-2 py-1 rounded-full font-bold ${\n                    userResult.verdict === 'Pass'\n                      ? 'bg-emerald-200 text-emerald-800'\n                      : 'bg-red-200 text-red-800'\n                  }`}>\n                    {userResult.verdict}\n                  </span>\n                </div>\n              </div>\n            </motion.div>\n          )}\n        </div>\n\n        <div className=\"px-6 pb-6 bg-gradient-to-br from-gray-50 to-gray-100/50 border-t border-gray-200/50\">\n          <div className=\"flex space-x-3 pt-6\">\n            <Button\n              variant=\"primary\"\n              size=\"md\"\n              className=\"flex-1 bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-700 hover:from-blue-700 hover:via-blue-800 hover:to-indigo-800 border-0 shadow-lg hover:shadow-xl transform hover:scale-105 hover:-translate-y-1 transition-all duration-300 font-semibold text-white relative overflow-hidden group\"\n              onClick={onStart}\n              icon={<TbPlayerPlay className=\"group-hover:scale-110 transition-transform duration-300\" />}\n            >\n              <span className=\"relative z-10\">\n                {showResults && userResult ? 'Retake Quiz' : 'Start Quiz'}\n              </span>\n              <div className=\"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n            </Button>\n\n            {showResults && onView && (\n              <Button\n                variant=\"secondary\"\n                size=\"md\"\n                className=\"bg-white border-2 border-blue-200 text-blue-600 hover:bg-blue-50 hover:border-blue-400 hover:text-blue-700 transform hover:scale-105 hover:-translate-y-1 transition-all duration-300 shadow-sm hover:shadow-md\"\n                onClick={onView}\n                icon={<TbTrophy className=\"hover:scale-110 transition-transform duration-300\" />}\n              >\n                View Results\n              </Button>\n            )}\n          </div>\n        </div>\n\n        {quiz.progress && quiz.progress > 0 && quiz.progress < 100 && (\n          <div className=\"px-6 pb-4\">\n            <div className=\"flex items-center justify-between text-xs text-gray-600 mb-2\">\n              <span>Progress</span>\n              <span>{quiz.progress}%</span>\n            </div>\n            <div className=\"progress-bar\">\n              <motion.div\n                initial={{ width: 0 }}\n                animate={{ width: `${quiz.progress}%` }}\n                transition={{ duration: 0.5 }}\n                className=\"progress-fill\"\n              />\n            </div>\n          </div>\n        )}\n\n        <motion.div\n          className=\"absolute inset-0 bg-gradient-to-r from-primary-600/5 to-blue-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none\"\n          whileHover={{ opacity: 1 }}\n        />\n      </Card>\n    </motion.div>\n  );\n};\n\nexport const QuizGrid = ({ quizzes, onQuizStart, onQuizView, showResults = false, userResults = {}, className = '' }) => {\n  return (\n    <div className={`quiz-grid-container ${className}`}>\n      {quizzes.map((quiz, index) => (\n        <motion.div\n          key={quiz._id || index}\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.3, delay: Math.min(index * 0.1, 0.8) }}\n          className=\"h-full\"\n        >\n          <QuizCard\n            quiz={quiz}\n            onStart={() => onQuizStart(quiz)}\n            onView={onQuizView ? () => onQuizView(quiz) : undefined}\n            showResults={showResults}\n            userResult={userResults[quiz._id]}\n            className=\"h-full\"\n          />\n        </motion.div>\n      ))}\n    </div>\n  );\n};\n\nexport default QuizCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,EAAEC,cAAc,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,cAAc,QAAQ,gBAAgB;AACpI,SAASC,IAAI,EAAEC,MAAM,QAAQ,SAAS;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,QAAQ,GAAGA,CAAC;EAChBC,IAAI;EACJC,OAAO;EACPC,MAAM;EACNC,WAAW,GAAG,KAAK;EACnBC,UAAU,GAAG,IAAI;EACjBC,SAAS,GAAG,EAAE;EACd,GAAGC;AACL,CAAC,KAAK;EAAA,IAAAC,eAAA,EAAAC,gBAAA;EACJ,MAAMC,kBAAkB,GAAIC,UAAU,IAAK;IACzC,QAAQA,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEC,WAAW,CAAC,CAAC;MAC/B,KAAK,MAAM;QACT,OAAO,6BAA6B;MACtC,KAAK,QAAQ;QACX,OAAO,+BAA+B;MACxC,KAAK,MAAM;QACT,OAAO,yBAAyB;MAClC;QACE,OAAO,2BAA2B;IACtC;EACF,CAAC;EAED,MAAMC,aAAa,GAAIC,UAAU,IAAK;IACpC,IAAIA,UAAU,IAAI,EAAE,EAAE,OAAO,gBAAgB;IAC7C,IAAIA,UAAU,IAAI,EAAE,EAAE,OAAO,iBAAiB;IAC9C,OAAO,cAAc;EACvB,CAAC;EAED,oBACEf,OAAA,CAACb,MAAM,CAAC6B,GAAG;IACTC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAG,CAAE;IAC/BC,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAE;IAC9BE,UAAU,EAAE;MAAEF,CAAC,EAAE,CAAC,CAAC;MAAEG,KAAK,EAAE;IAAK,CAAE;IACnCC,UAAU,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE;IAC9BjB,SAAS,EAAG,oBAAmBA,SAAU,EAAE;IAAAkB,QAAA,eAE3CzB,OAAA,CAACH,IAAI;MACH6B,WAAW;MACXC,OAAO,EAAC,SAAS;MACjBpB,SAAS,EAAC,iGAAiG;MAAA,GACvGC,KAAK;MAAAiB,QAAA,gBAGTzB,OAAA;QAAKO,SAAS,EAAC,0BAA0B;QAAAkB,QAAA,eACvCzB,OAAA;UAAKO,SAAS,EAAC,sFAAsF;UAAAkB,QAAA,gBAEnGzB,OAAA;YAAKO,SAAS,EAAC,6BAA6B;YAAAkB,QAAA,gBAC1CzB,OAAA;cAAKO,SAAS,EAAC;YAAqG;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3H/B,OAAA;cAAKO,SAAS,EAAC;YAAiH;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvI/B,OAAA;cAAKO,SAAS,EAAC;YAAmH;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtI,CAAC,eAGN/B,OAAA;YAAKO,SAAS,EAAC,kBAAkB;YAAAkB,QAAA,gBAC/BzB,OAAA;cAAKO,SAAS,EAAC;YAA+E;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrG/B,OAAA;cAAKO,SAAS,EAAC;YAAiF;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvG/B,OAAA;cAAKO,SAAS,EAAC;YAAsF;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzG,CAAC,eAGN/B,OAAA;YAAKO,SAAS,EAAC;UAA2G;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAEjI/B,OAAA;YAAKO,SAAS,EAAC,eAAe;YAAAkB,QAAA,eAC5BzB,OAAA;cAAKO,SAAS,EAAC,uCAAuC;cAAAkB,QAAA,gBACpDzB,OAAA;gBAAKO,SAAS,EAAC,QAAQ;gBAAAkB,QAAA,gBACrBzB,OAAA;kBAAKO,SAAS,EAAC,kCAAkC;kBAAAkB,QAAA,gBAC/CzB,OAAA;oBAAKO,SAAS,EAAC,8KAA8K;oBAAAkB,QAAA,eAC3LzB,OAAA,CAACL,OAAO;sBAACY,SAAS,EAAC;oBAAoB;sBAAAqB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC,eACN/B,OAAA;oBAAAyB,QAAA,gBACEzB,OAAA;sBAAKO,SAAS,EAAC,kCAAkC;sBAAAkB,QAAA,gBAC/CzB,OAAA;wBAAMO,SAAS,EAAC,6FAA6F;wBAAAkB,QAAA,GAAC,QACtG,EAACvB,IAAI,CAAC8B,KAAK,IAAI,KAAK;sBAAA;wBAAAJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtB,CAAC,EACN7B,IAAI,CAAC+B,OAAO,iBACXjC,OAAA;wBAAMO,SAAS,EAAC,oGAAoG;wBAAAkB,QAAA,EACjHvB,IAAI,CAAC+B;sBAAO;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACT,CACP;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eACN/B,OAAA;sBAAKO,SAAS,EAAC,0DAA0D;sBAAAkB,QAAA,gBACvEzB,OAAA;wBAAMO,SAAS,EAAC,6BAA6B;wBAAAkB,QAAA,gBAC3CzB,OAAA,CAACX,cAAc;0BAACkB,SAAS,EAAC;wBAAS;0BAAAqB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eACtC/B,OAAA;0BAAAyB,QAAA,EAAO,EAAAhB,eAAA,GAAAP,IAAI,CAACgC,SAAS,cAAAzB,eAAA,uBAAdA,eAAA,CAAgB0B,MAAM,KAAI;wBAAC;0BAAAP,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC,eACP/B,OAAA;wBAAMO,SAAS,EAAC,6BAA6B;wBAAAkB,QAAA,gBAC3CzB,OAAA,CAACZ,OAAO;0BAACmB,SAAS,EAAC;wBAAS;0BAAAqB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC/B/B,OAAA;0BAAAyB,QAAA,GAAOvB,IAAI,CAACsB,QAAQ,IAAI,EAAE,EAAC,GAAC;wBAAA;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/B,CAAC,eACP/B,OAAA;wBAAMO,SAAS,EAAC,6BAA6B;wBAAAkB,QAAA,gBAC3CzB,OAAA,CAACN,QAAQ;0BAACa,SAAS,EAAC;wBAAS;0BAAAqB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAChC/B,OAAA;0BAAAyB,QAAA,GAAOvB,IAAI,CAACkC,YAAY,IAAI,EAAE,EAAC,GAAC;wBAAA;0BAAAR,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN/B,OAAA;kBAAIO,SAAS,EAAC,6EAA6E;kBAAAkB,QAAA,EACxFvB,IAAI,CAACmC;gBAAI;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACL/B,OAAA;kBAAGO,SAAS,EAAC,2EAA2E;kBAAAkB,QAAA,EACrFvB,IAAI,CAACoC,WAAW,IAAI;gBAA0E;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,EACL1B,WAAW,IAAIC,UAAU,iBACxBN,OAAA,CAACb,MAAM,CAAC6B,GAAG;gBACTC,OAAO,EAAE;kBAAEK,KAAK,EAAE;gBAAE,CAAE;gBACtBF,OAAO,EAAE;kBAAEE,KAAK,EAAE;gBAAE,CAAE;gBACtBf,SAAS,EAAG,iFACVD,UAAU,CAACiC,OAAO,KAAK,MAAM,GACzB,oDAAoD,GACpD,4CACL,EAAE;gBAAAd,QAAA,gBAEHzB,OAAA,CAACT,QAAQ;kBAACgB,SAAS,EAAC;gBAAqB;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAC3CzB,UAAU,CAACiC,OAAO;cAAA;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN/B,OAAA;QAAKO,SAAS,EAAC,8DAA8D;QAAAkB,QAAA,gBAE3EzB,OAAA;UAAKO,SAAS,EAAC,4BAA4B;UAAAkB,QAAA,eACzCzB,OAAA;YAAKO,SAAS,EAAC;UAAmF;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtG,CAAC,eAEN/B,OAAA;UAAKO,SAAS,EAAC,eAAe;UAAAkB,QAAA,eAE5BzB,OAAA;YAAKO,SAAS,EAAC,6BAA6B;YAAAkB,QAAA,gBAC1CzB,OAAA,CAACb,MAAM,CAAC6B,GAAG;cACTK,UAAU,EAAE;gBAAEC,KAAK,EAAE,IAAI;gBAAEH,CAAC,EAAE,CAAC;cAAE,CAAE;cACnCZ,SAAS,EAAC,yNAAyN;cAAAkB,QAAA,gBAEnOzB,OAAA;gBAAKO,SAAS,EAAC;cAAqI;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3J/B,OAAA;gBAAKO,SAAS,EAAC,eAAe;gBAAAkB,QAAA,gBAC5BzB,OAAA;kBAAKO,SAAS,EAAC,kLAAkL;kBAAAkB,QAAA,eAC/LzB,OAAA,CAACX,cAAc;oBAACkB,SAAS,EAAC;kBAAoB;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC,eACN/B,OAAA;kBAAKO,SAAS,EAAC,uCAAuC;kBAAAkB,QAAA,EAAE,EAAAf,gBAAA,GAAAR,IAAI,CAACgC,SAAS,cAAAxB,gBAAA,uBAAdA,gBAAA,CAAgByB,MAAM,KAAI;gBAAC;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1F/B,OAAA;kBAAKO,SAAS,EAAC,6DAA6D;kBAAAkB,QAAA,EAAC;gBAAS;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eAEb/B,OAAA,CAACb,MAAM,CAAC6B,GAAG;cACTK,UAAU,EAAE;gBAAEC,KAAK,EAAE,IAAI;gBAAEH,CAAC,EAAE,CAAC;cAAE,CAAE;cACnCZ,SAAS,EAAC,wOAAwO;cAAAkB,QAAA,gBAElPzB,OAAA;gBAAKO,SAAS,EAAC;cAA2I;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjK/B,OAAA;gBAAKO,SAAS,EAAC,eAAe;gBAAAkB,QAAA,gBAC5BzB,OAAA;kBAAKO,SAAS,EAAC,wLAAwL;kBAAAkB,QAAA,eACrMzB,OAAA,CAACZ,OAAO;oBAACmB,SAAS,EAAC;kBAAoB;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC,eACN/B,OAAA;kBAAKO,SAAS,EAAC,0CAA0C;kBAAAkB,QAAA,EAAEvB,IAAI,CAACsB,QAAQ,IAAI;gBAAE;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrF/B,OAAA;kBAAKO,SAAS,EAAC,gEAAgE;kBAAAkB,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1F,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eAEb/B,OAAA,CAACb,MAAM,CAAC6B,GAAG;cACTK,UAAU,EAAE;gBAAEC,KAAK,EAAE,IAAI;gBAAEH,CAAC,EAAE,CAAC;cAAE,CAAE;cACnCZ,SAAS,EAAC,mOAAmO;cAAAkB,QAAA,gBAE7OzB,OAAA;gBAAKO,SAAS,EAAC;cAAyI;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/J/B,OAAA;gBAAKO,SAAS,EAAC,eAAe;gBAAAkB,QAAA,gBAC5BzB,OAAA;kBAAKO,SAAS,EAAC,sLAAsL;kBAAAkB,QAAA,eACnMzB,OAAA,CAACP,MAAM;oBAACc,SAAS,EAAC;kBAAoB;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACN/B,OAAA;kBAAKO,SAAS,EAAC,yCAAyC;kBAAAkB,QAAA,EAAEvB,IAAI,CAACkC,YAAY,IAAI;gBAAE;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxF/B,OAAA;kBAAKO,SAAS,EAAC,+DAA+D;kBAAAkB,QAAA,EAAC;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN/B,OAAA;UAAKO,SAAS,EAAC,wCAAwC;UAAAkB,QAAA,gBACrDzB,OAAA;YAAKO,SAAS,EAAC,6BAA6B;YAAAkB,QAAA,GACzCvB,IAAI,CAAC+B,OAAO,iBACXjC,OAAA,CAACb,MAAM,CAACqD,IAAI;cACVnB,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5Bf,SAAS,EAAC,oLAAoL;cAAAkB,QAAA,gBAE9LzB,OAAA,CAACL,OAAO;gBAACY,SAAS,EAAC;cAAc;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EACnC7B,IAAI,CAAC+B,OAAO;YAAA;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CACd,EACA7B,IAAI,CAACU,UAAU,iBACdZ,OAAA,CAACb,MAAM,CAACqD,IAAI;cACVnB,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5Bf,SAAS,EAAG,+EAA8EI,kBAAkB,CAACT,IAAI,CAACU,UAAU,CAAE,EAAE;cAAAa,QAAA,EAE/HvB,IAAI,CAACU;YAAU;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACd;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAEL7B,IAAI,CAACuC,QAAQ,GAAG,CAAC,iBAChBzC,OAAA;YAAKO,SAAS,EAAC,2CAA2C;YAAAkB,QAAA,gBACxDzB,OAAA,CAACV,OAAO;cAACiB,SAAS,EAAC;YAAS;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/B/B,OAAA;cAAMO,SAAS,EAAC,qBAAqB;cAAAkB,QAAA,GAAEvB,IAAI,CAACuC,QAAQ,EAAC,WAAS;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGL1B,WAAW,IAAIC,UAAU,iBACxBN,OAAA,CAACb,MAAM,CAAC6B,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BZ,SAAS,EAAC,+IAA+I;UAAAkB,QAAA,gBAGzJzB,OAAA;YAAKO,SAAS,EAAC,6BAA6B;YAAAkB,QAAA,gBAC1CzB,OAAA;cAAKO,SAAS,EAAC;YAA6F;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnH/B,OAAA;cAAKO,SAAS,EAAC;YAA2F;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9G,CAAC,eAEN/B,OAAA;YAAKO,SAAS,EAAC,eAAe;YAAAkB,QAAA,gBAC5BzB,OAAA;cAAKO,SAAS,EAAC,wCAAwC;cAAAkB,QAAA,gBACrDzB,OAAA;gBAAKO,SAAS,EAAC,6BAA6B;gBAAAkB,QAAA,gBAC1CzB,OAAA;kBAAKO,SAAS,EAAC,iHAAiH;kBAAAkB,QAAA,eAC9HzB,OAAA,CAACT,QAAQ;oBAACgB,SAAS,EAAC;kBAAoB;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,eACN/B,OAAA;kBAAAyB,QAAA,gBACEzB,OAAA;oBAAMO,SAAS,EAAC,oCAAoC;oBAAAkB,QAAA,EAAC;kBAAe;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC3E/B,OAAA;oBAAKO,SAAS,EAAC,sCAAsC;oBAAAkB,QAAA,GAClDnB,UAAU,CAACoC,cAAc,EAAC,GAAC,EAACpC,UAAU,CAACqC,cAAc,EAAC,kBACzD;kBAAA;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN/B,OAAA;gBAAKO,SAAS,EAAG,uBAAsBO,aAAa,CAACR,UAAU,CAACS,UAAU,CAAE,iBAAiB;gBAAAU,QAAA,GAC1FnB,UAAU,CAACS,UAAU,EAAC,GACzB;cAAA;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN/B,OAAA;cAAKO,SAAS,EAAC,gEAAgE;cAAAkB,QAAA,eAC7EzB,OAAA,CAACb,MAAM,CAAC6B,GAAG;gBACTC,OAAO,EAAE;kBAAE2B,KAAK,EAAE;gBAAE,CAAE;gBACtBxB,OAAO,EAAE;kBAAEwB,KAAK,EAAG,GAAEtC,UAAU,CAACS,UAAW;gBAAG,CAAE;gBAChDQ,UAAU,EAAE;kBAAEC,QAAQ,EAAE,CAAC;kBAAEqB,IAAI,EAAE;gBAAU,CAAE;gBAC7CtC,SAAS,EAAC;cAA8E;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN/B,OAAA;cAAKO,SAAS,EAAC,4DAA4D;cAAAkB,QAAA,gBACzEzB,OAAA;gBAAMO,SAAS,EAAC,eAAe;gBAAAkB,QAAA,GAAC,YACpB,EAAC,IAAIqB,IAAI,CAACxC,UAAU,CAACyC,WAAW,CAAC,CAACC,kBAAkB,CAAC,CAAC;cAAA;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC,eACP/B,OAAA;gBAAMO,SAAS,EAAG,oCAChBD,UAAU,CAACiC,OAAO,KAAK,MAAM,GACzB,iCAAiC,GACjC,yBACL,EAAE;gBAAAd,QAAA,EACAnB,UAAU,CAACiC;cAAO;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN/B,OAAA;QAAKO,SAAS,EAAC,qFAAqF;QAAAkB,QAAA,eAClGzB,OAAA;UAAKO,SAAS,EAAC,qBAAqB;UAAAkB,QAAA,gBAClCzB,OAAA,CAACF,MAAM;YACL6B,OAAO,EAAC,SAAS;YACjBsB,IAAI,EAAC,IAAI;YACT1C,SAAS,EAAC,mSAAmS;YAC7S2C,OAAO,EAAE/C,OAAQ;YACjBgD,IAAI,eAAEnD,OAAA,CAACR,YAAY;cAACe,SAAS,EAAC;YAAyD;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAN,QAAA,gBAE3FzB,OAAA;cAAMO,SAAS,EAAC,eAAe;cAAAkB,QAAA,EAC5BpB,WAAW,IAAIC,UAAU,GAAG,aAAa,GAAG;YAAY;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,eACP/B,OAAA;cAAKO,SAAS,EAAC;YAAkI;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClJ,CAAC,EAER1B,WAAW,IAAID,MAAM,iBACpBJ,OAAA,CAACF,MAAM;YACL6B,OAAO,EAAC,WAAW;YACnBsB,IAAI,EAAC,IAAI;YACT1C,SAAS,EAAC,iNAAiN;YAC3N2C,OAAO,EAAE9C,MAAO;YAChB+C,IAAI,eAAEnD,OAAA,CAACT,QAAQ;cAACgB,SAAS,EAAC;YAAmD;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAN,QAAA,EAClF;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAEL7B,IAAI,CAACkD,QAAQ,IAAIlD,IAAI,CAACkD,QAAQ,GAAG,CAAC,IAAIlD,IAAI,CAACkD,QAAQ,GAAG,GAAG,iBACxDpD,OAAA;QAAKO,SAAS,EAAC,WAAW;QAAAkB,QAAA,gBACxBzB,OAAA;UAAKO,SAAS,EAAC,8DAA8D;UAAAkB,QAAA,gBAC3EzB,OAAA;YAAAyB,QAAA,EAAM;UAAQ;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrB/B,OAAA;YAAAyB,QAAA,GAAOvB,IAAI,CAACkD,QAAQ,EAAC,GAAC;UAAA;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eACN/B,OAAA;UAAKO,SAAS,EAAC,cAAc;UAAAkB,QAAA,eAC3BzB,OAAA,CAACb,MAAM,CAAC6B,GAAG;YACTC,OAAO,EAAE;cAAE2B,KAAK,EAAE;YAAE,CAAE;YACtBxB,OAAO,EAAE;cAAEwB,KAAK,EAAG,GAAE1C,IAAI,CAACkD,QAAS;YAAG,CAAE;YACxC7B,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BjB,SAAS,EAAC;UAAe;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAED/B,OAAA,CAACb,MAAM,CAAC6B,GAAG;QACTT,SAAS,EAAC,0JAA0J;QACpKc,UAAU,EAAE;UAAEH,OAAO,EAAE;QAAE;MAAE;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEjB,CAAC;AAACsB,EAAA,GA7TIpD,QAAQ;AA+Td,OAAO,MAAMqD,QAAQ,GAAGA,CAAC;EAAEC,OAAO;EAAEC,WAAW;EAAEC,UAAU;EAAEpD,WAAW,GAAG,KAAK;EAAEqD,WAAW,GAAG,CAAC,CAAC;EAAEnD,SAAS,GAAG;AAAG,CAAC,KAAK;EACvH,oBACEP,OAAA;IAAKO,SAAS,EAAG,uBAAsBA,SAAU,EAAE;IAAAkB,QAAA,EAChD8B,OAAO,CAACI,GAAG,CAAC,CAACzD,IAAI,EAAE0D,KAAK,kBACvB5D,OAAA,CAACb,MAAM,CAAC6B,GAAG;MAETC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BI,UAAU,EAAE;QAAEC,QAAQ,EAAE,GAAG;QAAEqC,KAAK,EAAEC,IAAI,CAACC,GAAG,CAACH,KAAK,GAAG,GAAG,EAAE,GAAG;MAAE,CAAE;MACjErD,SAAS,EAAC,QAAQ;MAAAkB,QAAA,eAElBzB,OAAA,CAACC,QAAQ;QACPC,IAAI,EAAEA,IAAK;QACXC,OAAO,EAAEA,CAAA,KAAMqD,WAAW,CAACtD,IAAI,CAAE;QACjCE,MAAM,EAAEqD,UAAU,GAAG,MAAMA,UAAU,CAACvD,IAAI,CAAC,GAAG8D,SAAU;QACxD3D,WAAW,EAAEA,WAAY;QACzBC,UAAU,EAAEoD,WAAW,CAACxD,IAAI,CAAC+D,GAAG,CAAE;QAClC1D,SAAS,EAAC;MAAQ;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB;IAAC,GAbG7B,IAAI,CAAC+D,GAAG,IAAIL,KAAK;MAAAhC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAcZ,CACb;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACmC,GAAA,GAvBWZ,QAAQ;AAyBrB,eAAerD,QAAQ;AAAC,IAAAoD,EAAA,EAAAa,GAAA;AAAAC,YAAA,CAAAd,EAAA;AAAAc,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}