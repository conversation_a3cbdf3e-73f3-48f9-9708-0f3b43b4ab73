{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\modern\\\\QuizTimer.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { TbClock, TbAlertTriangle } from 'react-icons/tb';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuizTimer = ({\n  duration,\n  // in seconds\n  onTimeUp,\n  isActive = true,\n  showWarning = true,\n  warningThreshold = 300,\n  // 5 minutes\n  className = ''\n}) => {\n  _s();\n  const [timeRemaining, setTimeRemaining] = useState(duration);\n  const [isWarning, setIsWarning] = useState(false);\n  useEffect(() => {\n    setTimeRemaining(duration);\n  }, [duration]);\n  useEffect(() => {\n    if (!isActive) return;\n    const interval = setInterval(() => {\n      setTimeRemaining(prev => {\n        if (prev <= 1) {\n          onTimeUp === null || onTimeUp === void 0 ? void 0 : onTimeUp();\n          return 0;\n        }\n        const newTime = prev - 1;\n\n        // Check if we should show warning\n        if (showWarning && newTime <= warningThreshold && !isWarning) {\n          setIsWarning(true);\n        }\n        return newTime;\n      });\n    }, 1000);\n    return () => clearInterval(interval);\n  }, [isActive, onTimeUp, showWarning, warningThreshold, isWarning]);\n  const formatTime = seconds => {\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor(seconds % 3600 / 60);\n    const secs = seconds % 60;\n    if (hours > 0) {\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n    }\n    return `${minutes}:${secs.toString().padStart(2, '0')}`;\n  };\n  const getProgressPercentage = () => {\n    return (duration - timeRemaining) / duration * 100;\n  };\n  const getTimerColor = () => {\n    if (timeRemaining <= 60) return 'text-white'; // Last minute\n    if (timeRemaining <= warningThreshold) return 'text-white'; // Warning\n    return 'text-white'; // Normal\n  };\n\n  const getProgressColor = () => {\n    if (timeRemaining <= 60) return 'from-red-500 to-red-600';\n    if (timeRemaining <= warningThreshold) return 'from-yellow-500 to-yellow-600';\n    return 'from-primary-500 to-blue-500';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `${className}`,\n    children: [/*#__PURE__*/_jsxDEV(motion.div, {\n      animate: isWarning ? {\n        scale: [1, 1.05, 1]\n      } : {},\n      transition: {\n        duration: 1,\n        repeat: isWarning ? Infinity : 0\n      },\n      className: `inline-flex items-center space-x-3 px-6 py-3 rounded-xl shadow-lg border-2 ${timeRemaining <= 60 ? 'bg-gradient-to-r from-red-600 to-red-700 border-red-300 text-white' : timeRemaining <= warningThreshold ? 'bg-gradient-to-r from-yellow-500 to-orange-500 border-yellow-300 text-white' : 'bg-gradient-to-r from-blue-600 to-indigo-600 border-blue-300 text-white'}`,\n      style: {\n        textShadow: '1px 1px 2px rgba(0,0,0,0.5)'\n      },\n      children: [timeRemaining <= warningThreshold && /*#__PURE__*/_jsxDEV(motion.div, {\n        animate: {\n          rotate: [0, 10, -10, 0]\n        },\n        transition: {\n          duration: 0.5,\n          repeat: Infinity\n        },\n        children: /*#__PURE__*/_jsxDEV(TbAlertTriangle, {\n          className: \"w-5 h-5 text-white drop-shadow-md\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TbClock, {\n        className: \"w-5 h-5 text-white drop-shadow-md\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-xs font-semibold opacity-90 mb-1\",\n          children: \"TIME LEFT\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"font-mono font-black text-lg text-white\",\n          style: {\n            textShadow: '2px 2px 4px rgba(0,0,0,0.7)'\n          },\n          children: formatTime(timeRemaining)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-3 w-full bg-gray-300 rounded-full h-2 overflow-hidden shadow-inner\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          width: 0\n        },\n        animate: {\n          width: `${getProgressPercentage()}%`\n        },\n        transition: {\n          duration: 0.5\n        },\n        className: `h-full bg-gradient-to-r ${getProgressColor()} rounded-full shadow-sm`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this), isWarning && timeRemaining > 60 && /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: -10\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      className: \"mt-2 text-xs text-yellow-700 font-medium\",\n      children: [\"\\u26A0\\uFE0F \", Math.floor(timeRemaining / 60), \" minutes remaining\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 9\n    }, this), timeRemaining <= 60 && timeRemaining > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: -10\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      className: \"mt-2 text-xs text-red-700 font-medium\",\n      children: \"\\uD83D\\uDEA8 Less than 1 minute left!\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 9\n    }, this), timeRemaining === 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        scale: 0.8\n      },\n      animate: {\n        opacity: 1,\n        scale: 1\n      },\n      className: \"mt-2 text-xs text-red-700 font-bold\",\n      children: \"\\u23F0 Time's up!\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 72,\n    columnNumber: 5\n  }, this);\n};\n\n// Full-screen timer overlay for critical moments\n_s(QuizTimer, \"0cFoYewCebonPy1Gpf1Z84tglI0=\");\n_c = QuizTimer;\nexport const QuizTimerOverlay = ({\n  timeRemaining,\n  onClose\n}) => {\n  if (timeRemaining > 10) return null;\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    initial: {\n      opacity: 0\n    },\n    animate: {\n      opacity: 1\n    },\n    exit: {\n      opacity: 0\n    },\n    className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center\",\n    children: /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        scale: 0.8,\n        opacity: 0\n      },\n      animate: {\n        scale: 1,\n        opacity: 1\n      },\n      className: \"bg-white rounded-2xl p-8 text-center shadow-2xl max-w-sm mx-4\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        animate: {\n          scale: [1, 1.2, 1]\n        },\n        transition: {\n          duration: 1,\n          repeat: Infinity\n        },\n        className: \"text-6xl mb-4\",\n        children: \"\\u23F0\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-2xl font-bold text-red-600 mb-2\",\n        children: \"Time Almost Up!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        animate: {\n          scale: [1, 1.1, 1]\n        },\n        transition: {\n          duration: 0.5,\n          repeat: Infinity\n        },\n        className: \"text-4xl font-mono font-bold text-red-600 mb-4\",\n        children: timeRemaining\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 mb-4\",\n        children: \"Submit your answers now!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onClose,\n        className: \"px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors\",\n        children: \"Continue Quiz\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 156,\n    columnNumber: 5\n  }, this);\n};\n_c2 = QuizTimerOverlay;\nexport default QuizTimer;\nvar _c, _c2;\n$RefreshReg$(_c, \"QuizTimer\");\n$RefreshReg$(_c2, \"QuizTimerOverlay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "TbClock", "TbAlertTriangle", "jsxDEV", "_jsxDEV", "QuizTimer", "duration", "onTimeUp", "isActive", "showWarning", "warningThreshold", "className", "_s", "timeRemaining", "setTimeRemaining", "isWarning", "setIsWarning", "interval", "setInterval", "prev", "newTime", "clearInterval", "formatTime", "seconds", "hours", "Math", "floor", "minutes", "secs", "toString", "padStart", "getProgressPercentage", "getTimerColor", "getProgressColor", "children", "div", "animate", "scale", "transition", "repeat", "Infinity", "style", "textShadow", "rotate", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "initial", "width", "opacity", "y", "_c", "QuizTimer<PERSON><PERSON>lay", "onClose", "exit", "onClick", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/modern/QuizTimer.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { TbClock, TbAlertTriangle } from 'react-icons/tb';\n\nconst QuizTimer = ({\n  duration, // in seconds\n  onTimeUp,\n  isActive = true,\n  showWarning = true,\n  warningThreshold = 300, // 5 minutes\n  className = '',\n}) => {\n  const [timeRemaining, setTimeRemaining] = useState(duration);\n  const [isWarning, setIsWarning] = useState(false);\n\n  useEffect(() => {\n    setTimeRemaining(duration);\n  }, [duration]);\n\n  useEffect(() => {\n    if (!isActive) return;\n\n    const interval = setInterval(() => {\n      setTimeRemaining((prev) => {\n        if (prev <= 1) {\n          onTimeUp?.();\n          return 0;\n        }\n        \n        const newTime = prev - 1;\n        \n        // Check if we should show warning\n        if (showWarning && newTime <= warningThreshold && !isWarning) {\n          setIsWarning(true);\n        }\n        \n        return newTime;\n      });\n    }, 1000);\n\n    return () => clearInterval(interval);\n  }, [isActive, onTimeUp, showWarning, warningThreshold, isWarning]);\n\n  const formatTime = (seconds) => {\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor((seconds % 3600) / 60);\n    const secs = seconds % 60;\n\n    if (hours > 0) {\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n    }\n    return `${minutes}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  const getProgressPercentage = () => {\n    return ((duration - timeRemaining) / duration) * 100;\n  };\n\n  const getTimerColor = () => {\n    if (timeRemaining <= 60) return 'text-white'; // Last minute\n    if (timeRemaining <= warningThreshold) return 'text-white'; // Warning\n    return 'text-white'; // Normal\n  };\n\n  const getProgressColor = () => {\n    if (timeRemaining <= 60) return 'from-red-500 to-red-600';\n    if (timeRemaining <= warningThreshold) return 'from-yellow-500 to-yellow-600';\n    return 'from-primary-500 to-blue-500';\n  };\n\n  return (\n    <div className={`${className}`}>\n      {/* Compact Timer Display */}\n      <motion.div\n        animate={isWarning ? { scale: [1, 1.05, 1] } : {}}\n        transition={{ duration: 1, repeat: isWarning ? Infinity : 0 }}\n        className={`inline-flex items-center space-x-3 px-6 py-3 rounded-xl shadow-lg border-2 ${\n          timeRemaining <= 60\n            ? 'bg-gradient-to-r from-red-600 to-red-700 border-red-300 text-white'\n            : timeRemaining <= warningThreshold\n            ? 'bg-gradient-to-r from-yellow-500 to-orange-500 border-yellow-300 text-white'\n            : 'bg-gradient-to-r from-blue-600 to-indigo-600 border-blue-300 text-white'\n        }`}\n        style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }}\n      >\n        {timeRemaining <= warningThreshold && (\n          <motion.div\n            animate={{ rotate: [0, 10, -10, 0] }}\n            transition={{ duration: 0.5, repeat: Infinity }}\n          >\n            <TbAlertTriangle className=\"w-5 h-5 text-white drop-shadow-md\" />\n          </motion.div>\n        )}\n\n        <TbClock className=\"w-5 h-5 text-white drop-shadow-md\" />\n\n        <div className=\"text-center\">\n          <div className=\"text-xs font-semibold opacity-90 mb-1\">TIME LEFT</div>\n          <span className=\"font-mono font-black text-lg text-white\" style={{ textShadow: '2px 2px 4px rgba(0,0,0,0.7)' }}>\n            {formatTime(timeRemaining)}\n          </span>\n        </div>\n      </motion.div>\n\n      {/* Progress Bar */}\n      <div className=\"mt-3 w-full bg-gray-300 rounded-full h-2 overflow-hidden shadow-inner\">\n        <motion.div\n          initial={{ width: 0 }}\n          animate={{ width: `${getProgressPercentage()}%` }}\n          transition={{ duration: 0.5 }}\n          className={`h-full bg-gradient-to-r ${getProgressColor()} rounded-full shadow-sm`}\n        />\n      </div>\n\n      {/* Warning Message */}\n      {isWarning && timeRemaining > 60 && (\n        <motion.div\n          initial={{ opacity: 0, y: -10 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"mt-2 text-xs text-yellow-700 font-medium\"\n        >\n          ⚠️ {Math.floor(timeRemaining / 60)} minutes remaining\n        </motion.div>\n      )}\n\n      {/* Critical Warning */}\n      {timeRemaining <= 60 && timeRemaining > 0 && (\n        <motion.div\n          initial={{ opacity: 0, y: -10 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"mt-2 text-xs text-red-700 font-medium\"\n        >\n          🚨 Less than 1 minute left!\n        </motion.div>\n      )}\n\n      {/* Time's Up */}\n      {timeRemaining === 0 && (\n        <motion.div\n          initial={{ opacity: 0, scale: 0.8 }}\n          animate={{ opacity: 1, scale: 1 }}\n          className=\"mt-2 text-xs text-red-700 font-bold\"\n        >\n          ⏰ Time's up!\n        </motion.div>\n      )}\n    </div>\n  );\n};\n\n// Full-screen timer overlay for critical moments\nexport const QuizTimerOverlay = ({ timeRemaining, onClose }) => {\n  if (timeRemaining > 10) return null;\n\n  return (\n    <motion.div\n      initial={{ opacity: 0 }}\n      animate={{ opacity: 1 }}\n      exit={{ opacity: 0 }}\n      className=\"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center\"\n    >\n      <motion.div\n        initial={{ scale: 0.8, opacity: 0 }}\n        animate={{ scale: 1, opacity: 1 }}\n        className=\"bg-white rounded-2xl p-8 text-center shadow-2xl max-w-sm mx-4\"\n      >\n        <motion.div\n          animate={{ scale: [1, 1.2, 1] }}\n          transition={{ duration: 1, repeat: Infinity }}\n          className=\"text-6xl mb-4\"\n        >\n          ⏰\n        </motion.div>\n        \n        <h3 className=\"text-2xl font-bold text-red-600 mb-2\">\n          Time Almost Up!\n        </h3>\n        \n        <motion.div\n          animate={{ scale: [1, 1.1, 1] }}\n          transition={{ duration: 0.5, repeat: Infinity }}\n          className=\"text-4xl font-mono font-bold text-red-600 mb-4\"\n        >\n          {timeRemaining}\n        </motion.div>\n        \n        <p className=\"text-gray-600 mb-4\">\n          Submit your answers now!\n        </p>\n        \n        <button\n          onClick={onClose}\n          className=\"px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors\"\n        >\n          Continue Quiz\n        </button>\n      </motion.div>\n    </motion.div>\n  );\n};\n\nexport default QuizTimer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,EAAEC,eAAe,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,SAAS,GAAGA,CAAC;EACjBC,QAAQ;EAAE;EACVC,QAAQ;EACRC,QAAQ,GAAG,IAAI;EACfC,WAAW,GAAG,IAAI;EAClBC,gBAAgB,GAAG,GAAG;EAAE;EACxBC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGhB,QAAQ,CAACQ,QAAQ,CAAC;EAC5D,MAAM,CAACS,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAEjDC,SAAS,CAAC,MAAM;IACde,gBAAgB,CAACR,QAAQ,CAAC;EAC5B,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EAEdP,SAAS,CAAC,MAAM;IACd,IAAI,CAACS,QAAQ,EAAE;IAEf,MAAMS,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCJ,gBAAgB,CAAEK,IAAI,IAAK;QACzB,IAAIA,IAAI,IAAI,CAAC,EAAE;UACbZ,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAG,CAAC;UACZ,OAAO,CAAC;QACV;QAEA,MAAMa,OAAO,GAAGD,IAAI,GAAG,CAAC;;QAExB;QACA,IAAIV,WAAW,IAAIW,OAAO,IAAIV,gBAAgB,IAAI,CAACK,SAAS,EAAE;UAC5DC,YAAY,CAAC,IAAI,CAAC;QACpB;QAEA,OAAOI,OAAO;MAChB,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMC,aAAa,CAACJ,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACT,QAAQ,EAAED,QAAQ,EAAEE,WAAW,EAAEC,gBAAgB,EAAEK,SAAS,CAAC,CAAC;EAElE,MAAMO,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,IAAI,CAAC;IACxC,MAAMI,OAAO,GAAGF,IAAI,CAACC,KAAK,CAAEH,OAAO,GAAG,IAAI,GAAI,EAAE,CAAC;IACjD,MAAMK,IAAI,GAAGL,OAAO,GAAG,EAAE;IAEzB,IAAIC,KAAK,GAAG,CAAC,EAAE;MACb,OAAQ,GAAEA,KAAM,IAAGG,OAAO,CAACE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,IAAGF,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,EAAC;IAC9F;IACA,OAAQ,GAAEH,OAAQ,IAAGC,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,EAAC;EACzD,CAAC;EAED,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClC,OAAQ,CAACzB,QAAQ,GAAGO,aAAa,IAAIP,QAAQ,GAAI,GAAG;EACtD,CAAC;EAED,MAAM0B,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAInB,aAAa,IAAI,EAAE,EAAE,OAAO,YAAY,CAAC,CAAC;IAC9C,IAAIA,aAAa,IAAIH,gBAAgB,EAAE,OAAO,YAAY,CAAC,CAAC;IAC5D,OAAO,YAAY,CAAC,CAAC;EACvB,CAAC;;EAED,MAAMuB,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIpB,aAAa,IAAI,EAAE,EAAE,OAAO,yBAAyB;IACzD,IAAIA,aAAa,IAAIH,gBAAgB,EAAE,OAAO,+BAA+B;IAC7E,OAAO,8BAA8B;EACvC,CAAC;EAED,oBACEN,OAAA;IAAKO,SAAS,EAAG,GAAEA,SAAU,EAAE;IAAAuB,QAAA,gBAE7B9B,OAAA,CAACJ,MAAM,CAACmC,GAAG;MACTC,OAAO,EAAErB,SAAS,GAAG;QAAEsB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;MAAE,CAAC,GAAG,CAAC,CAAE;MAClDC,UAAU,EAAE;QAAEhC,QAAQ,EAAE,CAAC;QAAEiC,MAAM,EAAExB,SAAS,GAAGyB,QAAQ,GAAG;MAAE,CAAE;MAC9D7B,SAAS,EAAG,8EACVE,aAAa,IAAI,EAAE,GACf,oEAAoE,GACpEA,aAAa,IAAIH,gBAAgB,GACjC,6EAA6E,GAC7E,yEACL,EAAE;MACH+B,KAAK,EAAE;QAAEC,UAAU,EAAE;MAA8B,CAAE;MAAAR,QAAA,GAEpDrB,aAAa,IAAIH,gBAAgB,iBAChCN,OAAA,CAACJ,MAAM,CAACmC,GAAG;QACTC,OAAO,EAAE;UAAEO,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;QAAE,CAAE;QACrCL,UAAU,EAAE;UAAEhC,QAAQ,EAAE,GAAG;UAAEiC,MAAM,EAAEC;QAAS,CAAE;QAAAN,QAAA,eAEhD9B,OAAA,CAACF,eAAe;UAACS,SAAS,EAAC;QAAmC;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CACb,eAED3C,OAAA,CAACH,OAAO;QAACU,SAAS,EAAC;MAAmC;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEzD3C,OAAA;QAAKO,SAAS,EAAC,aAAa;QAAAuB,QAAA,gBAC1B9B,OAAA;UAAKO,SAAS,EAAC,uCAAuC;UAAAuB,QAAA,EAAC;QAAS;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACtE3C,OAAA;UAAMO,SAAS,EAAC,yCAAyC;UAAC8B,KAAK,EAAE;YAAEC,UAAU,EAAE;UAA8B,CAAE;UAAAR,QAAA,EAC5GZ,UAAU,CAACT,aAAa;QAAC;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGb3C,OAAA;MAAKO,SAAS,EAAC,uEAAuE;MAAAuB,QAAA,eACpF9B,OAAA,CAACJ,MAAM,CAACmC,GAAG;QACTa,OAAO,EAAE;UAAEC,KAAK,EAAE;QAAE,CAAE;QACtBb,OAAO,EAAE;UAAEa,KAAK,EAAG,GAAElB,qBAAqB,CAAC,CAAE;QAAG,CAAE;QAClDO,UAAU,EAAE;UAAEhC,QAAQ,EAAE;QAAI,CAAE;QAC9BK,SAAS,EAAG,2BAA0BsB,gBAAgB,CAAC,CAAE;MAAyB;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGLhC,SAAS,IAAIF,aAAa,GAAG,EAAE,iBAC9BT,OAAA,CAACJ,MAAM,CAACmC,GAAG;MACTa,OAAO,EAAE;QAAEE,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;MAAG,CAAE;MAChCf,OAAO,EAAE;QAAEc,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BxC,SAAS,EAAC,0CAA0C;MAAAuB,QAAA,GACrD,eACI,EAACT,IAAI,CAACC,KAAK,CAACb,aAAa,GAAG,EAAE,CAAC,EAAC,oBACrC;IAAA;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CACb,EAGAlC,aAAa,IAAI,EAAE,IAAIA,aAAa,GAAG,CAAC,iBACvCT,OAAA,CAACJ,MAAM,CAACmC,GAAG;MACTa,OAAO,EAAE;QAAEE,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;MAAG,CAAE;MAChCf,OAAO,EAAE;QAAEc,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BxC,SAAS,EAAC,uCAAuC;MAAAuB,QAAA,EAClD;IAED;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CACb,EAGAlC,aAAa,KAAK,CAAC,iBAClBT,OAAA,CAACJ,MAAM,CAACmC,GAAG;MACTa,OAAO,EAAE;QAAEE,OAAO,EAAE,CAAC;QAAEb,KAAK,EAAE;MAAI,CAAE;MACpCD,OAAO,EAAE;QAAEc,OAAO,EAAE,CAAC;QAAEb,KAAK,EAAE;MAAE,CAAE;MAClC1B,SAAS,EAAC,qCAAqC;MAAAuB,QAAA,EAChD;IAED;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CACb;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAAnC,EAAA,CAlJMP,SAAS;AAAA+C,EAAA,GAAT/C,SAAS;AAmJf,OAAO,MAAMgD,gBAAgB,GAAGA,CAAC;EAAExC,aAAa;EAAEyC;AAAQ,CAAC,KAAK;EAC9D,IAAIzC,aAAa,GAAG,EAAE,EAAE,OAAO,IAAI;EAEnC,oBACET,OAAA,CAACJ,MAAM,CAACmC,GAAG;IACTa,OAAO,EAAE;MAAEE,OAAO,EAAE;IAAE,CAAE;IACxBd,OAAO,EAAE;MAAEc,OAAO,EAAE;IAAE,CAAE;IACxBK,IAAI,EAAE;MAAEL,OAAO,EAAE;IAAE,CAAE;IACrBvC,SAAS,EAAC,kFAAkF;IAAAuB,QAAA,eAE5F9B,OAAA,CAACJ,MAAM,CAACmC,GAAG;MACTa,OAAO,EAAE;QAAEX,KAAK,EAAE,GAAG;QAAEa,OAAO,EAAE;MAAE,CAAE;MACpCd,OAAO,EAAE;QAAEC,KAAK,EAAE,CAAC;QAAEa,OAAO,EAAE;MAAE,CAAE;MAClCvC,SAAS,EAAC,+DAA+D;MAAAuB,QAAA,gBAEzE9B,OAAA,CAACJ,MAAM,CAACmC,GAAG;QACTC,OAAO,EAAE;UAAEC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;QAAE,CAAE;QAChCC,UAAU,EAAE;UAAEhC,QAAQ,EAAE,CAAC;UAAEiC,MAAM,EAAEC;QAAS,CAAE;QAC9C7B,SAAS,EAAC,eAAe;QAAAuB,QAAA,EAC1B;MAED;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb3C,OAAA;QAAIO,SAAS,EAAC,sCAAsC;QAAAuB,QAAA,EAAC;MAErD;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAEL3C,OAAA,CAACJ,MAAM,CAACmC,GAAG;QACTC,OAAO,EAAE;UAAEC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;QAAE,CAAE;QAChCC,UAAU,EAAE;UAAEhC,QAAQ,EAAE,GAAG;UAAEiC,MAAM,EAAEC;QAAS,CAAE;QAChD7B,SAAS,EAAC,gDAAgD;QAAAuB,QAAA,EAEzDrB;MAAa;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEb3C,OAAA;QAAGO,SAAS,EAAC,oBAAoB;QAAAuB,QAAA,EAAC;MAElC;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAEJ3C,OAAA;QACEoD,OAAO,EAAEF,OAAQ;QACjB3C,SAAS,EAAC,+EAA+E;QAAAuB,QAAA,EAC1F;MAED;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEjB,CAAC;AAACU,GAAA,GAhDWJ,gBAAgB;AAkD7B,eAAehD,SAAS;AAAC,IAAA+C,EAAA,EAAAK,GAAA;AAAAC,YAAA,CAAAN,EAAA;AAAAM,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}