{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\modern\\\\RankingDemo.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport UserRankingList from './UserRankingList';\nimport UserRankingCard from './UserRankingCard';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RankingDemo = () => {\n  _s();\n  const [layout, setLayout] = useState('horizontal');\n  const [size, setSize] = useState('medium');\n\n  // Sample user data\n  const sampleUsers = [{\n    userId: '1',\n    name: '<PERSON>',\n    profilePicture: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',\n    subscriptionStatus: 'active',\n    totalPoints: 2850,\n    passedExamsCount: 15,\n    quizzesTaken: 23,\n    score: 2850,\n    rank: 1\n  }, {\n    userId: '2',\n    name: '<PERSON>',\n    profilePicture: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',\n    subscriptionStatus: 'free',\n    totalPoints: 2720,\n    passedExamsCount: 12,\n    quizzesTaken: 20,\n    score: 2720,\n    rank: 2\n  }, {\n    userId: '3',\n    name: 'Carol Davis',\n    profilePicture: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',\n    subscriptionStatus: 'premium',\n    totalPoints: 2650,\n    passedExamsCount: 14,\n    quizzesTaken: 19,\n    score: 2650,\n    rank: 3\n  }, {\n    userId: '4',\n    name: 'David Wilson',\n    profilePicture: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',\n    subscriptionStatus: 'expired',\n    totalPoints: 2400,\n    passedExamsCount: 10,\n    quizzesTaken: 18,\n    score: 2400,\n    rank: 4\n  }, {\n    userId: '5',\n    name: 'Emma Brown',\n    profilePicture: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',\n    subscriptionStatus: 'active',\n    totalPoints: 2350,\n    passedExamsCount: 11,\n    quizzesTaken: 16,\n    score: 2350,\n    rank: 5\n  }, {\n    userId: '6',\n    name: 'Frank Miller',\n    profilePicture: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',\n    subscriptionStatus: 'free',\n    totalPoints: 2200,\n    passedExamsCount: 9,\n    quizzesTaken: 15,\n    score: 2200,\n    rank: 6\n  }, {\n    userId: '7',\n    name: 'Grace Lee',\n    profilePicture: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face',\n    subscriptionStatus: 'premium',\n    totalPoints: 2100,\n    passedExamsCount: 8,\n    quizzesTaken: 14,\n    score: 2100,\n    rank: 7\n  }, {\n    userId: '8',\n    name: 'Henry Taylor',\n    profilePicture: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face',\n    subscriptionStatus: 'free',\n    totalPoints: 1950,\n    passedExamsCount: 7,\n    quizzesTaken: 13,\n    score: 1950,\n    rank: 8\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 py-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-bold text-gray-900 mb-4\",\n          children: \"Modern User Ranking Component\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n          children: \"A beautiful, responsive ranking component with Instagram-style profile circles, premium user highlighting, and multiple layout options.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -10\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.2\n        },\n        className: \"bg-white rounded-xl p-6 mb-8 shadow-sm border border-gray-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-900 mb-4\",\n          children: \"Customization Options\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Layout Style\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: ['horizontal', 'vertical', 'grid'].map(layoutOption => /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  name: \"layout\",\n                  value: layoutOption,\n                  checked: layout === layoutOption,\n                  onChange: e => setLayout(e.target.value),\n                  className: \"mr-2 text-blue-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"capitalize\",\n                  children: layoutOption\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 41\n                }, this)]\n              }, layoutOption, true, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 37\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Component Size\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: ['small', 'medium', 'large'].map(sizeOption => /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  name: \"size\",\n                  value: sizeOption,\n                  checked: size === sizeOption,\n                  onChange: e => setSize(e.target.value),\n                  className: \"mr-2 text-blue-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"capitalize\",\n                  children: sizeOption\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 41\n                }, this)]\n              }, sizeOption, true, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 37\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.3\n        },\n        className: \"mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900 mb-6\",\n          children: \"Individual Card Examples\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl p-6 border border-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"Premium User (Gold Glow)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(UserRankingCard, {\n              user: sampleUsers[0],\n              rank: 1,\n              isCurrentUser: false,\n              layout: \"horizontal\",\n              size: size,\n              showStats: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl p-6 border border-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"Current User (Highlighted)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(UserRankingCard, {\n              user: sampleUsers[1],\n              rank: 2,\n              isCurrentUser: true,\n              layout: \"horizontal\",\n              size: size,\n              showStats: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.4\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900 mb-6\",\n          children: \"Complete Ranking List\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(UserRankingList, {\n          users: sampleUsers,\n          currentUserId: \"3\" // Carol Davis as current user\n          ,\n          layout: layout,\n          size: size,\n          showSearch: true,\n          showFilters: true,\n          showStats: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.5\n        },\n        className: \"mt-12 bg-white rounded-xl p-8 border border-gray-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900 mb-6\",\n          children: \"Key Features\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-semibold text-gray-900\",\n              children: \"\\uD83C\\uDFA8 Premium Highlighting\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-sm\",\n              children: \"Gold gradient glow for premium users with vibrant visual distinction\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-semibold text-gray-900\",\n              children: \"\\uD83D\\uDCF1 Responsive Design\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-sm\",\n              children: \"Adapts perfectly to mobile, tablet, and desktop screens\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-semibold text-gray-900\",\n              children: \"\\uD83D\\uDD0D Search & Filter\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-sm\",\n              children: \"Real-time search and filtering by subscription status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-semibold text-gray-900\",\n              children: \"\\u2728 Smooth Animations\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-sm\",\n              children: \"Framer Motion powered animations for engaging interactions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-semibold text-gray-900\",\n              children: \"\\uD83C\\uDFC6 Rank Indicators\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-sm\",\n              children: \"Special icons and colors for top performers\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-semibold text-gray-900\",\n              children: \"\\uD83C\\uDFAF Current User Focus\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-sm\",\n              children: \"Automatic highlighting and scroll-to functionality\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 103,\n    columnNumber: 9\n  }, this);\n};\n_s(RankingDemo, \"X0BW6Yc9X5I1oJILBx/MrMC36iM=\");\n_c = RankingDemo;\nexport default RankingDemo;\nvar _c;\n$RefreshReg$(_c, \"RankingDemo\");", "map": {"version": 3, "names": ["React", "useState", "motion", "UserRankingList", "UserRankingCard", "jsxDEV", "_jsxDEV", "RankingDemo", "_s", "layout", "setLayout", "size", "setSize", "sampleUsers", "userId", "name", "profilePicture", "subscriptionStatus", "totalPoints", "passedExamsCount", "quizzesTaken", "score", "rank", "className", "children", "div", "initial", "opacity", "y", "animate", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "transition", "delay", "map", "layoutOption", "type", "value", "checked", "onChange", "e", "target", "sizeOption", "user", "isCurrentUser", "showStats", "users", "currentUserId", "showSearch", "showFilters", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/modern/RankingDemo.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport UserRankingList from './UserRankingList';\nimport UserRankingCard from './UserRankingCard';\n\nconst RankingDemo = () => {\n    const [layout, setLayout] = useState('horizontal');\n    const [size, setSize] = useState('medium');\n\n    // Sample user data\n    const sampleUsers = [\n        {\n            userId: '1',\n            name: '<PERSON>',\n            profilePicture: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',\n            subscriptionStatus: 'active',\n            totalPoints: 2850,\n            passedExamsCount: 15,\n            quizzesTaken: 23,\n            score: 2850,\n            rank: 1\n        },\n        {\n            userId: '2',\n            name: '<PERSON>',\n            profilePicture: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',\n            subscriptionStatus: 'free',\n            totalPoints: 2720,\n            passedExamsCount: 12,\n            quizzesTaken: 20,\n            score: 2720,\n            rank: 2\n        },\n        {\n            userId: '3',\n            name: '<PERSON>',\n            profilePicture: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',\n            subscriptionStatus: 'premium',\n            totalPoints: 2650,\n            passedExamsCount: 14,\n            quizzesTaken: 19,\n            score: 2650,\n            rank: 3\n        },\n        {\n            userId: '4',\n            name: 'David Wilson',\n            profilePicture: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',\n            subscriptionStatus: 'expired',\n            totalPoints: 2400,\n            passedExamsCount: 10,\n            quizzesTaken: 18,\n            score: 2400,\n            rank: 4\n        },\n        {\n            userId: '5',\n            name: 'Emma Brown',\n            profilePicture: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',\n            subscriptionStatus: 'active',\n            totalPoints: 2350,\n            passedExamsCount: 11,\n            quizzesTaken: 16,\n            score: 2350,\n            rank: 5\n        },\n        {\n            userId: '6',\n            name: 'Frank Miller',\n            profilePicture: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',\n            subscriptionStatus: 'free',\n            totalPoints: 2200,\n            passedExamsCount: 9,\n            quizzesTaken: 15,\n            score: 2200,\n            rank: 6\n        },\n        {\n            userId: '7',\n            name: 'Grace Lee',\n            profilePicture: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face',\n            subscriptionStatus: 'premium',\n            totalPoints: 2100,\n            passedExamsCount: 8,\n            quizzesTaken: 14,\n            score: 2100,\n            rank: 7\n        },\n        {\n            userId: '8',\n            name: 'Henry Taylor',\n            profilePicture: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face',\n            subscriptionStatus: 'free',\n            totalPoints: 1950,\n            passedExamsCount: 7,\n            quizzesTaken: 13,\n            score: 1950,\n            rank: 8\n        }\n    ];\n\n    return (\n        <div className=\"min-h-screen bg-gray-50 py-8\">\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n                {/* Header */}\n                <motion.div\n                    initial={{ opacity: 0, y: -20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"text-center mb-8\"\n                >\n                    <h1 className=\"text-4xl font-bold text-gray-900 mb-4\">\n                        Modern User Ranking Component\n                    </h1>\n                    <p className=\"text-lg text-gray-600 max-w-3xl mx-auto\">\n                        A beautiful, responsive ranking component with Instagram-style profile circles, \n                        premium user highlighting, and multiple layout options.\n                    </p>\n                </motion.div>\n\n                {/* Controls */}\n                <motion.div\n                    initial={{ opacity: 0, y: -10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 0.2 }}\n                    className=\"bg-white rounded-xl p-6 mb-8 shadow-sm border border-gray-200\"\n                >\n                    <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Customization Options</h2>\n                    \n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                        {/* Layout Options */}\n                        <div>\n                            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                Layout Style\n                            </label>\n                            <div className=\"space-y-2\">\n                                {['horizontal', 'vertical', 'grid'].map((layoutOption) => (\n                                    <label key={layoutOption} className=\"flex items-center\">\n                                        <input\n                                            type=\"radio\"\n                                            name=\"layout\"\n                                            value={layoutOption}\n                                            checked={layout === layoutOption}\n                                            onChange={(e) => setLayout(e.target.value)}\n                                            className=\"mr-2 text-blue-600\"\n                                        />\n                                        <span className=\"capitalize\">{layoutOption}</span>\n                                    </label>\n                                ))}\n                            </div>\n                        </div>\n\n                        {/* Size Options */}\n                        <div>\n                            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                Component Size\n                            </label>\n                            <div className=\"space-y-2\">\n                                {['small', 'medium', 'large'].map((sizeOption) => (\n                                    <label key={sizeOption} className=\"flex items-center\">\n                                        <input\n                                            type=\"radio\"\n                                            name=\"size\"\n                                            value={sizeOption}\n                                            checked={size === sizeOption}\n                                            onChange={(e) => setSize(e.target.value)}\n                                            className=\"mr-2 text-blue-600\"\n                                        />\n                                        <span className=\"capitalize\">{sizeOption}</span>\n                                    </label>\n                                ))}\n                            </div>\n                        </div>\n                    </div>\n                </motion.div>\n\n                {/* Individual Card Examples */}\n                <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 0.3 }}\n                    className=\"mb-8\"\n                >\n                    <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Individual Card Examples</h2>\n                    \n                    <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                        {/* Premium User Card */}\n                        <div className=\"bg-white rounded-xl p-6 border border-gray-200\">\n                            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Premium User (Gold Glow)</h3>\n                            <UserRankingCard\n                                user={sampleUsers[0]}\n                                rank={1}\n                                isCurrentUser={false}\n                                layout=\"horizontal\"\n                                size={size}\n                                showStats={true}\n                            />\n                        </div>\n\n                        {/* Current User Card */}\n                        <div className=\"bg-white rounded-xl p-6 border border-gray-200\">\n                            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Current User (Highlighted)</h3>\n                            <UserRankingCard\n                                user={sampleUsers[1]}\n                                rank={2}\n                                isCurrentUser={true}\n                                layout=\"horizontal\"\n                                size={size}\n                                showStats={true}\n                            />\n                        </div>\n                    </div>\n                </motion.div>\n\n                {/* Full Ranking List */}\n                <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 0.4 }}\n                >\n                    <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Complete Ranking List</h2>\n                    \n                    <UserRankingList\n                        users={sampleUsers}\n                        currentUserId=\"3\" // Carol Davis as current user\n                        layout={layout}\n                        size={size}\n                        showSearch={true}\n                        showFilters={true}\n                        showStats={true}\n                    />\n                </motion.div>\n\n                {/* Features List */}\n                <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 0.5 }}\n                    className=\"mt-12 bg-white rounded-xl p-8 border border-gray-200\"\n                >\n                    <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Key Features</h2>\n                    \n                    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n                        <div className=\"space-y-2\">\n                            <h3 className=\"font-semibold text-gray-900\">🎨 Premium Highlighting</h3>\n                            <p className=\"text-gray-600 text-sm\">Gold gradient glow for premium users with vibrant visual distinction</p>\n                        </div>\n                        \n                        <div className=\"space-y-2\">\n                            <h3 className=\"font-semibold text-gray-900\">📱 Responsive Design</h3>\n                            <p className=\"text-gray-600 text-sm\">Adapts perfectly to mobile, tablet, and desktop screens</p>\n                        </div>\n                        \n                        <div className=\"space-y-2\">\n                            <h3 className=\"font-semibold text-gray-900\">🔍 Search & Filter</h3>\n                            <p className=\"text-gray-600 text-sm\">Real-time search and filtering by subscription status</p>\n                        </div>\n                        \n                        <div className=\"space-y-2\">\n                            <h3 className=\"font-semibold text-gray-900\">✨ Smooth Animations</h3>\n                            <p className=\"text-gray-600 text-sm\">Framer Motion powered animations for engaging interactions</p>\n                        </div>\n                        \n                        <div className=\"space-y-2\">\n                            <h3 className=\"font-semibold text-gray-900\">🏆 Rank Indicators</h3>\n                            <p className=\"text-gray-600 text-sm\">Special icons and colors for top performers</p>\n                        </div>\n                        \n                        <div className=\"space-y-2\">\n                            <h3 className=\"font-semibold text-gray-900\">🎯 Current User Focus</h3>\n                            <p className=\"text-gray-600 text-sm\">Automatic highlighting and scroll-to functionality</p>\n                        </div>\n                    </div>\n                </motion.div>\n            </div>\n        </div>\n    );\n};\n\nexport default RankingDemo;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,eAAe,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGT,QAAQ,CAAC,YAAY,CAAC;EAClD,MAAM,CAACU,IAAI,EAAEC,OAAO,CAAC,GAAGX,QAAQ,CAAC,QAAQ,CAAC;;EAE1C;EACA,MAAMY,WAAW,GAAG,CAChB;IACIC,MAAM,EAAE,GAAG;IACXC,IAAI,EAAE,eAAe;IACrBC,cAAc,EAAE,6FAA6F;IAC7GC,kBAAkB,EAAE,QAAQ;IAC5BC,WAAW,EAAE,IAAI;IACjBC,gBAAgB,EAAE,EAAE;IACpBC,YAAY,EAAE,EAAE;IAChBC,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE;EACV,CAAC,EACD;IACIR,MAAM,EAAE,GAAG;IACXC,IAAI,EAAE,WAAW;IACjBC,cAAc,EAAE,6FAA6F;IAC7GC,kBAAkB,EAAE,MAAM;IAC1BC,WAAW,EAAE,IAAI;IACjBC,gBAAgB,EAAE,EAAE;IACpBC,YAAY,EAAE,EAAE;IAChBC,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE;EACV,CAAC,EACD;IACIR,MAAM,EAAE,GAAG;IACXC,IAAI,EAAE,aAAa;IACnBC,cAAc,EAAE,6FAA6F;IAC7GC,kBAAkB,EAAE,SAAS;IAC7BC,WAAW,EAAE,IAAI;IACjBC,gBAAgB,EAAE,EAAE;IACpBC,YAAY,EAAE,EAAE;IAChBC,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE;EACV,CAAC,EACD;IACIR,MAAM,EAAE,GAAG;IACXC,IAAI,EAAE,cAAc;IACpBC,cAAc,EAAE,6FAA6F;IAC7GC,kBAAkB,EAAE,SAAS;IAC7BC,WAAW,EAAE,IAAI;IACjBC,gBAAgB,EAAE,EAAE;IACpBC,YAAY,EAAE,EAAE;IAChBC,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE;EACV,CAAC,EACD;IACIR,MAAM,EAAE,GAAG;IACXC,IAAI,EAAE,YAAY;IAClBC,cAAc,EAAE,0FAA0F;IAC1GC,kBAAkB,EAAE,QAAQ;IAC5BC,WAAW,EAAE,IAAI;IACjBC,gBAAgB,EAAE,EAAE;IACpBC,YAAY,EAAE,EAAE;IAChBC,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE;EACV,CAAC,EACD;IACIR,MAAM,EAAE,GAAG;IACXC,IAAI,EAAE,cAAc;IACpBC,cAAc,EAAE,6FAA6F;IAC7GC,kBAAkB,EAAE,MAAM;IAC1BC,WAAW,EAAE,IAAI;IACjBC,gBAAgB,EAAE,CAAC;IACnBC,YAAY,EAAE,EAAE;IAChBC,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE;EACV,CAAC,EACD;IACIR,MAAM,EAAE,GAAG;IACXC,IAAI,EAAE,WAAW;IACjBC,cAAc,EAAE,6FAA6F;IAC7GC,kBAAkB,EAAE,SAAS;IAC7BC,WAAW,EAAE,IAAI;IACjBC,gBAAgB,EAAE,CAAC;IACnBC,YAAY,EAAE,EAAE;IAChBC,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE;EACV,CAAC,EACD;IACIR,MAAM,EAAE,GAAG;IACXC,IAAI,EAAE,cAAc;IACpBC,cAAc,EAAE,6FAA6F;IAC7GC,kBAAkB,EAAE,MAAM;IAC1BC,WAAW,EAAE,IAAI;IACjBC,gBAAgB,EAAE,CAAC;IACnBC,YAAY,EAAE,EAAE;IAChBC,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE;EACV,CAAC,CACJ;EAED,oBACIhB,OAAA;IAAKiB,SAAS,EAAC,8BAA8B;IAAAC,QAAA,eACzClB,OAAA;MAAKiB,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBAEnDlB,OAAA,CAACJ,MAAM,CAACuB,GAAG;QACPC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BL,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAE5BlB,OAAA;UAAIiB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAEtD;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL3B,OAAA;UAAGiB,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAGvD;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGb3B,OAAA,CAACJ,MAAM,CAACuB,GAAG;QACPC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BM,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3BZ,SAAS,EAAC,+DAA+D;QAAAC,QAAA,gBAEzElB,OAAA;UAAIiB,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAqB;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEnF3B,OAAA;UAAKiB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBAElDlB,OAAA;YAAAkB,QAAA,gBACIlB,OAAA;cAAOiB,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR3B,OAAA;cAAKiB,SAAS,EAAC,WAAW;cAAAC,QAAA,EACrB,CAAC,YAAY,EAAE,UAAU,EAAE,MAAM,CAAC,CAACY,GAAG,CAAEC,YAAY,iBACjD/B,OAAA;gBAA0BiB,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBACnDlB,OAAA;kBACIgC,IAAI,EAAC,OAAO;kBACZvB,IAAI,EAAC,QAAQ;kBACbwB,KAAK,EAAEF,YAAa;kBACpBG,OAAO,EAAE/B,MAAM,KAAK4B,YAAa;kBACjCI,QAAQ,EAAGC,CAAC,IAAKhC,SAAS,CAACgC,CAAC,CAACC,MAAM,CAACJ,KAAK,CAAE;kBAC3ChB,SAAS,EAAC;gBAAoB;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eACF3B,OAAA;kBAAMiB,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAEa;gBAAY;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GAT1CI,YAAY;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUjB,CACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGN3B,OAAA;YAAAkB,QAAA,gBACIlB,OAAA;cAAOiB,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR3B,OAAA;cAAKiB,SAAS,EAAC,WAAW;cAAAC,QAAA,EACrB,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC,CAACY,GAAG,CAAEQ,UAAU,iBACzCtC,OAAA;gBAAwBiB,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBACjDlB,OAAA;kBACIgC,IAAI,EAAC,OAAO;kBACZvB,IAAI,EAAC,MAAM;kBACXwB,KAAK,EAAEK,UAAW;kBAClBJ,OAAO,EAAE7B,IAAI,KAAKiC,UAAW;kBAC7BH,QAAQ,EAAGC,CAAC,IAAK9B,OAAO,CAAC8B,CAAC,CAACC,MAAM,CAACJ,KAAK,CAAE;kBACzChB,SAAS,EAAC;gBAAoB;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eACF3B,OAAA;kBAAMiB,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAEoB;gBAAU;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GATxCW,UAAU;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUf,CACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGb3B,OAAA,CAACJ,MAAM,CAACuB,GAAG;QACPC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BM,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3BZ,SAAS,EAAC,MAAM;QAAAC,QAAA,gBAEhBlB,OAAA;UAAIiB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAwB;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEnF3B,OAAA;UAAKiB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBAElDlB,OAAA;YAAKiB,SAAS,EAAC,gDAAgD;YAAAC,QAAA,gBAC3DlB,OAAA;cAAIiB,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAwB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtF3B,OAAA,CAACF,eAAe;cACZyC,IAAI,EAAEhC,WAAW,CAAC,CAAC,CAAE;cACrBS,IAAI,EAAE,CAAE;cACRwB,aAAa,EAAE,KAAM;cACrBrC,MAAM,EAAC,YAAY;cACnBE,IAAI,EAAEA,IAAK;cACXoC,SAAS,EAAE;YAAK;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGN3B,OAAA;YAAKiB,SAAS,EAAC,gDAAgD;YAAAC,QAAA,gBAC3DlB,OAAA;cAAIiB,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAA0B;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxF3B,OAAA,CAACF,eAAe;cACZyC,IAAI,EAAEhC,WAAW,CAAC,CAAC,CAAE;cACrBS,IAAI,EAAE,CAAE;cACRwB,aAAa,EAAE,IAAK;cACpBrC,MAAM,EAAC,YAAY;cACnBE,IAAI,EAAEA,IAAK;cACXoC,SAAS,EAAE;YAAK;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGb3B,OAAA,CAACJ,MAAM,CAACuB,GAAG;QACPC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BM,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAAX,QAAA,gBAE3BlB,OAAA;UAAIiB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAqB;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEhF3B,OAAA,CAACH,eAAe;UACZ6C,KAAK,EAAEnC,WAAY;UACnBoC,aAAa,EAAC,GAAG,CAAC;UAAA;UAClBxC,MAAM,EAAEA,MAAO;UACfE,IAAI,EAAEA,IAAK;UACXuC,UAAU,EAAE,IAAK;UACjBC,WAAW,EAAE,IAAK;UAClBJ,SAAS,EAAE;QAAK;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAGb3B,OAAA,CAACJ,MAAM,CAACuB,GAAG;QACPC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BM,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3BZ,SAAS,EAAC,sDAAsD;QAAAC,QAAA,gBAEhElB,OAAA;UAAIiB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAY;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEvE3B,OAAA;UAAKiB,SAAS,EAAC,sDAAsD;UAAAC,QAAA,gBACjElB,OAAA;YAAKiB,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtBlB,OAAA;cAAIiB,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAC;YAAuB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxE3B,OAAA;cAAGiB,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAoE;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5G,CAAC,eAEN3B,OAAA;YAAKiB,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtBlB,OAAA;cAAIiB,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAC;YAAoB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrE3B,OAAA;cAAGiB,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAuD;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/F,CAAC,eAEN3B,OAAA;YAAKiB,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtBlB,OAAA;cAAIiB,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAC;YAAkB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnE3B,OAAA;cAAGiB,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAqD;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7F,CAAC,eAEN3B,OAAA;YAAKiB,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtBlB,OAAA;cAAIiB,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAC;YAAmB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpE3B,OAAA;cAAGiB,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAA0D;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClG,CAAC,eAEN3B,OAAA;YAAKiB,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtBlB,OAAA;cAAIiB,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAC;YAAkB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnE3B,OAAA;cAAGiB,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAA2C;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnF,CAAC,eAEN3B,OAAA;YAAKiB,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtBlB,OAAA;cAAIiB,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAC;YAAqB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtE3B,OAAA;cAAGiB,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAkD;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1F,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACzB,EAAA,CA/QID,WAAW;AAAA6C,EAAA,GAAX7C,WAAW;AAiRjB,eAAeA,WAAW;AAAC,IAAA6C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}