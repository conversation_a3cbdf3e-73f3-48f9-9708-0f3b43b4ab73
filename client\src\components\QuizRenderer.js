import React, { useState, useEffect } from 'react';
import '../pages/user/Quiz/responsive.css';

const QuizRenderer = ({
  question,
  questionIndex,
  totalQuestions,
  selectedAnswer,
  onAnswerChange,
  timeLeft,
  onNext,
  onPrevious,
  examTitle = "Quiz",
  isTimeWarning = false
}) => {
  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');
  const [isAnswered, setIsAnswered] = useState(false);

  useEffect(() => {
    setCurrentAnswer(selectedAnswer || '');
    setIsAnswered(!!selectedAnswer);
  }, [selectedAnswer, questionIndex]);

  const handleAnswerSelect = (answer) => {
    setCurrentAnswer(answer);
    setIsAnswered(true);
    onAnswerChange(answer);
  };

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const progressPercentage = ((questionIndex + 1) / totalQuestions) * 100;

  // Early return for invalid question
  if (!question || !question.name) {
    return (
      <div className="quiz-container quiz-responsive">
        <div className="quiz-content quiz-content-responsive">
          <div className="quiz-question-container">
            <div className="text-center text-red-600 p-6 bg-red-50 rounded-lg border border-red-200">
              <i className="ri-error-warning-line text-3xl mb-3 block"></i>
              <h3 className="text-lg font-semibold mb-2">Question Not Available</h3>
              <p className="text-sm">This question could not be loaded. Please try refreshing the page.</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const renderMCQ = () => {
    if (!question || !question.options || Object.keys(question.options).length === 0) {
      return (
        <div className="quiz-question-container">
          <div className="text-center text-red-600 p-4 bg-red-50 rounded-lg border border-red-200">
            <i className="ri-error-warning-line text-2xl mb-2"></i>
            <p>No options available for this question.</p>
          </div>
        </div>
      );
    }

    const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];

    return (
      <div className="quiz-options space-y-3">
        {Object.entries(question.options).map(([key, value], index) => {
          const optionKey = String(key).trim();
          const optionValue = String(value || '').trim();
          const label = optionLabels[index] || optionKey;
          const isSelected = currentAnswer === optionKey;

          // Skip empty options
          if (!optionValue) return null;

          return (
            <button
              key={optionKey}
              onClick={() => handleAnswerSelect(optionKey)}
              className={`quiz-option w-full text-left transition-all duration-200 ${
                isSelected ? 'selected' : ''
              }`}
              style={{
                color: isSelected ? '#ffffff' : '#374151'
              }}
            >
              <span className="quiz-option-letter">{label}</span>
              <span
                className="quiz-option-text"
                style={{
                  color: isSelected ? '#ffffff' : '#000000',
                  fontWeight: isSelected ? '600' : '500'
                }}
              >
                {optionValue}
              </span>
            </button>
          );
        })}
      </div>
    );
  };

  const renderFillBlank = () => (
    <div className="quiz-question-container space-y-4">
      <label className="block text-sm font-medium text-gray-700 mb-2">Your Answer:</label>
      <input
        type="text"
        value={currentAnswer}
        onChange={(e) => handleAnswerSelect(e.target.value)}
        placeholder="Type your answer here..."
        className="quiz-fill-input w-full"
      />

      {currentAnswer && (
        <div className="bg-gradient-to-r from-emerald-50 to-green-50 rounded-xl p-4 sm:p-6 border border-emerald-200 shadow-sm mt-4">
          <div className="flex items-center space-x-3 sm:space-x-4">
            <div className="w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-br from-emerald-500 to-green-500 rounded-full flex items-center justify-center shadow-sm">
              <span className="text-white text-sm sm:text-base">✓</span>
            </div>
            <p className="text-emerald-800 font-semibold text-sm sm:text-base">
              Answer: {currentAnswer}
            </p>
          </div>
        </div>
      )}
    </div>
  );

  const renderImageQuestion = () => (
    <div className="space-y-8">
      {question.imageUrl && (
        <div className="text-center">
          <div className="inline-block p-2 bg-white rounded-xl shadow-lg border border-gray-200">
            <img
              src={question.imageUrl}
              alt="Question diagram"
              className="max-w-full max-h-96 rounded-lg mx-auto"
            />
          </div>
        </div>
      )}

      {question.options ? renderMCQ() : renderFillBlank()}
    </div>
  );

  return (
    <div className="quiz-container quiz-responsive">
      <div className="quiz-progress-bar">
        <div
          className="quiz-progress-fill"
          style={{ width: `${progressPercentage}%` }}
        />
      </div>

      <div className="quiz-progress-container">
        <div className="quiz-header-content-improved">
          <div className="quiz-title-section">
            <p className="quiz-subtitle text-sm sm:text-base">
              {examTitle}
            </p>
          </div>

          <div className="quiz-timer-center">
            <div className={`quiz-timer ${isTimeWarning ? 'warning' : ''}`}>
              {formatTime(timeLeft)}
            </div>
          </div>

          <div className="quiz-question-counter-right">
            Question {questionIndex + 1} of {totalQuestions}
          </div>
        </div>
      </div>

      <div className="quiz-content pb-20 quiz-content-responsive">
        <div className="quiz-question-container">
          <div className="quiz-question-number text-sm sm:text-base">
            Question {questionIndex + 1}
          </div>

          <div className="quiz-question-text text-lg sm:text-xl">
            {question.name}
          </div>

          {question.image && (
            <div className="quiz-image-container">
              <img
                src={question.image}
                alt="Question"
                className="quiz-image"
              />
            </div>
          )}

          {question.answerType === "Options" && renderMCQ()}
          {(question.answerType === "Free Text" || question.answerType === "Fill in the Blank") && renderFillBlank()}
          {question.imageUrl && renderImageQuestion()}
        </div>
      </div>

      <div className="quiz-navigation">
        <button
          onClick={onPrevious}
          disabled={questionIndex === 0}
          className={`quiz-nav-btn ${questionIndex === 0 ? 'disabled' : 'secondary'}`}
          title={questionIndex === 0 ? 'This is the first question' : 'Go to previous question'}
        >
          <i className="ri-arrow-left-line mr-2"></i>
          Previous
        </button>

        <button
          onClick={onNext}
          disabled={!isAnswered}
          className={`quiz-nav-btn ${!isAnswered ? 'disabled' : 'primary'}`}
          title={!isAnswered ? 'Please select an answer first' :
                 questionIndex === totalQuestions - 1 ? 'Submit your quiz' : 'Go to next question'}
        >
          {questionIndex === totalQuestions - 1 ? (
            <>
              <i className="ri-check-line mr-2"></i>
              Submit Quiz
            </>
          ) : (
            <>
              Next
              <i className="ri-arrow-right-line ml-2"></i>
            </>
          )}
        </button>
      </div>


    </div>
  );
};

export default QuizRenderer;
