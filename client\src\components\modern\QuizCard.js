import React from 'react';
import { motion } from 'framer-motion';
import { Tb<PERSON><PERSON>, TbQuestionMark, TbUsers, TbTrophy, TbPlayerPlay, TbStar, TbTarget, TbBrain, TbChevronRight } from 'react-icons/tb';
import { Card, Button } from './index';

const QuizCard = ({
  quiz,
  onStart,
  onView,
  showResults = false,
  userResult = null,
  className = '',
  ...props
}) => {
  const getDifficultyColor = (difficulty) => {
    switch (difficulty?.toLowerCase()) {
      case 'easy':
        return 'bg-gradient-to-r from-green-500 to-emerald-500 text-white';
      case 'medium':
        return 'bg-gradient-to-r from-yellow-500 to-orange-500 text-white';
      case 'hard':
        return 'bg-gradient-to-r from-red-500 to-pink-500 text-white';
      default:
        return 'bg-gradient-to-r from-gray-500 to-slate-500 text-white';
    }
  };



  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -12, scale: 1.03 }}
      transition={{ duration: 0.4, ease: "easeOut" }}
      className={`quiz-card-modern group ${className}`}
    >
      <Card
        interactive
        variant="default"
        className="quiz-card overflow-hidden h-full border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white relative rounded-2xl"
        {...props}
      >
        {/* Enhanced Header with Dynamic Gradient */}
        <div className="relative overflow-hidden">
          <div className="bg-gradient-to-br from-blue-600 via-indigo-700 to-purple-800 p-6 text-white relative">
            {/* Animated Background Elements */}
            <div className="absolute inset-0 opacity-20">
              <div className="absolute top-0 right-0 w-40 h-40 bg-white rounded-full -translate-y-20 translate-x-20 animate-pulse"></div>
              <div className="absolute bottom-0 left-0 w-32 h-32 bg-white rounded-full translate-y-16 -translate-x-16 animate-pulse delay-700"></div>
              <div className="absolute top-1/2 left-1/2 w-20 h-20 bg-white rounded-full -translate-x-10 -translate-y-10 animate-pulse delay-300"></div>
            </div>

            {/* Floating Particles */}
            <div className="absolute inset-0">
              <div className="absolute top-4 right-8 w-2 h-2 bg-white rounded-full animate-bounce delay-100"></div>
              <div className="absolute top-12 right-16 w-1 h-1 bg-white rounded-full animate-bounce delay-500"></div>
              <div className="absolute bottom-8 left-12 w-1.5 h-1.5 bg-white rounded-full animate-bounce delay-300"></div>
            </div>

            {/* Shimmer Effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 animate-shimmer"></div>

            <div className="relative z-10">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-14 h-14 bg-white/20 backdrop-blur-md rounded-2xl flex items-center justify-center border border-white/30 shadow-xl group-hover:scale-110 transition-transform duration-300">
                      <TbBrain className="w-7 h-7 text-white" />
                    </div>
                    <div>
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="text-white text-xs font-bold uppercase tracking-wider bg-blue-600/80 px-3 py-1 rounded-full border border-white/30 shadow-lg">
                          Class {quiz.class || 'N/A'}
                        </span>
                        {quiz.subject && (
                          <span className="text-white text-xs font-bold uppercase tracking-wider bg-gradient-to-r from-orange-500 to-red-500 px-3 py-1 rounded-full border border-white/30 shadow-lg">
                            {quiz.subject}
                          </span>
                        )}
                      </div>
                      <div className="text-white text-sm font-bold flex items-center space-x-3 drop-shadow-md">
                        <span className="flex items-center space-x-1 bg-white/20 px-2 py-1 rounded-lg">
                          <TbQuestionMark className="w-4 h-4" />
                          <span>{quiz.questions?.length || 0}</span>
                        </span>
                        <span className="flex items-center space-x-1 bg-white/20 px-2 py-1 rounded-lg">
                          <TbClock className="w-4 h-4" />
                          <span>{quiz.duration || 30}m</span>
                        </span>
                        <span className="flex items-center space-x-1 bg-white/20 px-2 py-1 rounded-lg">
                          <TbTarget className="w-4 h-4" />
                          <span>{quiz.passingMarks || 70}%</span>
                        </span>
                      </div>
                    </div>
                  </div>
                  <h3 className="text-xl font-bold mb-3 line-clamp-2 text-white drop-shadow-xl leading-tight" style={{ textShadow: '2px 2px 4px rgba(0,0,0,0.5)' }}>
                    {quiz.name}
                  </h3>
                  <p className="text-white text-sm line-clamp-2 font-medium leading-relaxed bg-black/20 px-3 py-2 rounded-lg backdrop-blur-sm border border-white/20">
                    {quiz.description || 'Test your knowledge with this comprehensive quiz and track your progress'}
                  </p>
                </div>
                {showResults && userResult && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className={`px-3 py-2 rounded-xl text-xs font-bold shadow-xl border backdrop-blur-sm ml-4 ${
                      userResult.verdict === 'Pass'
                        ? 'bg-emerald-500/90 text-white border-emerald-400/50'
                        : 'bg-red-500/90 text-white border-red-400/50'
                    }`}
                  >
                    <TbTrophy className="w-4 h-4 inline mr-1" />
                    {userResult.verdict}
                  </motion.div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Stats Section */}
        <div className="p-6 pb-4 bg-gradient-to-br from-gray-50/50 to-white relative">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-blue-500 to-purple-600"></div>
          </div>

          <div className="relative z-10">
            {/* Quick Stats Row */}
            <div className="grid grid-cols-3 gap-3 mb-6">
              <motion.div
                whileHover={{ scale: 1.05, y: -2 }}
                className="bg-gradient-to-br from-blue-50 via-blue-100/80 to-blue-200/60 rounded-2xl p-4 text-center border border-blue-200/70 hover:border-blue-400/70 transition-all duration-300 hover:shadow-xl group relative overflow-hidden"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-blue-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative z-10">
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <TbQuestionMark className="w-5 h-5 text-white" />
                  </div>
                  <div className="text-2xl font-bold text-blue-700 mb-1">{quiz.questions?.length || 0}</div>
                  <div className="text-xs text-blue-600 font-semibold uppercase tracking-wide">Questions</div>
                </div>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.05, y: -2 }}
                className="bg-gradient-to-br from-emerald-50 via-emerald-100/80 to-emerald-200/60 rounded-2xl p-4 text-center border border-emerald-200/70 hover:border-emerald-400/70 transition-all duration-300 hover:shadow-xl group relative overflow-hidden"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-emerald-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative z-10">
                  <div className="w-10 h-10 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <TbClock className="w-5 h-5 text-white" />
                  </div>
                  <div className="text-2xl font-bold text-emerald-700 mb-1">{quiz.duration || 30}</div>
                  <div className="text-xs text-emerald-600 font-semibold uppercase tracking-wide">Minutes</div>
                </div>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.05, y: -2 }}
                className="bg-gradient-to-br from-purple-50 via-purple-100/80 to-purple-200/60 rounded-2xl p-4 text-center border border-purple-200/70 hover:border-purple-400/70 transition-all duration-300 hover:shadow-xl group relative overflow-hidden"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-purple-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative z-10">
                  <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <TbStar className="w-5 h-5 text-white" />
                  </div>
                  <div className="text-2xl font-bold text-purple-700 mb-1">{quiz.passingMarks || 70}</div>
                  <div className="text-xs text-purple-600 font-semibold uppercase tracking-wide">Pass %</div>
                </div>
              </motion.div>
            </div>
          </div>

          {/* Enhanced Subject & Difficulty Display */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              {quiz.subject && (
                <motion.span
                  whileHover={{ scale: 1.05 }}
                  className="inline-flex items-center px-4 py-3 rounded-xl text-sm font-bold bg-gradient-to-r from-orange-500 via-red-500 to-pink-500 text-white shadow-xl border-2 border-white/30 backdrop-blur-sm"
                  style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }}
                >
                  <TbBrain className="w-5 h-5 mr-2 drop-shadow-md" />
                  {quiz.subject}
                </motion.span>
              )}
              {quiz.difficulty && (
                <motion.span
                  whileHover={{ scale: 1.05 }}
                  className={`inline-flex items-center px-3 py-2 rounded-xl text-sm font-bold shadow-lg border-2 border-white/30 ${getDifficultyColor(quiz.difficulty)}`}
                  style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }}
                >
                  {quiz.difficulty}
                </motion.span>
              )}
            </div>

            {quiz.attempts > 0 && (
              <div className="flex items-center space-x-2 bg-gray-100 px-3 py-2 rounded-xl border border-gray-200">
                <TbUsers className="w-4 h-4 text-gray-600" />
                <span className="text-sm font-semibold text-gray-700">{quiz.attempts} attempts</span>
              </div>
            )}
          </div>

          {/* Enhanced User Results Section */}
          {showResults && userResult && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-gradient-to-br from-emerald-50 via-green-50 to-emerald-100/50 border-2 border-emerald-200/70 rounded-2xl p-5 mb-6 relative overflow-hidden"
            >
              {/* Background Pattern */}
              <div className="absolute inset-0 opacity-10">
                <div className="absolute top-0 right-0 w-20 h-20 bg-emerald-500 rounded-full -translate-y-10 translate-x-10"></div>
                <div className="absolute bottom-0 left-0 w-16 h-16 bg-green-500 rounded-full translate-y-8 -translate-x-8"></div>
              </div>

              <div className="relative z-10">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center shadow-lg">
                      <TbTrophy className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <span className="text-sm font-bold text-emerald-900" style={{ textShadow: '1px 1px 2px rgba(255,255,255,0.5)' }}>Your Best Score</span>
                      <div className="text-xs text-emerald-800 font-semibold" style={{ textShadow: '1px 1px 2px rgba(255,255,255,0.5)' }}>
                        {userResult.correctAnswers}/{userResult.totalQuestions} correct answers
                      </div>
                    </div>
                  </div>
                  <div className="text-3xl font-black text-emerald-900 drop-shadow-lg" style={{ textShadow: '2px 2px 4px rgba(255,255,255,0.5)' }}>
                    {userResult.percentage}%
                  </div>
                </div>

                {/* Progress Bar */}
                <div className="w-full bg-emerald-200/50 rounded-full h-3 mb-3 overflow-hidden">
                  <motion.div
                    initial={{ width: 0 }}
                    animate={{ width: `${userResult.percentage}%` }}
                    transition={{ duration: 1, ease: "easeOut" }}
                    className="h-full bg-gradient-to-r from-emerald-500 to-green-500 rounded-full shadow-sm"
                  />
                </div>

                <div className="flex items-center justify-between text-xs">
                  <span className="font-semibold text-emerald-800" style={{ textShadow: '1px 1px 2px rgba(255,255,255,0.5)' }}>
                    Completed {new Date(userResult.completedAt).toLocaleDateString()}
                  </span>
                  <span className={`px-3 py-1 rounded-full font-bold shadow-md ${
                    userResult.verdict === 'Pass'
                      ? 'bg-gradient-to-r from-emerald-600 to-green-600 text-white border border-emerald-400'
                      : 'bg-gradient-to-r from-red-600 to-pink-600 text-white border border-red-400'
                  }`} style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.3)' }}>
                    {userResult.verdict}
                  </span>
                </div>
              </div>
            </motion.div>
          )}
        </div>

        {/* Enhanced Action Buttons */}
        <div className="px-6 pb-6 bg-gradient-to-br from-gray-50/80 to-white border-t border-gray-200/30 relative">
          {/* Background Glow */}
          <div className="absolute inset-0 bg-gradient-to-t from-blue-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

          <div className="relative z-10 pt-6">
            <div className="flex space-x-3">
              <motion.div className="flex-1">
                <Button
                  variant="primary"
                  size="md"
                  className="w-full bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-700 hover:from-blue-700 hover:via-indigo-700 hover:to-purple-800 border-0 shadow-xl hover:shadow-2xl transform hover:scale-105 hover:-translate-y-2 transition-all duration-400 font-bold text-white relative overflow-hidden group"
                  onClick={onStart}
                  icon={<TbPlayerPlay className="group-hover:scale-125 group-hover:rotate-12 transition-all duration-300" />}
                >
                  <span className="relative z-10 flex items-center justify-center space-x-2">
                    <span>{showResults && userResult ? 'Retake Quiz' : 'Start Quiz'}</span>
                    <TbChevronRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
                  </span>

                  {/* Animated Background */}
                  <div className="absolute inset-0 bg-gradient-to-r from-white/20 via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                  {/* Shine Effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 translate-x-[-200%] group-hover:translate-x-[200%] transition-transform duration-700"></div>
                </Button>
              </motion.div>

              {showResults && onView && (
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.2 }}
                >
                  <Button
                    variant="secondary"
                    size="md"
                    className="bg-white/90 backdrop-blur-sm border-2 border-indigo-200/70 text-indigo-600 hover:bg-indigo-50 hover:border-indigo-400 hover:text-indigo-700 transform hover:scale-105 hover:-translate-y-2 transition-all duration-400 shadow-lg hover:shadow-xl font-semibold relative overflow-hidden group"
                    onClick={onView}
                    icon={<TbTrophy className="group-hover:scale-110 group-hover:rotate-12 transition-all duration-300 text-yellow-500" />}
                  >
                    <span className="relative z-10">Results</span>
                    <div className="absolute inset-0 bg-gradient-to-r from-indigo-500/10 to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </Button>
                </motion.div>
              )}
            </div>

            {/* Quick Action Hint */}
            <div className="mt-4 text-center">
              <span className="text-xs text-gray-700 font-semibold opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-gray-100 px-3 py-1 rounded-full">
                Click to start your learning journey
              </span>
            </div>
          </div>
        </div>

        {/* Enhanced Progress Section */}
        {quiz.progress && quiz.progress > 0 && quiz.progress < 100 && (
          <div className="px-6 pb-4 bg-gradient-to-br from-gray-50/50 to-white">
            <div className="flex items-center justify-between text-sm mb-3">
              <span className="font-semibold flex items-center space-x-2 text-gray-800">
                <TbTarget className="w-4 h-4 text-blue-600" />
                <span>Learning Progress</span>
              </span>
              <span className="font-bold text-blue-700 bg-blue-100 px-2 py-1 rounded-lg">{quiz.progress}%</span>
            </div>
            <div className="w-full bg-gray-200/70 rounded-full h-3 overflow-hidden shadow-inner">
              <motion.div
                initial={{ width: 0 }}
                animate={{ width: `${quiz.progress}%` }}
                transition={{ duration: 1, ease: "easeOut" }}
                className="h-full bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 rounded-full shadow-sm relative"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-white/30 to-transparent rounded-full"></div>
              </motion.div>
            </div>
            <div className="mt-2 text-xs text-center">
              <span className="text-gray-700 font-medium bg-gray-100 px-3 py-1 rounded-full">
                Keep going! You're making great progress.
              </span>
            </div>
          </div>
        )}

        {/* Enhanced Hover Effects */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-indigo-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none rounded-2xl"
          whileHover={{ opacity: 1 }}
        />

        {/* Floating Action Indicator */}
        <motion.div
          initial={{ opacity: 0, scale: 0 }}
          whileHover={{ opacity: 1, scale: 1 }}
          className="absolute top-4 right-4 w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center shadow-lg pointer-events-none"
        >
          <TbChevronRight className="w-4 h-4 text-white" />
        </motion.div>
      </Card>
    </motion.div>
  );
};

export const QuizGrid = ({ quizzes, onQuizStart, onQuizView, showResults = false, userResults = {}, className = '' }) => {
  return (
    <div className={`quiz-grid-container ${className}`}>
      {quizzes.map((quiz, index) => (
        <motion.div
          key={quiz._id || index}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: Math.min(index * 0.1, 0.8) }}
          className="h-full"
        >
          <QuizCard
            quiz={quiz}
            onStart={() => onQuizStart(quiz)}
            onView={onQuizView ? () => onQuizView(quiz) : undefined}
            showResults={showResults}
            userResult={userResults[quiz._id]}
            className="h-full"
          />
        </motion.div>
      ))}
    </div>
  );
};

export default QuizCard;
