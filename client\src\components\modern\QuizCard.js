import React from 'react';
import { motion } from 'framer-motion';
import { Tb<PERSON><PERSON>, TbQuestionMark, Tb<PERSON><PERSON><PERSON>, TbTrophy, TbPlayerPlay } from 'react-icons/tb';
import { Card, Button } from './index';

const QuizCard = ({
  quiz,
  onStart,
  onView,
  showResults = false,
  userResult = null,
  className = '',
  ...props
}) => {
  const getDifficultyColor = (difficulty) => {
    switch (difficulty?.toLowerCase()) {
      case 'easy':
        return 'bg-green-100 text-green-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'hard':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getScoreColor = (percentage) => {
    if (percentage >= 80) return 'text-green-600';
    if (percentage >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -8, scale: 1.02 }}
      transition={{ duration: 0.3 }}
      className={`quiz-card-modern ${className}`}
    >
      <Card
        interactive
        variant="default"
        className="quiz-card overflow-hidden h-full border-0 shadow-lg hover:shadow-xl transition-all duration-300"
        {...props}
      >
        <div className="bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 p-6 text-white relative overflow-hidden">
          {/* Enhanced Background Pattern */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-0 right-0 w-32 h-32 bg-white rounded-full -translate-y-16 translate-x-16 animate-pulse"></div>
            <div className="absolute bottom-0 left-0 w-24 h-24 bg-white rounded-full translate-y-12 -translate-x-12 animate-pulse"></div>
            <div className="absolute top-1/2 left-1/2 w-16 h-16 bg-white/5 rounded-full -translate-x-8 -translate-y-8"></div>
          </div>

          {/* Gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-transparent via-blue-600/20 to-indigo-900/30"></div>

          <div className="relative z-10">
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-3">
                  <div className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center border border-white/20 shadow-lg">
                    <TbQuestionMark className="w-5 h-5 text-white" />
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {quiz.class && (
                      <span className="px-3 py-1 rounded-full text-xs font-semibold bg-white/25 text-white backdrop-blur-sm border border-white/20 shadow-sm">
                        Class {quiz.class}
                      </span>
                    )}
                    {quiz.difficulty && (
                      <span className="px-3 py-1 rounded-full text-xs font-semibold bg-white/25 text-white backdrop-blur-sm border border-white/20 shadow-sm">
                        {quiz.difficulty}
                      </span>
                    )}
                  </div>
                </div>
                <h3 className="text-xl font-bold mb-2 line-clamp-2 text-white drop-shadow-sm">{quiz.name}</h3>
                <p className="text-blue-100 text-sm line-clamp-2 opacity-90 font-medium">
                  {quiz.description || 'Test your knowledge with this comprehensive quiz'}
                </p>
              </div>
              {showResults && userResult && (
                <div className={`px-4 py-2 rounded-full text-xs font-bold shadow-lg border backdrop-blur-sm ml-4 ${
                  userResult.verdict === 'Pass'
                    ? 'bg-green-500/90 text-white border-green-400/50'
                    : 'bg-red-500/90 text-white border-red-400/50'
                }`}>
                  {userResult.verdict}
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="p-6 pb-4 bg-gradient-to-br from-white to-gray-50/50">
          <div className="grid grid-cols-3 gap-4 mb-6">
            <div className="bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-xl p-4 text-center border border-blue-200/50 hover:border-blue-300 transition-all duration-300 hover:shadow-lg hover:-translate-y-1 group">
              <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center mx-auto mb-2 group-hover:scale-110 transition-transform duration-300">
                <TbQuestionMark className="w-4 h-4 text-white" />
              </div>
              <div className="text-xl font-bold text-blue-700">{quiz.questions?.length || 0}</div>
              <div className="text-xs text-blue-600 font-semibold">Questions</div>
            </div>

            <div className="bg-gradient-to-br from-green-50 to-green-100/50 rounded-xl p-4 text-center border border-green-200/50 hover:border-green-300 transition-all duration-300 hover:shadow-lg hover:-translate-y-1 group">
              <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center mx-auto mb-2 group-hover:scale-110 transition-transform duration-300">
                <TbClock className="w-4 h-4 text-white" />
              </div>
              <div className="text-xl font-bold text-green-700">{quiz.duration || 30}</div>
              <div className="text-xs text-green-600 font-semibold">Minutes</div>
            </div>

            <div className="bg-gradient-to-br from-purple-50 to-purple-100/50 rounded-xl p-4 text-center border border-purple-200/50 hover:border-purple-300 transition-all duration-300 hover:shadow-lg hover:-translate-y-1 group">
              <div className="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center mx-auto mb-2 group-hover:scale-110 transition-transform duration-300">
                <TbUsers className="w-4 h-4 text-white" />
              </div>
              <div className="text-xl font-bold text-purple-700">{quiz.attempts || 0}</div>
              <div className="text-xs text-purple-600 font-semibold">Attempts</div>
            </div>
          </div>

          {quiz.subject && (
            <div className="mb-4">
              <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-gradient-to-r from-primary-100 to-blue-100 text-primary-800 border border-primary-200">
                📚 {quiz.subject}
              </span>
            </div>
          )}

          {showResults && userResult && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-4 mb-4"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <TbTrophy className="w-5 h-5 text-yellow-500" />
                  <span className="text-sm font-semibold text-gray-700">Your Best Score</span>
                </div>
                <div className={`text-xl font-bold ${getScoreColor(userResult.percentage)}`}>
                  {userResult.percentage}%
                </div>
              </div>
              <div className="mt-2 text-xs text-gray-600 flex items-center space-x-2">
                <span>{userResult.correctAnswers}/{userResult.totalQuestions} correct</span>
                <span>•</span>
                <span>Completed {new Date(userResult.completedAt).toLocaleDateString()}</span>
              </div>
            </motion.div>
          )}
        </div>

        <div className="px-6 pb-6 bg-gradient-to-br from-gray-50 to-gray-100/50 border-t border-gray-200/50">
          <div className="flex space-x-3 pt-6">
            <Button
              variant="primary"
              size="md"
              className="flex-1 bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-700 hover:from-blue-700 hover:via-blue-800 hover:to-indigo-800 border-0 shadow-lg hover:shadow-xl transform hover:scale-105 hover:-translate-y-1 transition-all duration-300 font-semibold text-white relative overflow-hidden group"
              onClick={onStart}
              icon={<TbPlayerPlay className="group-hover:scale-110 transition-transform duration-300" />}
            >
              <span className="relative z-10">
                {showResults && userResult ? 'Retake Quiz' : 'Start Quiz'}
              </span>
              <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </Button>

            {showResults && onView && (
              <Button
                variant="secondary"
                size="md"
                className="bg-white border-2 border-blue-200 text-blue-600 hover:bg-blue-50 hover:border-blue-400 hover:text-blue-700 transform hover:scale-105 hover:-translate-y-1 transition-all duration-300 shadow-sm hover:shadow-md"
                onClick={onView}
                icon={<TbTrophy className="hover:scale-110 transition-transform duration-300" />}
              >
                View Results
              </Button>
            )}
          </div>
        </div>

        {quiz.progress && quiz.progress > 0 && quiz.progress < 100 && (
          <div className="px-6 pb-4">
            <div className="flex items-center justify-between text-xs text-gray-600 mb-2">
              <span>Progress</span>
              <span>{quiz.progress}%</span>
            </div>
            <div className="progress-bar">
              <motion.div
                initial={{ width: 0 }}
                animate={{ width: `${quiz.progress}%` }}
                transition={{ duration: 0.5 }}
                className="progress-fill"
              />
            </div>
          </div>
        )}

        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-primary-600/5 to-blue-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"
          whileHover={{ opacity: 1 }}
        />
      </Card>
    </motion.div>
  );
};

export const QuizGrid = ({ quizzes, onQuizStart, onQuizView, showResults = false, userResults = {}, className = '' }) => {
  return (
    <div className={`quiz-grid-container ${className}`}>
      {quizzes.map((quiz, index) => (
        <motion.div
          key={quiz._id || index}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: Math.min(index * 0.1, 0.8) }}
          className="h-full"
        >
          <QuizCard
            quiz={quiz}
            onStart={() => onQuizStart(quiz)}
            onView={onQuizView ? () => onQuizView(quiz) : undefined}
            showResults={showResults}
            userResult={userResults[quiz._id]}
            className="h-full"
          />
        </motion.div>
      ))}
    </div>
  );
};

export default QuizCard;
