/* Premium user glow effects */
.premium-glow {
    position: relative;
}

.premium-glow::before {
    content: '';
    position: absolute;
    inset: -2px;
    padding: 2px;
    background: linear-gradient(45deg, #fbbf24, #f59e0b, #d97706, #fbbf24);
    border-radius: 50%;
    background-size: 200% 200%;
    animation: premium-pulse 3s ease-in-out infinite;
    z-index: -1;
}

.premium-glow::after {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(45deg, #fbbf24, #f59e0b, #d97706, #fbbf24);
    border-radius: 50%;
    background-size: 200% 200%;
    animation: premium-rotate 4s linear infinite;
    opacity: 0.7;
    filter: blur(8px);
    z-index: -2;
}

@keyframes premium-pulse {
    0%, 100% {
        background-position: 0% 50%;
        transform: scale(1);
    }
    50% {
        background-position: 100% 50%;
        transform: scale(1.05);
    }
}

@keyframes premium-rotate {
    0% {
        background-position: 0% 50%;
        transform: rotate(0deg);
    }
    100% {
        background-position: 100% 50%;
        transform: rotate(360deg);
    }
}

/* Subtle hover effects */
.ranking-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.ranking-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Avatar border gradients */
.avatar-premium {
    background: linear-gradient(45deg, #fbbf24, #f59e0b, #d97706);
    padding: 2px;
    border-radius: 50%;
}

.avatar-free {
    background: linear-gradient(45deg, #3b82f6, #1d4ed8);
    padding: 2px;
    border-radius: 50%;
}

.avatar-expired {
    background: linear-gradient(45deg, #ef4444, #dc2626);
    padding: 2px;
    border-radius: 50%;
}

/* Rank badge styles */
.rank-badge {
    transition: all 0.2s ease-in-out;
}

.rank-badge:hover {
    transform: scale(1.1);
}

/* Current user highlight */
.current-user-card {
    background: linear-gradient(135deg, #dbeafe 0%, #e0e7ff 100%);
    border: 2px solid #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Subscription status badges */
.status-premium {
    background: linear-gradient(45deg, #fbbf24, #f59e0b);
    color: white;
    font-weight: 600;
}

.status-free {
    background: linear-gradient(45deg, #dbeafe, #bfdbfe);
    color: #1e40af;
    font-weight: 500;
}

.status-expired {
    background: linear-gradient(45deg, #fee2e2, #fecaca);
    color: #dc2626;
    font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 640px) {
    .ranking-card {
        padding: 12px;
    }
    
    .premium-glow::before,
    .premium-glow::after {
        inset: -1px;
    }
}

/* Loading skeleton animation */
@keyframes skeleton-loading {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: skeleton-loading 1.5s infinite;
}

/* Smooth transitions for all interactive elements */
* {
    transition-property: transform, box-shadow, background-color, border-color;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

/* Focus states for accessibility */
.ranking-card:focus-within {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

button:focus-visible {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .premium-glow::before,
    .premium-glow::after {
        opacity: 1;
        filter: none;
    }
    
    .current-user-card {
        border-width: 3px;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .premium-glow::before,
    .premium-glow::after {
        animation: none;
    }
    
    .ranking-card {
        transition: none;
    }
    
    * {
        transition: none !important;
        animation: none !important;
    }
}
