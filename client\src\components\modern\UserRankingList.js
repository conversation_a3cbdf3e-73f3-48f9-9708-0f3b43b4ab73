import React, { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { TbSearch, TbFilter, TbUser, TbUsers, TbTrophy } from 'react-icons/tb';
import UserRankingCard from './UserRankingCard';

const UserRankingList = ({ 
    users = [], 
    currentUserId = null,
    layout = 'horizontal', // 'horizontal', 'vertical', 'grid'
    size = 'medium',
    showSearch = true,
    showFilters = true,
    showStats = true,
    className = ''
}) => {
    const [searchTerm, setSearchTerm] = useState('');
    const [filterStatus, setFilterStatus] = useState('all'); // 'all', 'premium', 'free', 'expired'
    const [sortBy, setSortBy] = useState('rank'); // 'rank', 'points', 'name'
    const currentUserRef = useRef(null);

    // Filter and sort users
    const filteredUsers = users
        .filter(user => {
            const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase());
            const matchesFilter = filterStatus === 'all' || 
                (filterStatus === 'premium' && (user.subscriptionStatus === 'active' || user.subscriptionStatus === 'premium')) ||
                (filterStatus === 'free' && user.subscriptionStatus === 'free') ||
                (filterStatus === 'expired' && user.subscriptionStatus === 'expired');
            return matchesSearch && matchesFilter;
        })
        .sort((a, b) => {
            switch (sortBy) {
                case 'points':
                    return (b.totalPoints || 0) - (a.totalPoints || 0);
                case 'name':
                    return a.name.localeCompare(b.name);
                case 'rank':
                default:
                    return (a.rank || 0) - (b.rank || 0);
            }
        });

    // Scroll to current user
    const scrollToCurrentUser = () => {
        if (currentUserRef.current) {
            currentUserRef.current.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });
        }
    };

    // Get layout classes
    const getLayoutClasses = () => {
        switch (layout) {
            case 'vertical':
                return 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4';
            case 'grid':
                return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4';
            case 'horizontal':
            default:
                return 'space-y-3';
        }
    };

    // Container animation variants
    const containerVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1
            }
        }
    };

    // Stats summary
    const totalUsers = users.length;
    const premiumUsers = users.filter(u => u.subscriptionStatus === 'active' || u.subscriptionStatus === 'premium').length;
    const topScore = users.length > 0 ? Math.max(...users.map(u => u.totalPoints || 0)) : 0;

    return (
        <div className={`space-y-6 ${className}`}>
            {/* Header with Stats */}
            {showStats && (
                <motion.div
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-200"
                >
                    <div className="flex items-center justify-between mb-4">
                        <h2 className="text-2xl font-bold text-gray-900 flex items-center space-x-2">
                            <TbTrophy className="w-6 h-6 text-yellow-500" />
                            <span>Leaderboard</span>
                        </h2>
                        
                        {currentUserId && (
                            <motion.button
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                onClick={scrollToCurrentUser}
                                className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2"
                            >
                                <TbUser className="w-4 h-4" />
                                <span>Find Me</span>
                            </motion.button>
                        )}
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                        <div className="bg-white rounded-lg p-4 border border-gray-200">
                            <div className="flex items-center space-x-2">
                                <TbUsers className="w-5 h-5 text-blue-500" />
                                <span className="text-sm text-gray-600">Total Users</span>
                            </div>
                            <div className="text-2xl font-bold text-gray-900 mt-1">{totalUsers}</div>
                        </div>
                        
                        <div className="bg-white rounded-lg p-4 border border-gray-200">
                            <div className="flex items-center space-x-2">
                                <TbTrophy className="w-5 h-5 text-yellow-500" />
                                <span className="text-sm text-gray-600">Premium Users</span>
                            </div>
                            <div className="text-2xl font-bold text-gray-900 mt-1">{premiumUsers}</div>
                        </div>
                        
                        <div className="bg-white rounded-lg p-4 border border-gray-200">
                            <div className="flex items-center space-x-2">
                                <TbUser className="w-5 h-5 text-green-500" />
                                <span className="text-sm text-gray-600">Top Score</span>
                            </div>
                            <div className="text-2xl font-bold text-gray-900 mt-1">{topScore}</div>
                        </div>
                    </div>
                </motion.div>
            )}

            {/* Search and Filters */}
            {(showSearch || showFilters) && (
                <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-white rounded-xl p-4 border border-gray-200 shadow-sm"
                >
                    <div className="flex flex-col sm:flex-row gap-4">
                        {/* Search */}
                        {showSearch && (
                            <div className="flex-1">
                                <div className="relative">
                                    <TbSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                                    <input
                                        type="text"
                                        placeholder="Search users..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                        className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    />
                                </div>
                            </div>
                        )}

                        {/* Filters */}
                        {showFilters && (
                            <div className="flex gap-2">
                                <select
                                    value={filterStatus}
                                    onChange={(e) => setFilterStatus(e.target.value)}
                                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                >
                                    <option value="all">All Users</option>
                                    <option value="premium">Premium</option>
                                    <option value="free">Free</option>
                                    <option value="expired">Expired</option>
                                </select>

                                <select
                                    value={sortBy}
                                    onChange={(e) => setSortBy(e.target.value)}
                                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                >
                                    <option value="rank">By Rank</option>
                                    <option value="points">By Points</option>
                                    <option value="name">By Name</option>
                                </select>
                            </div>
                        )}
                    </div>
                </motion.div>
            )}

            {/* User List */}
            <motion.div
                variants={containerVariants}
                initial="hidden"
                animate="visible"
                className={getLayoutClasses()}
            >
                <AnimatePresence>
                    {filteredUsers.map((user, index) => {
                        const isCurrentUser = user.userId === currentUserId || user._id === currentUserId;
                        const rank = index + 1;
                        
                        return (
                            <motion.div
                                key={user.userId || user._id}
                                ref={isCurrentUser ? currentUserRef : null}
                                layout
                                initial={{ opacity: 0, scale: 0.9 }}
                                animate={{ opacity: 1, scale: 1 }}
                                exit={{ opacity: 0, scale: 0.9 }}
                                transition={{ duration: 0.2 }}
                            >
                                <UserRankingCard
                                    user={user}
                                    rank={rank}
                                    isCurrentUser={isCurrentUser}
                                    layout={layout}
                                    size={size}
                                    showStats={showStats}
                                />
                            </motion.div>
                        );
                    })}
                </AnimatePresence>
            </motion.div>

            {/* Empty State */}
            {filteredUsers.length === 0 && (
                <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="text-center py-12"
                >
                    <TbUsers className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No users found</h3>
                    <p className="text-gray-500">
                        {searchTerm || filterStatus !== 'all' 
                            ? 'Try adjusting your search or filters'
                            : 'No users available to display'
                        }
                    </p>
                </motion.div>
            )}

            {/* Floating Action Button for Current User */}
            {currentUserId && filteredUsers.length > 10 && (
                <motion.button
                    initial={{ opacity: 0, scale: 0 }}
                    animate={{ opacity: 1, scale: 1 }}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={scrollToCurrentUser}
                    className="fixed bottom-6 right-6 bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 z-50"
                    title="Find me in ranking"
                >
                    <TbUser className="w-6 h-6" />
                </motion.button>
            )}
        </div>
    );
};

export default UserRankingList;
