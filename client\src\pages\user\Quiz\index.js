import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { message } from 'antd';
import { Tb<PERSON><PERSON>, TbSearch, TbFilter } from 'react-icons/tb';
import { getAllExams } from '../../../apicalls/exams';
import { HideLoading, ShowLoading } from '../../../redux/loaderSlice';
import { QuizGrid } from '../../../components/modern';
import './responsive.css';
import './style.css';

const Quiz = () => {
  const [exams, setExams] = useState([]);
  const [filteredExams, setFilteredExams] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedClass, setSelectedClass] = useState('');
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.user);

  // Set default class filter to user's class
  const userClass = user?.class || '';

  useEffect(() => {
    const getExams = async () => {
      try {
        dispatch(ShowLoading());
        const response = await getAllExams();
        dispatch(HideLoading());

        if (response.success) {
          // Sort exams by creation date (newest first)
          const sortedExams = response.data.sort((a, b) => {
            return new Date(b.createdAt || b.date || 0) - new Date(a.createdAt || a.date || 0);
          });

          setExams(sortedExams);

          // Set default filter to user's class if available (with proper type conversion)
          if (userClass) {
            // Convert to string to match exam class format
            setSelectedClass(String(userClass));
          }
        } else {
          message.error(response.message);
        }
      } catch (error) {
        dispatch(HideLoading());
        message.error(error.message);
      }
    };

    getExams();
  }, [dispatch, userClass]);

  // Filter exams based on search term and selected class
  useEffect(() => {
    let filtered = [...exams];

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(exam =>
        exam.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        exam.subject?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by class (with proper type conversion)
    if (selectedClass) {
      filtered = filtered.filter(exam => {
        // Convert both to strings for comparison to handle number vs string mismatch
        return String(exam.class) === String(selectedClass);
      });
    }

    // Sort filtered results by newest first
    filtered.sort((a, b) => {
      return new Date(b.createdAt || b.date || 0) - new Date(a.createdAt || a.date || 0);
    });

    setFilteredExams(filtered);
  }, [exams, searchTerm, selectedClass]);

  // Get unique classes for filter dropdown
  const availableClasses = [...new Set(exams.map(exam => exam.class).filter(Boolean))].sort();

  const handleQuizStart = (quiz) => {
    navigate(`/quiz/${quiz._id}/start`);
  };

  return (
    <div className="quiz-listing-container">
      <div className="quiz-listing-content">


        {/* Enhanced Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="quiz-listing-header"
        >
          <div className="text-center mb-6">
            <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full mb-6 shadow-lg">
              <TbBrain className="w-10 h-10 text-white" />
            </div>
            <h1 className="heading-2 text-gradient mb-4">
              Challenge Your Mind
            </h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Test your knowledge with our comprehensive quizzes. Track your progress and improve your skills.
            </p>
            <div className="flex items-center justify-center space-x-6 mt-6 text-sm text-gray-500">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>{exams.length} Available Quizzes</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span>Your Class: {userClass || 'All Classes'}</span>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Search and Filter Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="mb-8"
        >
          <div className="max-w-4xl mx-auto">
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              {/* Search Box */}
              <div className="flex-1 relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <TbSearch className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search quizzes by name or subject..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-xl leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base"
                />
              </div>

              {/* Class Filter */}
              <div className="sm:w-48">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <TbFilter className="h-5 w-5 text-gray-400" />
                  </div>
                  <select
                    value={selectedClass}
                    onChange={(e) => setSelectedClass(e.target.value)}
                    className="block w-full pl-10 pr-8 py-3 border border-gray-300 rounded-xl bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base appearance-none"
                  >
                    <option value="">All Classes</option>
                    {availableClasses.map((className) => (
                      <option key={className} value={className}>
                        Class {className}
                      </option>
                    ))}
                  </select>
                  <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                    <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>

            {/* Enhanced Results Summary */}
            <div className="flex items-center justify-between bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl px-4 py-3 border border-blue-100">
              <div className="flex items-center space-x-3">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                  <span className="text-sm font-medium text-blue-700">
                    {filteredExams.length} quiz{filteredExams.length !== 1 ? 'es' : ''} found
                  </span>
                </div>
                {selectedClass && (
                  <div className="flex items-center space-x-2">
                    <span className="text-xs text-blue-600">•</span>
                    <span className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded-full">
                      Class {selectedClass}
                    </span>
                  </div>
                )}
                {searchTerm && (
                  <div className="flex items-center space-x-2">
                    <span className="text-xs text-blue-600">•</span>
                    <span className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded-full">
                      "{searchTerm}"
                    </span>
                  </div>
                )}
              </div>
              {(searchTerm || selectedClass) && (
                <button
                  onClick={() => {
                    setSearchTerm('');
                    setSelectedClass('');
                  }}
                  className="text-xs text-blue-600 hover:text-blue-800 font-medium transition-colors px-3 py-1 rounded-full hover:bg-blue-100"
                >
                  Clear filters
                </button>
              )}
            </div>
          </div>
        </motion.div>

        {/* Quiz Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          {filteredExams.length > 0 ? (
            <QuizGrid
              quizzes={filteredExams}
              onQuizStart={handleQuizStart}
              className="quiz-grid-container"
            />
          ) : exams.length > 0 ? (
            <div className="text-center py-16">
              <div className="max-w-md mx-auto">
                <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <TbSearch className="w-12 h-12 text-gray-400" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  No quizzes found
                </h3>
                <p className="text-gray-500 mb-6">
                  We couldn't find any quizzes matching your search criteria. Try adjusting your filters or search terms.
                </p>
                <div className="space-y-3">
                  <button
                    onClick={() => {
                      setSearchTerm('');
                      setSelectedClass('');
                    }}
                    className="w-full sm:w-auto px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors font-medium"
                  >
                    Show All Quizzes
                  </button>
                  <div className="text-sm text-gray-400">
                    or try searching for a different topic
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-16">
              <div className="max-w-md mx-auto">
                <div className="w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <TbBrain className="w-12 h-12 text-blue-500" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  No quizzes available yet
                </h3>
                <p className="text-gray-500 mb-6">
                  Quizzes will appear here once your instructor adds them. Check back soon!
                </p>
                <button
                  onClick={() => window.location.reload()}
                  className="px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors font-medium"
                >
                  Refresh Page
                </button>
              </div>
            </div>
          )}
        </motion.div>
      </div>


    </div>
  );
};

export default Quiz;
