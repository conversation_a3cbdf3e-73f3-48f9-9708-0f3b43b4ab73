.pass {
    background: var(--success-light) !important;
    border: 1px solid var(--success) !important;
    color: var(--success-dark) !important;
    border-radius: var(--radius-lg) !important;
    padding: var(--space-3) !important;
}

.pass-dark {
    background: var(--success) !important;
    color: white !important;
    box-shadow: var(--shadow-md) !important;
    border-radius: var(--radius-lg) !important;
    padding: var(--space-3) !important;
}

.fail {
    background: var(--danger-light) !important;
    border: 1px solid var(--danger) !important;
    color: var(--danger-dark) !important;
    border-radius: var(--radius-lg) !important;
    padding: var(--space-3) !important;
}

.fail-dark {
    background: var(--danger) !important;
    color: white !important;
    box-shadow: var(--shadow-md) !important;
    border-radius: var(--radius-lg) !important;
    padding: var(--space-3) !important;
}

.no-attempts {
    background: var(--primary-50) !important;
    border: 1px solid var(--primary) !important;
    color: var(--primary-dark) !important;
    border-radius: var(--radius-lg) !important;
    padding: var(--space-3) !important;
}

.no-attempts-dark {
    background: var(--primary) !important;
    color: white !important;
    box-shadow: var(--shadow-md) !important;
    border-radius: var(--radius-lg) !important;
    padding: var(--space-3) !important;
}

.card-design {
    border-radius: var(--radius-lg) !important;
    box-shadow: var(--shadow-md) !important;
    border: 1px solid var(--gray-200) !important;
    transition: var(--transition-normal) !important;
    background: var(--white) !important;
    padding: var(--space-4) !important;
}

.card-design:hover {
    transform: translateY(-2px) !important;
    box-shadow: var(--shadow-lg) !important;
    border-color: var(--primary) !important;
}

.box-tags {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 16px !important;
    margin: 0px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.box-tags-button {
    border-radius: 12px;
    padding: 12px 16px !important;
    margin: 0px;
    border: none;
    width: 160px;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.box-tags-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.box-tags-icon {
    margin-right: 8px;
    border-radius: 12px;
    width: 56px;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* ===== ENHANCED QUIZ CARD STYLES ===== */

.quiz-card-modern {
    position: relative;
    height: 100%;
    max-width: 100%;
    perspective: 1000px;
}

.quiz-card-modern .quiz-card {
    border-radius: 1.25rem;
    overflow: hidden;
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    box-shadow:
        0 10px 25px -5px rgba(0, 123, 255, 0.1),
        0 8px 10px -6px rgba(0, 123, 255, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.05);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    height: 100%;
    display: flex;
    flex-direction: column;
    border: 1px solid rgba(0, 123, 255, 0.08);
    backdrop-filter: blur(10px);
    position: relative;
}

.quiz-card-modern:hover .quiz-card {
    box-shadow:
        0 25px 50px -12px rgba(0, 123, 255, 0.25),
        0 20px 25px -5px rgba(0, 123, 255, 0.1),
        0 0 0 1px rgba(0, 123, 255, 0.1);
    transform: translateY(-8px) rotateX(2deg);
    border-color: rgba(0, 123, 255, 0.2);
}

.quiz-card-modern .quiz-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(135deg, #007BFF 0%, #0056D2 50%, #00D4FF 100%);
    z-index: 1;
    border-radius: 1.25rem 1.25rem 0 0;
}

.quiz-card-modern .quiz-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.02) 0%, rgba(0, 86, 210, 0.05) 100%);
    z-index: 0;
    border-radius: 1.25rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.quiz-card-modern:hover .quiz-card::after {
    opacity: 1;
}

/* Quiz card header gradient */
.quiz-card-modern .quiz-card .bg-gradient-to-r {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);
    border-radius: 1rem 1rem 0 0;
}

.quiz-card-modern .quiz-card .bg-gradient-to-r::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.quiz-card-modern:hover .quiz-card .bg-gradient-to-r::before {
    left: 100%;
}

/* Stats boxes styling */
.quiz-card-modern .bg-gray-50 {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 1px solid rgba(0, 123, 255, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 0.75rem;
    backdrop-filter: blur(5px);
}

.quiz-card-modern .bg-gray-50:hover {
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.05) 0%, rgba(0, 86, 210, 0.08) 100%);
    transform: translateY(-3px) scale(1.02);
    border-color: rgba(0, 123, 255, 0.2);
    box-shadow: 0 8px 25px -8px rgba(0, 123, 255, 0.2);
}

/* Button enhancements */
.quiz-card-modern .btn {
    font-weight: 600;
    letter-spacing: 0.025em;
    border-radius: 0.75rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.quiz-card-modern .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.quiz-card-modern .btn:hover::before {
    left: 100%;
}

.quiz-card-modern .btn:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 10px 25px -5px rgba(0, 123, 255, 0.4);
}

/* Subject badge styling */
.quiz-card-modern .bg-gradient-to-r.from-primary-100 {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
    border-radius: 0.75rem;
    border: 1px solid rgba(59, 130, 246, 0.1);
}

/* Result card styling */
.quiz-card-modern .bg-gradient-to-r.from-green-50 {
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
    box-shadow: 0 4px 15px rgba(34, 197, 94, 0.15);
    border-radius: 0.75rem;
    border: 1px solid rgba(34, 197, 94, 0.1);
}

/* ===== QUIZ GRID LAYOUT ===== */

.quiz-grid {
    display: grid;
    gap: 2rem;
    margin-top: 2.5rem;
    animation: fadeInUp 0.6s ease-out;
}

/* Responsive grid columns */
@media (min-width: 640px) {
    .quiz-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.75rem;
    }
}

@media (min-width: 1024px) {
    .quiz-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 2rem;
    }
}

@media (min-width: 1280px) {
    .quiz-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 2.25rem;
    }
}

/* Staggered animation for quiz cards */
.quiz-grid .quiz-card-modern:nth-child(1) { animation-delay: 0.1s; }
.quiz-grid .quiz-card-modern:nth-child(2) { animation-delay: 0.2s; }
.quiz-grid .quiz-card-modern:nth-child(3) { animation-delay: 0.3s; }
.quiz-grid .quiz-card-modern:nth-child(4) { animation-delay: 0.4s; }
.quiz-grid .quiz-card-modern:nth-child(5) { animation-delay: 0.5s; }
.quiz-grid .quiz-card-modern:nth-child(6) { animation-delay: 0.6s; }
.quiz-grid .quiz-card-modern:nth-child(7) { animation-delay: 0.7s; }
.quiz-grid .quiz-card-modern:nth-child(8) { animation-delay: 0.8s; }

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== LOADING AND EMPTY STATES ===== */

.quiz-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    animation: pulse 2s infinite;
}

.quiz-empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
    border-radius: 2rem;
    box-shadow: 0 20px 25px -5px rgba(0, 123, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 123, 255, 0.08);
    margin-top: 2rem;
}

.quiz-empty-state h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #374151;
    margin-bottom: 1rem;
}

.quiz-empty-state p {
    color: #6b7280;
    font-size: 1rem;
    line-height: 1.6;
}

/* ===== QUIZ QUESTION COMPONENT STYLES ===== */

/* Modern input styling */
.input-modern {
    width: 100%;
    padding: 1rem 1.25rem;
    border: 2px solid #e5e7eb;
    border-radius: 0.75rem;
    font-size: 1rem;
    font-weight: 500;
    color: #374151;
    background: #f9fafb;
    transition: all 0.2s ease;
    outline: none;
}

.input-modern:focus {
    border-color: #007BFF;
    background: white;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.input-modern::placeholder {
    color: #9ca3af;
    font-weight: 400;
}

/* Shadow utilities */
.shadow-medium {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Success and error colors */
.text-success-600 {
    color: #059669;
}

.text-success-700 {
    color: #047857;
}

.text-error-600 {
    color: #dc2626;
}

.bg-success-50 {
    background-color: #f0fdf4;
}

/* Container modern */
.container-modern {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Quiz question specific styles */
.quiz-question-wrapper {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    margin-bottom: 2rem;
}

/* Enhanced quiz option styles for better user experience */
.quiz-option {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    user-select: none;
    -webkit-tap-highlight-color: transparent;
}

.quiz-option:focus {
    outline: 2px solid #007BFF;
    outline-offset: 2px;
}

.quiz-option.selected {
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.15);
}

/* Option styling improvements */
.quiz-option-modern {
    background: #f9fafb;
    border: 2px solid #e5e7eb;
    border-radius: 0.75rem;
    padding: 1rem 1.25rem;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 1rem;
    font-weight: 500;
    color: #374151;
    position: relative;
    display: flex;
    align-items: center;
    min-height: 60px;
    margin-bottom: 0.75rem;
}

.quiz-option-modern:hover {
    border-color: #007BFF;
    background: rgba(0, 123, 255, 0.05);
    transform: translateY(-1px);
}

.quiz-option-modern.selected {
    border-color: #007BFF;
    background: rgba(0, 123, 255, 0.1);
}

.quiz-option-modern.correct {
    border-color: #059669;
    background: rgba(5, 150, 105, 0.1);
}

.quiz-option-modern.incorrect {
    border-color: #dc2626;
    background: rgba(220, 38, 38, 0.1);
}

/* ===== QUIZ START PAGE STYLES ===== */

.quiz-start-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
}

/* ===== QUIZ RESULT PAGE ENHANCEMENTS ===== */

.quiz-result-card {
    transition: all 0.3s ease;
}

.quiz-result-stat-card {
    transition: transform 0.2s ease;
}

.quiz-result-stat-card:hover {
    transform: translateY(-2px);
}

.quiz-result-btn {
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.quiz-result-btn:hover {
    transform: translateY(-1px);
}

.quiz-result-btn:active {
    transform: translateY(0);
}

.quiz-start-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 2rem;
    padding: 3rem;
    max-width: 600px;
    width: 100%;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.quiz-start-header {
    text-align: center;
    margin-bottom: 2rem;
}

.quiz-start-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: #1f2937;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.quiz-start-subtitle {
    font-size: 1.125rem;
    color: #6b7280;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.quiz-start-info {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.quiz-info-item {
    text-align: center;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 1rem;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.quiz-info-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

.quiz-info-icon {
    width: 2rem;
    height: 2rem;
    color: #007BFF;
    margin: 0 auto 1rem;
}

.quiz-info-value {
    font-size: 2rem;
    font-weight: 800;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.quiz-info-label {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 600;
}

.quiz-start-instructions {
    background: rgba(255, 243, 205, 0.5);
    border: 1px solid #f59e0b;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.quiz-start-instructions h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #92400e;
    margin-bottom: 1rem;
}

.quiz-start-instructions ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.quiz-start-instructions li {
    color: #92400e;
    margin-bottom: 0.5rem;
    font-size: 0.9375rem;
    line-height: 1.5;
}

.quiz-start-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.quiz-start-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem 2rem;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    min-width: 160px;
}

.quiz-start-btn.primary {
    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);
    color: white;
    box-shadow: 0 4px 14px 0 rgba(0, 123, 255, 0.25);
}

.quiz-start-btn.primary:hover {
    background: linear-gradient(135deg, #0056D2 0%, #004494 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(0, 123, 255, 0.35);
}

.quiz-start-btn.secondary {
    background: white;
    color: #6b7280;
    border: 2px solid #e5e7eb;
}

.quiz-start-btn.secondary:hover {
    background: #f9fafb;
    border-color: #d1d5db;
    transform: translateY(-2px);
}

/* ===== QUIZ RESULT PAGE STYLES ===== */

.quiz-result-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 2rem 1rem;
}

.quiz-result-content {
    max-width: 800px;
    margin: 0 auto;
}

.quiz-result-header {
    background: white;
    border-radius: 2rem;
    padding: 3rem;
    text-align: center;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
    border: 1px solid #e5e7eb;
    margin-bottom: 2rem;
}

.quiz-result-gif-container {
    margin-bottom: 2rem;
}

.quiz-result-gif {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid #e5e7eb;
}

.quiz-result-title {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.quiz-result-title.text-success {
    color: #059669;
}

.quiz-result-title.text-error {
    color: #dc2626;
}

.quiz-result-subtitle {
    font-size: 1.25rem;
    color: #6b7280;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.quiz-result-score {
    display: flex;
    justify-content: center;
    margin-bottom: 2rem;
}

.quiz-result-score-circle {
    width: 150px;
    height: 150px;
    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    box-shadow: 0 10px 25px -5px rgba(0, 123, 255, 0.3);
}

.quiz-result-score-value {
    font-size: 2.5rem;
    font-weight: 800;
    line-height: 1;
}

.quiz-result-score-label {
    font-size: 0.875rem;
    font-weight: 600;
    opacity: 0.9;
}