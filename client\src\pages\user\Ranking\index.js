import React, { useEffect, useState, useRef } from "react";
import './index.css'
import { motion, AnimatePresence } from "framer-motion";
import { getAllReportsForRanking } from "../../../apicalls/reports";
import { getUserInfo } from "../../../apicalls/users";
import { message } from "antd";
import PageTitle from "../../../components/PageTitle";
import { useDispatch } from "react-redux";
import { HideLoading, ShowLoading } from "../../../redux/loaderSlice";
import { Card, Button, Loading } from "../../../components/modern";
import image from '../../../assets/person.png';
import { IoPersonCircleOutline } from "react-icons/io5";
import {
  TbTrophy,
  TbMedal,
  TbCrown,
  TbUsers,
  TbSchool,
  TbStar,
  TbChartBar,
  TbUser,
  TbAward
} from "react-icons/tb";

const Ranking = () => {
    const [rankingData, setRankingData] = useState('');
    const [userRanking, setUserRanking] = useState('');
    const [userData, setUserData] = useState('');
    const [isAdmin, setIsAdmin] = useState(false);
    const [isMobile, setIsMobile] = useState(false);
    const [activeTab, setActiveTab] = useState("overall"); // "overall" or "class"
    const currentUserRef = useRef(null);

    const dispatch = useDispatch();

    // Function to scroll to current user
    const scrollToCurrentUser = () => {
        if (currentUserRef.current) {
            currentUserRef.current.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });
        }
    };

    const fetchReports = async () => {
        try {
            const response = await getAllReportsForRanking();
            if (response.success) {
                setRankingData(response.data);
            } else {
                message.error(response.message);
            }
        } catch (error) {
            message.error(error.message);
        }
    }


    const getUserData = async () => {
        try {
            const response = await getUserInfo();
            if (response.success) {
                if (response.data.isAdmin) {
                    setIsAdmin(true);
                } else {
                    setIsAdmin(false);
                    setUserData(response.data);
                    await fetchReports();
                    dispatch(HideLoading());
                }
            } else {
                message.error(response.message);
            }
        } catch (error) {
            message.error(error.message);
        }
    };

    useEffect(() => {
        if (window.innerWidth < 700) {
            setIsMobile(true);
        }
        else {
            setIsMobile(false);
        }
        if (localStorage.getItem("token")) {
            dispatch(ShowLoading());
            getUserData();
        }
    }, []);

    const getUserStats = () => {
        const Ranking = rankingData
            .map((user, index) => ({
                user,
                ranking: index + 1,
            }))
            .filter((item) => item.user.userId.includes(userData._id));
        setUserRanking(Ranking);
    }

    useEffect(() => {
        if (rankingData) {
            getUserStats();
        }
    }, [rankingData]);

    // Helper function to format user ID for mobile devices
    const formatMobileUserId = (userId) => {
        const prefix = userId.slice(0, 4);
        const suffix = userId.slice(-4);
        return `${prefix}.....${suffix}`;
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
            {!isAdmin && (
                <div className="container-modern py-8">
                    {/* Modern Header - Responsive */}
                    <motion.div
                        initial={{ opacity: 0, y: -20 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="text-center mb-6 sm:mb-8"
                    >
                        <div className="inline-flex items-center justify-center w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full mb-3 sm:mb-4">
                            <TbTrophy className="w-6 h-6 sm:w-7 sm:h-7 md:w-8 md:h-8 text-white" />
                        </div>
                        <h1 className="heading-2 text-gradient mb-3 sm:mb-4 text-2xl sm:text-3xl md:text-4xl">Leaderboard</h1>
                        <p className="text-base sm:text-lg md:text-xl text-gray-600">
                            See how you rank against other students
                        </p>
                    </motion.div>

                    {/* Modern Tabs - Responsive */}
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.2 }}
                        className="mb-6 sm:mb-8"
                    >
                        <Card className="p-2">
                            <div className="flex gap-2">
                                <motion.button
                                    whileHover={{ scale: 1.02 }}
                                    whileTap={{ scale: 0.98 }}
                                    className={`flex items-center space-x-1 sm:space-x-2 px-3 sm:px-6 py-2 sm:py-3 rounded-lg font-medium transition-all duration-200 flex-1 justify-center text-sm sm:text-base ${
                                        activeTab === "overall"
                                            ? 'bg-primary-600 text-white shadow-md'
                                            : 'text-gray-600 hover:bg-gray-100'
                                    }`}
                                    onClick={() => setActiveTab("overall")}
                                >
                                    <TbUsers className="w-4 h-4 sm:w-5 sm:h-5" />
                                    <span className="hidden sm:inline">Overall Ranking</span>
                                    <span className="sm:hidden">Overall</span>
                                    {activeTab === "overall" && (
                                        <motion.div
                                            layoutId="activeRankingTab"
                                            className="w-2 h-2 bg-white rounded-full"
                                        />
                                    )}
                                </motion.button>
                                <motion.button
                                    whileHover={{ scale: 1.02 }}
                                    whileTap={{ scale: 0.98 }}
                                    className={`flex items-center space-x-1 sm:space-x-2 px-3 sm:px-6 py-2 sm:py-3 rounded-lg font-medium transition-all duration-200 flex-1 justify-center text-sm sm:text-base ${
                                        activeTab === "class"
                                            ? 'bg-primary-600 text-white shadow-md'
                                            : 'text-gray-600 hover:bg-gray-100'
                                    }`}
                                    onClick={() => setActiveTab("class")}
                                >
                                    <TbSchool className="w-4 h-4 sm:w-5 sm:h-5" />
                                    <span className="hidden sm:inline">Class Ranking</span>
                                    <span className="sm:hidden">Class</span>
                                    {activeTab === "class" && (
                                        <motion.div
                                            layoutId="activeRankingTab"
                                            className="w-2 h-2 bg-white rounded-full"
                                        />
                                    )}
                                </motion.button>
                            </div>
                        </Card>
                    </motion.div>

                    {/* Pinned User Profile - Your Position */}
                    {userData && (
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.3 }}
                            className="mb-6"
                        >
                            <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-2 border-blue-200">
                                <div className="p-4">
                                    <div className="flex items-center justify-between mb-3">
                                        <h3 className="text-lg font-bold text-gray-800 flex items-center">
                                            <TbUser className="w-5 h-5 mr-2 text-blue-600" />
                                            Your Position
                                        </h3>
                                        <div className="flex items-center space-x-2">
                                            <span className="bg-blue-100 text-blue-800 text-xs font-bold px-2 py-1 rounded-full">
                                                #{userRanking && userRanking.length > 0 ? userRanking[0].ranking : 'N/A'}
                                            </span>
                                            <span className="bg-yellow-100 text-yellow-800 text-xs font-bold px-2 py-1 rounded-full">
                                                {userData.totalPoints || 0} pts
                                            </span>
                                        </div>
                                    </div>

                                    <div className="flex items-center space-x-4">
                                        {/* User's Profile Picture */}
                                        <div className={`w-14 h-14 rounded-full p-0.5 flex items-center justify-center flex-shrink-0 ${
                                            userData.subscriptionStatus === "active"
                                                ? 'bg-gradient-to-r from-green-400 to-green-600'
                                                : userData.subscriptionStatus === "free"
                                                ? 'bg-gradient-to-r from-blue-400 to-blue-600'
                                                : 'bg-gradient-to-r from-red-400 to-red-600'
                                        }`}>
                                            <div className="w-full h-full rounded-full overflow-hidden bg-gray-200 flex items-center justify-center">
                                                {userData.userPhoto ? (
                                                    <img
                                                        src={userData.userPhoto}
                                                        alt="profile"
                                                        className="w-full h-full object-cover"
                                                        onError={(e) => { e.target.src = image }}
                                                    />
                                                ) : (
                                                    <TbUser className="w-7 h-7 text-gray-400" />
                                                )}
                                            </div>
                                        </div>

                                        {/* User Info */}
                                        <div className="flex-1">
                                            <div className="flex items-center space-x-2 mb-1">
                                                <h4 className="font-bold text-gray-900">{userData.name}</h4>
                                                <span className="bg-gradient-to-r from-blue-500 to-purple-500 text-white text-xs px-2 py-1 rounded-full font-bold">You</span>
                                            </div>
                                            <div className="flex items-center space-x-4 text-sm text-gray-600">
                                                <span>Class {userData.class}</span>
                                                <span>•</span>
                                                <span>{userData.school}</span>
                                            </div>
                                        </div>

                                        {/* Quick Stats & Find Me Button */}
                                        <div className="text-right">
                                            <motion.button
                                                whileHover={{ scale: 1.05 }}
                                                whileTap={{ scale: 0.95 }}
                                                onClick={scrollToCurrentUser}
                                                className="bg-gradient-to-r from-blue-500 to-purple-500 text-white text-xs px-3 py-1.5 rounded-full font-bold mb-2 hover:shadow-md transition-all duration-200"
                                            >
                                                Find Me in List
                                            </motion.button>
                                            <div className="text-sm text-gray-600">Performance</div>
                                            <div className="flex items-center space-x-3 mt-1">
                                                <div className="text-center">
                                                    <div className="text-xs text-green-600 font-bold">{userData.passedExamsCount || 0}</div>
                                                    <div className="text-xs text-gray-500">Passed</div>
                                                </div>
                                                <div className="text-center">
                                                    <div className="text-xs text-blue-600 font-bold">{userData.quizzesTaken || 0}</div>
                                                    <div className="text-xs text-gray-500">Quizzes</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </Card>
                        </motion.div>
                    )}

                    {/* Modern Leaderboard */}
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.4 }}
                    >
                        {rankingData.length > 0 ? (
                            <Card className="overflow-hidden">
                                {/* Enhanced Leaderboard Header - Responsive */}
                                <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 text-white p-4 sm:p-6 relative overflow-hidden">
                                    {/* Background Pattern - Responsive */}
                                    <div className="absolute inset-0 opacity-10">
                                        <div className="absolute top-2 left-2 sm:top-4 sm:left-4 text-3xl sm:text-4xl md:text-6xl">🏆</div>
                                        <div className="absolute top-4 right-4 sm:top-8 sm:right-8 text-2xl sm:text-3xl md:text-4xl">⭐</div>
                                        <div className="absolute bottom-2 left-1/4 sm:bottom-4 text-xl sm:text-2xl md:text-3xl">🎯</div>
                                        <div className="absolute bottom-3 right-1/3 sm:bottom-6 text-3xl sm:text-4xl md:text-5xl">💎</div>
                                    </div>

                                    <div className="relative z-10">
                                        <div className="flex flex-col sm:flex-row items-center justify-center space-y-2 sm:space-y-0 sm:space-x-3 mb-3 sm:mb-2">
                                            <TbTrophy className="w-8 h-8 sm:w-10 sm:h-10 text-yellow-300 animate-bounce" />
                                            <h2 className="text-xl sm:text-2xl md:text-3xl font-black bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent text-center">
                                                {activeTab === "overall" ? "Global Leaderboard" : "Class Champions"}
                                            </h2>
                                            <TbTrophy className="w-8 h-8 sm:w-10 sm:h-10 text-yellow-300 animate-bounce hidden sm:block" style={{ animationDelay: '0.5s' }} />
                                        </div>
                                        <p className="text-center text-blue-100 text-sm sm:text-base md:text-lg font-semibold">
                                            {activeTab === "overall"
                                                ? "🌟 Elite performers from all classes competing for glory! 🌟"
                                                : `🎓 Class ${userData?.class || 'your class'} top achievers! 🎓`
                                            }
                                        </p>
                                        <div className="text-center mt-3">
                                            <span className="bg-white/20 px-3 py-2 sm:px-4 rounded-full text-xs sm:text-sm font-bold block sm:inline">
                                                💪 Earn points by acing quizzes • 🏅 Climb the ranks • 👑 Become a champion!
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                {/* Top 3 Podium - Responsive */}
                                <div className="bg-gradient-to-br from-blue-50 to-purple-50 p-3 sm:p-6 border-b">
                                    <h3 className="text-lg sm:text-xl font-bold text-center mb-4 sm:mb-6 text-gray-800">🏆 Champions Podium 🏆</h3>
                                    <div className="flex justify-center items-end space-x-2 sm:space-x-4 overflow-x-auto pb-2">
                                        {(activeTab === "overall"
                                            ? rankingData.slice(0, 3)
                                            : rankingData.filter(user => user.userClass === userData?.class).slice(0, 3)
                                        ).map((user, index) => {
                                            const positions = [1, 0, 2]; // Silver, Diamond, Bronze order for visual appeal
                                            const actualPosition = positions.indexOf(index);
                                            const heights = ['h-16 sm:h-20 md:h-24', 'h-20 sm:h-24 md:h-32', 'h-12 sm:h-16 md:h-20']; // Responsive heights for podium effect
                                            const badges = [
                                                { icon: "🥈", color: "from-gray-400 to-gray-600", title: "Silver" },
                                                { icon: "💎", color: "from-blue-400 to-cyan-600", title: "Diamond" },
                                                { icon: "🥉", color: "from-amber-400 to-orange-600", title: "Bronze" }
                                            ];

                                            return (
                                                <motion.div
                                                    key={user.userId}
                                                    initial={{ opacity: 0, y: 50 }}
                                                    animate={{ opacity: 1, y: 0 }}
                                                    transition={{ delay: index * 0.2 }}
                                                    className="text-center"
                                                >
                                                    {/* WhatsApp-style Podium Avatar */}
                                                    <div className="mb-2 sm:mb-3">
                                                        <div className={`w-16 h-16 mx-auto rounded-full p-1 shadow-lg ${
                                                            user.subscriptionStatus === "active"
                                                                ? 'bg-gradient-to-r from-green-400 to-green-600'
                                                                : user.subscriptionStatus === "free"
                                                                ? 'bg-gradient-to-r from-blue-400 to-blue-600'
                                                                : 'bg-gradient-to-r from-red-400 to-red-600'
                                                        }`}>
                                                            <div className="w-full h-full rounded-full overflow-hidden bg-gray-200 flex items-center justify-center">
                                                                {user.userPhoto ? (
                                                                    <img src={user.userPhoto} alt="profile" className="w-full h-full object-cover" />
                                                                ) : (
                                                                    <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                                                                        <TbUser className="w-8 h-8 text-gray-400" />
                                                                    </div>
                                                                )}
                                                            </div>
                                                        </div>
                                                        <div className="text-lg sm:text-xl md:text-2xl mt-1 sm:mt-2">{badges[index].icon}</div>
                                                    </div>

                                                    {/* Podium - Responsive */}
                                                    <div className={`${heights[index]} w-16 sm:w-18 md:w-20 bg-gradient-to-t ${badges[index].color} rounded-t-lg flex flex-col justify-end p-1 sm:p-2 shadow-lg`}>
                                                        <div className="text-white text-center">
                                                            <div className="font-bold text-sm sm:text-base md:text-lg">{actualPosition + 1}</div>
                                                            <div className="text-xs hidden sm:block">{badges[index].title}</div>
                                                        </div>
                                                    </div>

                                                    {/* User Info - Responsive */}
                                                    <div className="mt-1 sm:mt-2">
                                                        <div className="font-bold text-xs sm:text-sm truncate w-16 sm:w-18 md:w-20">{user.userName}</div>
                                                        <div className="text-xs text-yellow-600 font-semibold">{user.totalPoints || 0} pts</div>
                                                        <div className={`text-xs font-bold mt-1 ${
                                                            user.subscriptionStatus === "active" ? 'text-green-600' :
                                                            user.subscriptionStatus === "free" ? 'text-blue-600' : 'text-red-600'
                                                        }`}>
                                                            {user.subscriptionStatus === "active" ? "Premium" :
                                                             user.subscriptionStatus === "free" ? "Free" : "Expired"}
                                                        </div>
                                                    </div>
                                                </motion.div>
                                            );
                                        })}
                                    </div>
                                </div>

                                {/* Leaderboard Content - Responsive */}
                                <div className="p-3 sm:p-4 md:p-6">
                                    <div className="space-y-3 sm:space-y-4">
                                        {(activeTab === "overall"
                                            ? rankingData
                                            : rankingData.filter(user => user.userClass === userData?.class)
                                        ).map((user, index) => {
                                            const isCurrentUser = user.userId.includes(userData?._id);
                                            const getRankBadge = (position) => {
                                                if (position === 0) return {
                                                    icon: "💎",
                                                    color: "text-blue-600",
                                                    bg: "bg-gradient-to-br from-blue-100 to-cyan-100",
                                                    border: "border-blue-300",
                                                    title: "Diamond",
                                                    glow: "shadow-blue-200"
                                                };
                                                if (position === 1) return {
                                                    icon: "🥈",
                                                    color: "text-gray-600",
                                                    bg: "bg-gradient-to-br from-gray-100 to-slate-100",
                                                    border: "border-gray-300",
                                                    title: "Silver",
                                                    glow: "shadow-gray-200"
                                                };
                                                if (position === 2) return {
                                                    icon: "🥉",
                                                    color: "text-amber-600",
                                                    bg: "bg-gradient-to-br from-amber-100 to-orange-100",
                                                    border: "border-amber-300",
                                                    title: "Bronze",
                                                    glow: "shadow-amber-200"
                                                };
                                                return {
                                                    icon: position + 1,
                                                    color: "text-gray-600",
                                                    bg: "bg-gray-50",
                                                    border: "border-gray-200",
                                                    title: `Rank ${position + 1}`,
                                                    glow: "shadow-gray-100"
                                                };
                                            };

                                            const rankInfo = getRankBadge(index);

                                            return (
                                                <motion.div
                                                    key={user.userId}
                                                    ref={isCurrentUser ? currentUserRef : null}
                                                    initial={{ opacity: 0, x: -20 }}
                                                    animate={{ opacity: 1, x: 0 }}
                                                    transition={{ delay: index * 0.1 }}
                                                    className={`flex items-center space-x-2 sm:space-x-3 md:space-x-4 p-3 sm:p-4 rounded-xl transition-all duration-200 ${
                                                        isCurrentUser
                                                            ? 'bg-gradient-to-r from-blue-50 to-purple-50 border-2 border-blue-300 shadow-lg ring-2 ring-blue-200 ring-opacity-50 transform scale-[1.02]'
                                                            : user.subscriptionStatus === "expired"
                                                            ? 'bg-gray-100 border border-gray-300 opacity-75'
                                                            : user.subscriptionStatus === "active"
                                                            ? 'bg-green-50 border border-green-200'
                                                            : 'bg-gray-50 hover:bg-gray-100'
                                                    }`}
                                                >
                                                    {/* Enhanced Rank Badge - Responsive */}
                                                    <div className={`relative w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 rounded-full flex items-center justify-center ${rankInfo.bg} ${rankInfo.border} border-2 ${rankInfo.glow} shadow-lg transition-all duration-300 hover:scale-110 flex-shrink-0`}>
                                                        {index < 3 ? (
                                                            <div className="text-center">
                                                                <div className="text-lg sm:text-xl md:text-2xl mb-0.5 sm:mb-1">{rankInfo.icon}</div>
                                                                <div className={`text-xs font-bold ${rankInfo.color} hidden sm:block`}>{rankInfo.title}</div>
                                                            </div>
                                                        ) : (
                                                            <div className="text-center">
                                                                <span className="font-black text-sm sm:text-base md:text-lg text-gray-700">{rankInfo.icon}</span>
                                                                <div className="text-xs font-semibold text-gray-500 hidden sm:block">Rank</div>
                                                            </div>
                                                        )}

                                                        {/* Glow effect for top 3 */}
                                                        {index < 3 && (
                                                            <div className={`absolute inset-0 rounded-full ${rankInfo.bg} opacity-50 blur-md -z-10 animate-pulse`}></div>
                                                        )}
                                                    </div>

                                                    {/* WhatsApp-style Profile Picture */}
                                                    <div className={`w-12 h-12 rounded-full p-0.5 flex items-center justify-center flex-shrink-0 ${
                                                        user.subscriptionStatus === "active"
                                                            ? 'bg-gradient-to-r from-green-400 to-green-600'
                                                            : user.subscriptionStatus === "free"
                                                            ? 'bg-gradient-to-r from-blue-400 to-blue-600'
                                                            : 'bg-gradient-to-r from-red-400 to-red-600'
                                                    }`}>
                                                        <div className="w-full h-full rounded-full overflow-hidden bg-gray-200 flex items-center justify-center">
                                                            {user.userPhoto ? (
                                                                <img
                                                                    src={user.userPhoto}
                                                                    alt="profile"
                                                                    className="w-full h-full object-cover"
                                                                    onError={(e) => { e.target.src = image }}
                                                                />
                                                            ) : (
                                                                <TbUser className="w-6 h-6 text-gray-400" />
                                                            )}
                                                        </div>
                                                    </div>

                                                    {/* Enhanced User Info - Responsive */}
                                                    <div className="flex-1 min-w-0">
                                                        <div className="flex flex-col sm:flex-row sm:items-center space-y-1 sm:space-y-0 sm:space-x-2 mb-2">
                                                            <h3 className={`font-bold text-sm sm:text-base md:text-lg truncate ${
                                                                isCurrentUser ? 'text-primary-900' : 'text-gray-900'
                                                            }`}>
                                                                {user.userName}
                                                            </h3>
                                                            <div className="flex items-center space-x-1 sm:space-x-2">
                                                                {isCurrentUser && (
                                                                    <span className="bg-gradient-to-r from-blue-500 to-purple-500 text-white text-xs px-2 py-1 rounded-full font-bold">You</span>
                                                                )}
                                                                {index < 3 && (
                                                                    <span className={`text-xs px-2 py-1 rounded-full font-bold hidden sm:inline ${
                                                                        index === 0 ? 'bg-blue-100 text-blue-800' :
                                                                        index === 1 ? 'bg-gray-100 text-gray-800' :
                                                                        'bg-amber-100 text-amber-800'
                                                                    }`}>
                                                                        {rankInfo.title} Champion
                                                                    </span>
                                                                )}
                                                                {/* Subscription Status Badge */}
                                                                <span className={`text-xs px-2 py-1 rounded-full font-bold ${
                                                                    user.subscriptionStatus === "active"
                                                                        ? 'bg-green-100 text-green-800'
                                                                        : user.subscriptionStatus === "free"
                                                                        ? 'bg-blue-100 text-blue-800'
                                                                        : 'bg-red-100 text-red-800'
                                                                }`}>
                                                                    {user.subscriptionStatus === "active" ? "Premium" :
                                                                     user.subscriptionStatus === "free" ? "Free" : "Expired"}
                                                                </span>
                                                            </div>
                                                        </div>

                                                        {/* Points and Stats - Responsive */}
                                                        <div className="flex flex-wrap items-center gap-2 sm:gap-3 md:gap-4 mb-2">
                                                            <div className="flex items-center space-x-1">
                                                                <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                                                                <span className="font-bold text-yellow-600 text-xs sm:text-sm">{user.totalPoints || 0} pts</span>
                                                            </div>
                                                            <div className="flex items-center space-x-1">
                                                                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                                                                <span className="font-semibold text-green-600 text-xs sm:text-sm">{user.passedExamsCount || 0} passed</span>
                                                            </div>
                                                            <div className="flex items-center space-x-1">
                                                                <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                                                                <span className="font-semibold text-blue-600 text-xs sm:text-sm">{user.quizzesTaken || 0} quizzes</span>
                                                            </div>
                                                        </div>

                                                        <div className="flex flex-col sm:flex-row sm:items-center space-y-1 sm:space-y-0 sm:space-x-4 text-xs sm:text-sm text-gray-600">
                                                            <div className="flex items-center space-x-1">
                                                                <TbSchool className="w-3 h-3 sm:w-4 sm:h-4" />
                                                                <span className="truncate">{user.userSchool || 'Not Enrolled'}</span>
                                                            </div>
                                                            <div className="flex items-center space-x-1">
                                                                <TbUsers className="w-4 h-4" />
                                                                <span>{user.userClass || 'Not Enrolled'}</span>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    {/* Score */}
                                                    <div className="text-right">
                                                        <div className={`text-2xl font-bold ${
                                                            isCurrentUser ? 'text-primary-600' : 'text-gray-900'
                                                        }`}>
                                                            {user.score}
                                                        </div>
                                                        <div className="text-xs text-gray-500">points</div>
                                                    </div>
                                                </motion.div>
                                            );
                                        })}
                                    </div>
                                </div>
                            </Card>
                        ) : (
                            <Card className="p-12 text-center">
                                <TbChartBar className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                                <h3 className="text-xl font-semibold text-gray-900 mb-2">No Rankings Yet</h3>
                                <p className="text-gray-600">
                                    Complete some quizzes to see your ranking on the leaderboard!
                                </p>
                            </Card>
                        )}
                    </motion.div>

                    {/* Floating Action Button - Find Me */}
                    {userData && (
                        <motion.button
                            initial={{ opacity: 0, scale: 0 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ delay: 1 }}
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                            onClick={scrollToCurrentUser}
                            className="fixed bottom-6 right-6 bg-gradient-to-r from-blue-500 to-purple-500 text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 z-50"
                            title="Find me in ranking"
                        >
                            <TbUser className="w-6 h-6" />
                        </motion.button>
                    )}

                </div>
            )}
        </div>
    );
}

export default Ranking;