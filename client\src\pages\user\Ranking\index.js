import React, { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { getAllReportsForRanking } from "../../../apicalls/reports";
import { getUserInfo } from "../../../apicalls/users";
import { message } from "antd";
import { useDispatch } from "react-redux";
import { HideLoading, ShowLoading } from "../../../redux/loaderSlice";
import UserRankingList from "../../../components/modern/UserRankingList";
import { TbTrophy, TbUsers, TbSchool } from "react-icons/tb";

const Ranking = () => {
    const [rankingData, setRankingData] = useState([]);
    const [userData, setUserData] = useState(null);
    const [isAdmin, setIsAdmin] = useState(false);
    const [activeTab, setActiveTab] = useState("overall"); // "overall" or "class"
    const [loading, setLoading] = useState(true);

    const dispatch = useDispatch();

    // Helper function to determine subscription status
    const getSubscriptionStatus = (user) => {
        if (!user.paymentRequired) {
            return 'free';
        }

        if (user.subscription && user.subscription.status === 'active') {
            if (user.subscription.paymentStatus === 'paid') {
                const endDate = new Date(user.subscription.endDate);
                const now = new Date();
                if (endDate > now) {
                    return 'active';
                }
            }
        }

        return 'expired';
    };

    const fetchReports = async () => {
        try {
            const response = await getAllReportsForRanking();
            if (response.success) {
                // Transform data for our new components
                const transformedData = response.data.map((item, index) => ({
                    userId: item.user._id,
                    _id: item.user._id,
                    name: item.user.name,
                    profilePicture: item.user.profilePicture || null,
                    subscriptionStatus: getSubscriptionStatus(item.user),
                    totalPoints: item.totalPoints || 0,
                    passedExamsCount: item.passedExamsCount || 0,
                    quizzesTaken: item.quizzesTaken || 0,
                    score: item.totalPoints || 0,
                    rank: index + 1,
                    userSchool: item.user.userSchool || 'Not Enrolled',
                    userClass: item.user.userClass || 'Not Enrolled'
                }));
                setRankingData(transformedData);
            } else {
                message.error(response.message);
            }
        } catch (error) {
            message.error(error.message);
        } finally {
            setLoading(false);
        }
    }


    const getUserData = async () => {
        try {
            const response = await getUserInfo();
            if (response.success) {
                if (response.data.isAdmin) {
                    setIsAdmin(true);
                } else {
                    setIsAdmin(false);
                    setUserData(response.data);
                    await fetchReports();
                    dispatch(HideLoading());
                }
            } else {
                message.error(response.message);
            }
        } catch (error) {
            message.error(error.message);
        }
    };

    useEffect(() => {
        if (localStorage.getItem("token")) {
            dispatch(ShowLoading());
            getUserData();
        }
    }, []);

    if (isAdmin) {
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="text-center">
                    <TbUsers className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                    <h2 className="text-2xl font-bold text-gray-900 mb-2">Admin Access</h2>
                    <p className="text-gray-600">Ranking is only available for students</p>
                </div>
            </div>
        );
    }

    if (loading) {
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p className="text-gray-600">Loading ranking data...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50">
            <div className="container mx-auto px-4 py-8">
                {/* Header */}
                <motion.div
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-center mb-8"
                >
                    <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full mb-4">
                        <TbTrophy className="w-8 h-8 text-white" />
                    </div>
                    <h1 className="text-4xl font-bold text-gray-900 mb-4">Student Leaderboard</h1>
                    <p className="text-xl text-gray-600">
                        See how you rank against other students
                    </p>
                </motion.div>

                {/* Tabs for Overall vs Class Ranking */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                    className="mb-8"
                >
                    <div className="bg-white rounded-xl p-2 shadow-sm border border-gray-200 max-w-md mx-auto">
                        <div className="flex gap-2">
                            <motion.button
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                                className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 flex-1 justify-center ${
                                    activeTab === "overall"
                                        ? 'bg-blue-600 text-white shadow-md'
                                        : 'text-gray-600 hover:bg-gray-100'
                                }`}
                                onClick={() => setActiveTab("overall")}
                            >
                                <TbUsers className="w-5 h-5" />
                                <span>Overall</span>
                            </motion.button>
                            <motion.button
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                                className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 flex-1 justify-center ${
                                    activeTab === "class"
                                        ? 'bg-blue-600 text-white shadow-md'
                                        : 'text-gray-600 hover:bg-gray-100'
                                }`}
                                onClick={() => setActiveTab("class")}
                            >
                                <TbSchool className="w-5 h-5" />
                                <span>Class</span>
                            </motion.button>
                        </div>
                    </div>
                </motion.div>

                {/* Modern Ranking List */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                >
                    <UserRankingList
                        users={rankingData}
                        currentUserId={userData?._id}
                        layout="horizontal"
                        size="medium"
                        showSearch={true}
                        showFilters={true}
                        showStats={true}
                    />
                </motion.div>
            </div>
        </div>
    );
};

export default Ranking;