import React, { useEffect, useState } from "react";
import './index.css'
import { motion, AnimatePresence } from "framer-motion";
import { getAllReportsForRanking } from "../../../apicalls/reports";
import { getUserInfo } from "../../../apicalls/users";
import { message } from "antd";
import PageTitle from "../../../components/PageTitle";
import { useDispatch } from "react-redux";
import { HideLoading, ShowLoading } from "../../../redux/loaderSlice";
import { Card, Button, Loading } from "../../../components/modern";
import image from '../../../assets/person.png';
import { IoPersonCircleOutline } from "react-icons/io5";
import {
  TbTrophy,
  TbMedal,
  TbCrown,
  TbUsers,
  TbSchool,
  TbStar,
  TbChartBar,
  TbUser,
  TbAward
} from "react-icons/tb";

const Ranking = () => {
    const [rankingData, setRankingData] = useState('');
    const [userRanking, setUserRanking] = useState('');
    const [userData, setUserData] = useState('');
    const [isAdmin, setIsAdmin] = useState(false);
    const [isMobile, setIsMobile] = useState(false);
    const [activeTab, setActiveTab] = useState("overall"); // "overall" or "class"


    const dispatch = useDispatch();

    const fetchReports = async () => {
        try {
            const response = await getAllReportsForRanking();
            if (response.success) {
                setRankingData(response.data);
            } else {
                message.error(response.message);
            }
        } catch (error) {
            message.error(error.message);
        }
    }


    const getUserData = async () => {
        try {
            const response = await getUserInfo();
            if (response.success) {
                if (response.data.isAdmin) {
                    setIsAdmin(true);
                } else {
                    setIsAdmin(false);
                    setUserData(response.data);
                    await fetchReports();
                    dispatch(HideLoading());
                }
            } else {
                message.error(response.message);
            }
        } catch (error) {
            message.error(error.message);
        }
    };

    useEffect(() => {
        if (window.innerWidth < 700) {
            setIsMobile(true);
        }
        else {
            setIsMobile(false);
        }
        if (localStorage.getItem("token")) {
            dispatch(ShowLoading());
            getUserData();
        }
    }, []);

    const getUserStats = () => {
        const Ranking = rankingData
            .map((user, index) => ({
                user,
                ranking: index + 1,
            }))
            .filter((item) => item.user.userId.includes(userData._id));
        setUserRanking(Ranking);
    }

    useEffect(() => {
        if (rankingData) {
            getUserStats();
        }
    }, [rankingData]);

    // Helper function to format user ID for mobile devices
    const formatMobileUserId = (userId) => {
        const prefix = userId.slice(0, 4);
        const suffix = userId.slice(-4);
        return `${prefix}.....${suffix}`;
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
            {!isAdmin && (
                <div className="container-modern py-8">
                    {/* Modern Header */}
                    <motion.div
                        initial={{ opacity: 0, y: -20 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="text-center mb-8"
                    >
                        <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full mb-4">
                            <TbTrophy className="w-8 h-8 text-white" />
                        </div>
                        <h1 className="heading-2 text-gradient mb-4">Leaderboard</h1>
                        <p className="text-xl text-gray-600">
                            See how you rank against other students
                        </p>
                    </motion.div>

                    {/* Modern Tabs */}
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.2 }}
                        className="mb-8"
                    >
                        <Card className="p-2">
                            <div className="flex gap-2">
                                <motion.button
                                    whileHover={{ scale: 1.02 }}
                                    whileTap={{ scale: 0.98 }}
                                    className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 flex-1 justify-center ${
                                        activeTab === "overall"
                                            ? 'bg-primary-600 text-white shadow-md'
                                            : 'text-gray-600 hover:bg-gray-100'
                                    }`}
                                    onClick={() => setActiveTab("overall")}
                                >
                                    <TbUsers className="w-5 h-5" />
                                    <span>Overall Ranking</span>
                                    {activeTab === "overall" && (
                                        <motion.div
                                            layoutId="activeRankingTab"
                                            className="w-2 h-2 bg-white rounded-full"
                                        />
                                    )}
                                </motion.button>
                                <motion.button
                                    whileHover={{ scale: 1.02 }}
                                    whileTap={{ scale: 0.98 }}
                                    className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 flex-1 justify-center ${
                                        activeTab === "class"
                                            ? 'bg-primary-600 text-white shadow-md'
                                            : 'text-gray-600 hover:bg-gray-100'
                                    }`}
                                    onClick={() => setActiveTab("class")}
                                >
                                    <TbSchool className="w-5 h-5" />
                                    <span>Class Ranking</span>
                                    {activeTab === "class" && (
                                        <motion.div
                                            layoutId="activeRankingTab"
                                            className="w-2 h-2 bg-white rounded-full"
                                        />
                                    )}
                                </motion.button>
                            </div>
                        </Card>
                    </motion.div>

                    {/* Modern Leaderboard */}
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.4 }}
                    >
                        {rankingData.length > 0 ? (
                            <Card className="overflow-hidden">
                                {/* Enhanced Leaderboard Header */}
                                <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 text-white p-6 relative overflow-hidden">
                                    {/* Background Pattern */}
                                    <div className="absolute inset-0 opacity-10">
                                        <div className="absolute top-4 left-4 text-6xl">🏆</div>
                                        <div className="absolute top-8 right-8 text-4xl">⭐</div>
                                        <div className="absolute bottom-4 left-1/4 text-3xl">🎯</div>
                                        <div className="absolute bottom-6 right-1/3 text-5xl">💎</div>
                                    </div>

                                    <div className="relative z-10">
                                        <div className="flex items-center justify-center space-x-3 mb-2">
                                            <TbTrophy className="w-10 h-10 text-yellow-300 animate-bounce" />
                                            <h2 className="text-3xl font-black bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent">
                                                {activeTab === "overall" ? "Global Leaderboard" : "Class Champions"}
                                            </h2>
                                            <TbTrophy className="w-10 h-10 text-yellow-300 animate-bounce" style={{ animationDelay: '0.5s' }} />
                                        </div>
                                        <p className="text-center text-blue-100 text-lg font-semibold">
                                            {activeTab === "overall"
                                                ? "🌟 Elite performers from all classes competing for glory! 🌟"
                                                : `🎓 Class ${userData?.class || 'your class'} top achievers! 🎓`
                                            }
                                        </p>
                                        <div className="text-center mt-3">
                                            <span className="bg-white/20 px-4 py-2 rounded-full text-sm font-bold">
                                                💪 Earn points by acing quizzes • 🏅 Climb the ranks • 👑 Become a champion!
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                {/* Top 3 Podium */}
                                <div className="bg-gradient-to-br from-blue-50 to-purple-50 p-6 border-b">
                                    <h3 className="text-xl font-bold text-center mb-6 text-gray-800">🏆 Champions Podium 🏆</h3>
                                    <div className="flex justify-center items-end space-x-4">
                                        {(activeTab === "overall"
                                            ? rankingData.slice(0, 3)
                                            : rankingData.filter(user => user.userClass === userData?.class).slice(0, 3)
                                        ).map((user, index) => {
                                            const positions = [1, 0, 2]; // Silver, Diamond, Bronze order for visual appeal
                                            const actualPosition = positions.indexOf(index);
                                            const heights = ['h-24', 'h-32', 'h-20']; // Different heights for podium effect
                                            const badges = [
                                                { icon: "🥈", color: "from-gray-400 to-gray-600", title: "Silver" },
                                                { icon: "💎", color: "from-blue-400 to-cyan-600", title: "Diamond" },
                                                { icon: "🥉", color: "from-amber-400 to-orange-600", title: "Bronze" }
                                            ];

                                            return (
                                                <motion.div
                                                    key={user.userId}
                                                    initial={{ opacity: 0, y: 50 }}
                                                    animate={{ opacity: 1, y: 0 }}
                                                    transition={{ delay: index * 0.2 }}
                                                    className="text-center"
                                                >
                                                    {/* User Avatar */}
                                                    <div className="mb-3">
                                                        <div className="w-16 h-16 mx-auto rounded-full overflow-hidden border-4 border-white shadow-lg">
                                                            {user.userPhoto ? (
                                                                <img src={user.userPhoto} alt="profile" className="w-full h-full object-cover" />
                                                            ) : (
                                                                <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                                                                    <TbUser className="w-8 h-8 text-gray-400" />
                                                                </div>
                                                            )}
                                                        </div>
                                                        <div className="text-2xl mt-2">{badges[index].icon}</div>
                                                    </div>

                                                    {/* Podium */}
                                                    <div className={`${heights[index]} w-20 bg-gradient-to-t ${badges[index].color} rounded-t-lg flex flex-col justify-end p-2 shadow-lg`}>
                                                        <div className="text-white text-center">
                                                            <div className="font-bold text-lg">{actualPosition + 1}</div>
                                                            <div className="text-xs">{badges[index].title}</div>
                                                        </div>
                                                    </div>

                                                    {/* User Info */}
                                                    <div className="mt-2">
                                                        <div className="font-bold text-sm truncate w-20">{user.userName}</div>
                                                        <div className="text-xs text-yellow-600 font-semibold">{user.totalPoints || 0} pts</div>
                                                    </div>
                                                </motion.div>
                                            );
                                        })}
                                    </div>
                                </div>

                                {/* Leaderboard Content */}
                                <div className="p-6">
                                    <div className="space-y-4">
                                        {(activeTab === "overall"
                                            ? rankingData
                                            : rankingData.filter(user => user.userClass === userData?.class)
                                        ).map((user, index) => {
                                            const isCurrentUser = user.userId.includes(userData?._id);
                                            const getRankBadge = (position) => {
                                                if (position === 0) return {
                                                    icon: "💎",
                                                    color: "text-blue-600",
                                                    bg: "bg-gradient-to-br from-blue-100 to-cyan-100",
                                                    border: "border-blue-300",
                                                    title: "Diamond",
                                                    glow: "shadow-blue-200"
                                                };
                                                if (position === 1) return {
                                                    icon: "🥈",
                                                    color: "text-gray-600",
                                                    bg: "bg-gradient-to-br from-gray-100 to-slate-100",
                                                    border: "border-gray-300",
                                                    title: "Silver",
                                                    glow: "shadow-gray-200"
                                                };
                                                if (position === 2) return {
                                                    icon: "🥉",
                                                    color: "text-amber-600",
                                                    bg: "bg-gradient-to-br from-amber-100 to-orange-100",
                                                    border: "border-amber-300",
                                                    title: "Bronze",
                                                    glow: "shadow-amber-200"
                                                };
                                                return {
                                                    icon: position + 1,
                                                    color: "text-gray-600",
                                                    bg: "bg-gray-50",
                                                    border: "border-gray-200",
                                                    title: `Rank ${position + 1}`,
                                                    glow: "shadow-gray-100"
                                                };
                                            };

                                            const rankInfo = getRankBadge(index);

                                            return (
                                                <motion.div
                                                    key={user.userId}
                                                    initial={{ opacity: 0, x: -20 }}
                                                    animate={{ opacity: 1, x: 0 }}
                                                    transition={{ delay: index * 0.1 }}
                                                    className={`flex items-center space-x-4 p-4 rounded-xl transition-all duration-200 ${
                                                        isCurrentUser
                                                            ? 'bg-primary-50 border-2 border-primary-200 shadow-md'
                                                            : 'bg-gray-50 hover:bg-gray-100'
                                                    }`}
                                                >
                                                    {/* Enhanced Rank Badge */}
                                                    <div className={`relative w-16 h-16 rounded-full flex items-center justify-center ${rankInfo.bg} ${rankInfo.border} border-2 ${rankInfo.glow} shadow-lg transition-all duration-300 hover:scale-110`}>
                                                        {index < 3 ? (
                                                            <div className="text-center">
                                                                <div className="text-2xl mb-1">{rankInfo.icon}</div>
                                                                <div className={`text-xs font-bold ${rankInfo.color}`}>{rankInfo.title}</div>
                                                            </div>
                                                        ) : (
                                                            <div className="text-center">
                                                                <span className="font-black text-lg text-gray-700">{rankInfo.icon}</span>
                                                                <div className="text-xs font-semibold text-gray-500">Rank</div>
                                                            </div>
                                                        )}

                                                        {/* Glow effect for top 3 */}
                                                        {index < 3 && (
                                                            <div className={`absolute inset-0 rounded-full ${rankInfo.bg} opacity-50 blur-md -z-10 animate-pulse`}></div>
                                                        )}
                                                    </div>

                                                    {/* Profile Picture */}
                                                    <div className="w-12 h-12 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center">
                                                        {user.userPhoto ? (
                                                            <img
                                                                src={user.userPhoto}
                                                                alt="profile"
                                                                className="w-full h-full object-cover"
                                                                onError={(e) => { e.target.src = image }}
                                                            />
                                                        ) : (
                                                            <TbUser className="w-6 h-6 text-gray-400" />
                                                        )}
                                                    </div>

                                                    {/* Enhanced User Info */}
                                                    <div className="flex-1 min-w-0">
                                                        <div className="flex items-center space-x-2 mb-2">
                                                            <h3 className={`font-bold text-lg truncate ${
                                                                isCurrentUser ? 'text-primary-900' : 'text-gray-900'
                                                            }`}>
                                                                {user.userName}
                                                            </h3>
                                                            {isCurrentUser && (
                                                                <span className="bg-gradient-to-r from-blue-500 to-purple-500 text-white text-xs px-2 py-1 rounded-full font-bold">You</span>
                                                            )}
                                                            {index < 3 && (
                                                                <span className={`text-xs px-2 py-1 rounded-full font-bold ${
                                                                    index === 0 ? 'bg-blue-100 text-blue-800' :
                                                                    index === 1 ? 'bg-gray-100 text-gray-800' :
                                                                    'bg-amber-100 text-amber-800'
                                                                }`}>
                                                                    {rankInfo.title} Champion
                                                                </span>
                                                            )}
                                                        </div>

                                                        {/* Points and Stats */}
                                                        <div className="flex items-center space-x-4 mb-2">
                                                            <div className="flex items-center space-x-1">
                                                                <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                                                                <span className="font-bold text-yellow-600">{user.totalPoints || 0} pts</span>
                                                            </div>
                                                            <div className="flex items-center space-x-1">
                                                                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                                                                <span className="font-semibold text-green-600">{user.passedExamsCount || 0} passed</span>
                                                            </div>
                                                            <div className="flex items-center space-x-1">
                                                                <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                                                                <span className="font-semibold text-blue-600">{user.quizzesTaken || 0} quizzes</span>
                                                            </div>
                                                        </div>

                                                        <div className="flex items-center space-x-4 text-sm text-gray-600">
                                                            <div className="flex items-center space-x-1">
                                                                <TbSchool className="w-4 h-4" />
                                                                <span className="truncate">{user.userSchool || 'Not Enrolled'}</span>
                                                            </div>
                                                            <div className="flex items-center space-x-1">
                                                                <TbUsers className="w-4 h-4" />
                                                                <span>{user.userClass || 'Not Enrolled'}</span>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    {/* Score */}
                                                    <div className="text-right">
                                                        <div className={`text-2xl font-bold ${
                                                            isCurrentUser ? 'text-primary-600' : 'text-gray-900'
                                                        }`}>
                                                            {user.score}
                                                        </div>
                                                        <div className="text-xs text-gray-500">points</div>
                                                    </div>
                                                </motion.div>
                                            );
                                        })}
                                    </div>
                                </div>
                            </Card>
                        ) : (
                            <Card className="p-12 text-center">
                                <TbChartBar className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                                <h3 className="text-xl font-semibold text-gray-900 mb-2">No Rankings Yet</h3>
                                <p className="text-gray-600">
                                    Complete some quizzes to see your ranking on the leaderboard!
                                </p>
                            </Card>
                        )}
                    </motion.div>

                </div>
            )}
        </div>
    );
}

export default Ranking;